#!/usr/bin/env python3
"""
性能基准测试工具
对比优化前后的系统性能
"""
import asyncio
import json
import time
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Any
import statistics

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from optimized_fault_diagnosis import OptimizedFaultDiagnosisSystem


class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        self.results = []
    
    async def run_single_benchmark(self, test_case: Dict[str, Any], run_id: int) -> Dict[str, Any]:
        """运行单次基准测试"""
        print(f"🧪 运行测试 {run_id}: {test_case.get('uuid', 'unknown')}")
        
        system = OptimizedFaultDiagnosisSystem()
        task_str = json.dumps(test_case, ensure_ascii=False, indent=2)
        
        start_time = time.time()
        try:
            result = await system.diagnose_single_case(task_str)
            end_time = time.time()
            
            duration = end_time - start_time
            success = "error" not in result
            
            benchmark_result = {
                "run_id": run_id,
                "case_uuid": test_case.get("uuid", "unknown"),
                "duration": duration,
                "success": success,
                "result": result,
                "timestamp": start_time
            }
            
            if success:
                print(f"  ✅ 成功 - {duration:.2f}秒")
            else:
                print(f"  ❌ 失败 - {duration:.2f}秒: {result.get('error', '未知错误')}")
            
            await system.close()
            return benchmark_result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            benchmark_result = {
                "run_id": run_id,
                "case_uuid": test_case.get("uuid", "unknown"),
                "duration": duration,
                "success": False,
                "result": {"error": str(e)},
                "timestamp": start_time
            }
            
            print(f"  ❌ 异常 - {duration:.2f}秒: {e}")
            await system.close()
            return benchmark_result
    
    async def run_benchmark_suite(
        self, 
        test_cases: List[Dict[str, Any]], 
        runs_per_case: int = 3
    ) -> Dict[str, Any]:
        """运行完整的基准测试套件"""
        print("🚀 开始性能基准测试")
        print("=" * 60)
        print(f"📋 测试案例: {len(test_cases)}")
        print(f"🔄 每案例运行次数: {runs_per_case}")
        print(f"📊 总测试次数: {len(test_cases) * runs_per_case}")
        print()
        
        all_results = []
        
        for case_idx, test_case in enumerate(test_cases):
            print(f"📝 测试案例 {case_idx + 1}/{len(test_cases)}: {test_case.get('uuid', 'unknown')}")
            
            case_results = []
            for run in range(runs_per_case):
                result = await self.run_single_benchmark(test_case, run + 1)
                case_results.append(result)
                all_results.append(result)
                
                # 避免API限流
                if run < runs_per_case - 1:
                    await asyncio.sleep(1)
            
            # 计算该案例的统计信息
            successful_runs = [r for r in case_results if r["success"]]
            if successful_runs:
                durations = [r["duration"] for r in successful_runs]
                avg_duration = statistics.mean(durations)
                min_duration = min(durations)
                max_duration = max(durations)
                
                print(f"  📊 案例统计: 平均{avg_duration:.2f}s, 最快{min_duration:.2f}s, 最慢{max_duration:.2f}s")
            else:
                print(f"  ❌ 该案例所有运行都失败")
            
            print()
        
        # 计算总体统计
        return self._calculate_statistics(all_results)
    
    def _calculate_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算统计信息"""
        successful_results = [r for r in results if r["success"]]
        failed_results = [r for r in results if not r["success"]]
        
        stats = {
            "total_runs": len(results),
            "successful_runs": len(successful_results),
            "failed_runs": len(failed_results),
            "success_rate": len(successful_results) / len(results) * 100 if results else 0,
            "results": results
        }
        
        if successful_results:
            durations = [r["duration"] for r in successful_results]
            stats.update({
                "avg_duration": statistics.mean(durations),
                "median_duration": statistics.median(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "std_duration": statistics.stdev(durations) if len(durations) > 1 else 0
            })
        
        return stats
    
    def print_benchmark_report(self, stats: Dict[str, Any]):
        """打印基准测试报告"""
        print("📊 性能基准测试报告")
        print("=" * 60)
        
        print(f"📋 总运行次数: {stats['total_runs']}")
        print(f"✅ 成功次数: {stats['successful_runs']}")
        print(f"❌ 失败次数: {stats['failed_runs']}")
        print(f"🎯 成功率: {stats['success_rate']:.1f}%")
        
        if stats['successful_runs'] > 0:
            print(f"\n⏱️ 性能指标:")
            print(f"  平均耗时: {stats['avg_duration']:.2f}秒")
            print(f"  中位数耗时: {stats['median_duration']:.2f}秒")
            print(f"  最快耗时: {stats['min_duration']:.2f}秒")
            print(f"  最慢耗时: {stats['max_duration']:.2f}秒")
            print(f"  标准差: {stats['std_duration']:.2f}秒")
            
            # 性能等级评估
            avg_time = stats['avg_duration']
            if avg_time < 30:
                grade = "🚀 优秀"
            elif avg_time < 60:
                grade = "✅ 良好"
            elif avg_time < 120:
                grade = "⚠️ 一般"
            else:
                grade = "❌ 需要优化"
            
            print(f"  性能等级: {grade}")
        
        print()


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="性能基准测试")
    parser.add_argument("-i", "--input", required=True, help="测试案例JSON文件")
    parser.add_argument("-r", "--runs", type=int, default=3, help="每案例运行次数")
    parser.add_argument("-o", "--output", help="保存结果到文件")
    
    args = parser.parse_args()
    
    # 读取测试案例
    try:
        with open(args.input, 'r', encoding='utf-8') as f:
            test_cases = json.load(f)
        print(f"✅ 加载了 {len(test_cases)} 个测试案例")
    except Exception as e:
        print(f"❌ 读取测试文件失败: {e}")
        return
    
    # 运行基准测试
    benchmark = PerformanceBenchmark()
    stats = await benchmark.run_benchmark_suite(test_cases, args.runs)
    
    # 打印报告
    benchmark.print_benchmark_report(stats)
    
    # 保存结果
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            print(f"📁 基准测试结果已保存到: {args.output}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
