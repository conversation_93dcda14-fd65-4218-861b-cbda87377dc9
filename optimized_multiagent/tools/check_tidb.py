#!/usr/bin/python3
# 判断tidb的相关详细规则
# 增加tidb的异常指标

import pytz
from datetime import datetime
import pandas as pd
import glob
import os
import json

def format_time_to_shanghai(time_str):
    # 解析输入的UTC时间字符串
    dt_utc = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%SZ")
    # 设置为UTC时区
    dt_utc = dt_utc.replace(tzinfo=pytz.UTC)
    # 转换为上海时区
    dt_shanghai = dt_utc.astimezone(pytz.timezone("Asia/Shanghai"))
    # 格式化输出为 年-月-日_小时
    return dt_shanghai.strftime("%Y-%m-%d")

def check_tidb_interrupt(
    start_time: str,
    end_time: str,
    time_diff_threads: int = 300
):
    """增加pod变化worker节点指标"""

    results = []
    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    # print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"./data/{date_str}/metric-parquet/infra/infra_tidb/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_tidb_cpu_usage_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据，前后start end时间各增加5分钟
    # 增加10分钟是为了防止出现1s的情况
    start_dt_adj = start_dt - pd.Timedelta(minutes=5)
    end_dt_adj = end_dt + pd.Timedelta(minutes=5)
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 读取cpu_usage的value，判断按照time时序是否发生大量断点
    if not filtered_df.empty and 'cpu_usage' in filtered_df.columns and 'time' in filtered_df.columns:
        # 先按pod分组
        interrupt_result = []
        for pod_name, pod_df in filtered_df.groupby('object_type'):
            pod_df = pod_df.sort_values('time')
            # 计算相邻时间点的间隔（秒）
            time_diffs = pod_df['time'].diff().dt.total_seconds().dropna()
            # 统计大于300秒（5分钟）的断点数量
            interrupt_count = (time_diffs > time_diff_threads).sum()
            # 记录断点数量
            interrupt_result.append({
                'object': 'tidb_tidb',
                'interrupt_count': int(interrupt_count)
            })
        # 转为json返回
        result_json = json.dumps(interrupt_result, ensure_ascii=False)
        return result_json
    else:
        print("数据中缺少cpu_usage或time字段，无法判断断点。")
        return []

def check_tikv_interrupt(
    start_time: str,
    end_time: str,
    time_diff_threads: int = 300
):
    """增加pod变化worker节点指标"""

    results = []
    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    # print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"./data/{date_str}/metric-parquet/other/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_tikv_cpu_usage_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据，前后start end时间各增加5分钟
    # 增加10分钟是为了防止出现1s的情况
    start_dt_adj = start_dt - pd.Timedelta(minutes=5)
    end_dt_adj = end_dt + pd.Timedelta(minutes=5)
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 读取cpu_usage的value，判断按照time时序是否发生大量断点
    if not filtered_df.empty and 'cpu_usage' in filtered_df.columns and 'time' in filtered_df.columns:
        # 先按pod分组
        interrupt_result = []
        for pod_name, pod_df in filtered_df.groupby('object_type'):
            pod_df = pod_df.sort_values('time')
            # 计算相邻时间点的间隔（秒）
            time_diffs = pod_df['time'].diff().dt.total_seconds().dropna()
            interrupt_count = (time_diffs > time_diff_threads).sum()
            # 记录断点数量
            interrupt_result.append({
                'object': "tidb_tikv",
                'interrupt_count': int(interrupt_count)
            })
        # 转为json返回
        result_json = json.dumps(interrupt_result, ensure_ascii=False)
        return result_json
    else:
        print("数据中缺少cpu_usage或time字段，无法判断断点。")
        return []

def check_pd_interrupt(
    start_time: str,
    end_time: str,
    time_diff_threads: int = 300
):
    """增加pod变化worker节点指标"""

    results = []
    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    # print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"./data/{date_str}/metric-parquet/other/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_pd_cpu_usage_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据，前后start end时间各增加5分钟
    # 增加10分钟是为了防止出现1s的情况
    start_dt_adj = start_dt - pd.Timedelta(minutes=5)
    end_dt_adj = end_dt + pd.Timedelta(minutes=5)
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 这个判断用于检查filtered_df（过滤后的DataFrame）是否为空，如果不为空则返回True，表示有数据；为空则返回False，表示没有数据
    if not filtered_df.empty and 'cpu_usage' in filtered_df.columns and 'time' in filtered_df.columns:
        # 先按pod分组
        interrupt_result = []
        for pod_name, pod_df in filtered_df.groupby('object_type'):
            pod_df = pod_df.sort_values('time')
            # 计算相邻时间点的间隔（秒）
            time_diffs = pod_df['time'].diff().dt.total_seconds().dropna()
            interrupt_count = (time_diffs > time_diff_threads).sum()
            # 记录断点数量
            interrupt_result.append({
                'object': 'tidb-pd',
                'interrupt_count': int(interrupt_count)
            })

        result_json = json.dumps(interrupt_result, ensure_ascii=False)
        return result_json
    else:
        print("数据中缺少cpu_usage或time字段，无法判断断点。")
        return []
    
def main():

    import time 
    process_start_time = time.time()
    start_time = '2025-06-16T16:10:02Z'
    end_time = '2025-06-16T16:26:02Z'
    # check_metric_tidb(start_time=start_time,
    #           end_time=end_time)
    # check_metric_infra(start_time=start_time,
    #           end_time=end_time)
    # result = check_tikv_io_util(start_time=start_time,
    #           end_time=end_time)

      
    result = check_tidb_interrupt(
        start_time=start_time,
        end_time=end_time
    )
    result = check_tikv_interrupt(
        start_time=start_time,
        end_time=end_time
    )
    result = check_pd_interrupt(
        start_time=start_time,
        end_time=end_time
    )
    print(result)
    # for item in result:
    #     if 'entity_name' in item and 'recommendationservice' in item['entity_name'] and item['kpi'] == 'rrt':
    #         print(item)
    
    # for item in result:
    #     if item['max'] > 50:
    #         print(item)

    process_end_time = time.time()
    process_time = process_end_time - process_start_time
    print(f"日志处理结束，处理时间{process_time}s")
    

if __name__ == "__main__":
    main()
   
