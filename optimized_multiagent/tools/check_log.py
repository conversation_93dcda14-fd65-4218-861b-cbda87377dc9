#!/usr/bin/python3
# 根据输入的时间，返回过滤去重后的日志信息
# 增加白名单功能，对部分白名单不进行处理
# 增加读取预处理数据功能，加快case处理效率

import pyarrow.parquet as pq
import pandas as pd
import os
import json
import time
import sys
from typing import List
from datetime import datetime
import pytz

# 添加配置路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.data_config import get_log_file_path

default_filter_logs_number = 100
default_filter_words = [
    "error", "fail", "exception", "warn", "critical", "panic", "abort", "fatal",
    "refused", "denied", "timeout", "unavailable", "disconnect", "lost", "crash",
    "not found", "unreachable", "rejected", "broken", "corrupt", "invalid", "overflow",
    "deadlock", "hang", "assert", "traceback", "stacktrace", "segfault", "oom", "out of memory"
]

white_list_logs = [
    '100 changes in 300 seconds'
]

def format_time_to_shanghai(time_str):
    # 解析输入的UTC时间字符串
    dt_utc = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%SZ")
    # 设置为UTC时区
    dt_utc = dt_utc.replace(tzinfo=pytz.UTC)
    # 转换为上海时区
    dt_shanghai = dt_utc.astimezone(pytz.timezone("Asia/Shanghai"))
    # 格式化输出为 年-月-日_小时
    return dt_shanghai.strftime("%Y-%m-%d_%H")


import concurrent.futures

def process_chunk(chunk_df, start_time_ts, end_time_ts, filter_words):
    anomaly_logs = []
    for idx, row in chunk_df.iterrows():
        ts_raw = row.get('@timestamp')
        if isinstance(ts_raw, (int, float)):
            timestamp = int(ts_raw)
        else:
            try:
                timestamp = int(pd.to_datetime(ts_raw).timestamp() * 1000)
            except Exception:
                continue

        if timestamp < start_time_ts:
            continue
        if timestamp > end_time_ts:
            break

        row_text = str(row.get('message', '')) if isinstance(row.get('message', ''), str) else ''
        if any(keyword in row_text for keyword in filter_words):
            log_entry = dict(row)
            anomaly_logs.append(log_entry)
    return anomaly_logs

def check_log(
    start_time: str,
    end_time: str,
    filter_words: List[str] = default_filter_words,
    preprocessed_log_file: str = None  # 新增参数：可选，预处理log数据文件路径
) -> List[dict]:
    """
    根据输入的时间，查询日志
    功能增强：
    1. 如果定义了清洗的日志文件（preprocessed_log_file参数不为None），则直接读取该清洗后的日志文件进行处理。
    2. 如果未定义清洗的日志文件，则按照日期规则定位原始日志文件并进行清洗处理。
    3. 根据开始时间和结束时间，以及过滤规则，过滤需要过滤的日志信息。
    4. 对日志进行去重处理。
    5. 并发处理，每10000条切分，最多16个并发。
    """
    if preprocessed_log_file:
        if not os.path.exists(preprocessed_log_file):
            print(f"当前预处理文件不存在，通过读取原始文件进行处理")
        else:
            # 读取预处理后的json文件
            import json
            with open(preprocessed_log_file,'r') as f:
                return json.loads(f.read())
            
    start_time_ts = int(pd.to_datetime(start_time).timestamp() * 1000)
    end_time_ts = int(pd.to_datetime(end_time).timestamp() * 1000)

    filedate = format_time_to_shanghai(start_time).split('_')[0]
    hour_str = format_time_to_shanghai(start_time).split('_')[1]
    filename = get_log_file_path(filedate, hour_str)
    if not os.path.exists(filename):
        print(f"文件{filename}不存在")
        return None

    print(f"[1/5] 正在读取 parquet 文件: {filename}")
    df = pq.read_table(filename).to_pandas()
    df = df.sort_values("@timestamp")
    print(f"[2/5] parquet 文件读取完成，共 {len(df)} 行")
    print(f"[3/5] 开始并发处理数据...")

    # 预处理：将所有字段名和内容转为小写
    df_lower = df.map(lambda x: str(x).lower() if isinstance(x, str) else x)
    df_lower.columns = [str(col).lower() for col in df_lower.columns]

    chunk_size = 10000
    total_rows = len(df_lower)
    chunks = [df_lower.iloc[i:i+chunk_size] for i in range(0, total_rows, chunk_size)]

    anomaly_logs = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
        futures = []
        for i, chunk in enumerate(chunks):
            futures.append(executor.submit(process_chunk, chunk, start_time_ts, end_time_ts, filter_words))
        for i, future in enumerate(concurrent.futures.as_completed(futures)):
            result = future.result()
            anomaly_logs.extend(result)
            # print(f"[进度] 并发分块处理完成 {i+1}/{len(chunks)} 块，累计异常日志 {len(anomaly_logs)} 条")

    print("[4/5] 全部数据处理完毕！")

    # 对 anomaly_logs 进行去重，如果 message 内容相同，只保留一条，并记录去重前后的数量
    anomaly_logs_count = len(anomaly_logs)
    unique_logs = {}
    for log in anomaly_logs:
        k8_pod = log.get('k8_pod','')
        tmp_message = log.get('message', '')

        # 如果 tmp_message 包含白名单中的任意字符串，则跳过该日志
        if any(w in tmp_message for w in white_list_logs):
            continue

        # 如果是front，按照error,http.req.path,message进行去重
        if 'front' in log.get('k8_pod', ''):
            try:
                tmp_message_json = json.loads(tmp_message)
                msg = f"{tmp_message_json.get('error','')}-{tmp_message_json.get('http.req.path','')}-{tmp_message_json.get('message','')}"
            except Exception:
                msg = f"{tmp_message}"
        else:
            msg = tmp_message

        if msg not in unique_logs:
            unique_logs[msg] = log

    anomaly_logs_dedup = list(unique_logs.values())
    anomaly_logs_dedup_count = len(anomaly_logs_dedup)
    print(f"[5/5] 数据去重完成，去重前: {anomaly_logs_count}，去重后日志数量: {anomaly_logs_dedup_count}")

    if len(anomaly_logs_dedup) > default_filter_logs_number:
        print(f"[信息] 去重后日志数量较大，进行数据清洗")
        
        filter_front_logs = []
        filter_front_logs_msg = {}
        for item in anomaly_logs_dedup:
            # 不处理front日志
            if 'front' in item['k8_pod']:
                continue

            # 压缩redis日志
            if 'redistimeoutexception' in item['message']:
                tmp_message = f"{item['k8_pod']}-redistimeoutexception"

            if tmp_message not in filter_front_logs_msg:
                filter_front_logs_msg[tmp_message] = item

        filter_front_logs = list(filter_front_logs_msg.values())
        for item in filter_front_logs:
            print(item)

        anomaly_logs_dedup = filter_front_logs
        print(f"[信息] 数据清洗完成，日志数据共{len(anomaly_logs_dedup)}条")

    return anomaly_logs_dedup

def main():

    process_start_time = time.time()
    start_time = '2025-06-16T16:10:02Z'
    end_time = '2025-06-16T16:26:02Z'

    # for timerange in time_lists:
    result = check_log(
        start_time=start_time,
        end_time=end_time
    )
    print(result)
    # for log in result:
    #     print(log)
    process_end_time = time.time()
    process_time = process_end_time - process_start_time
    print(f"日志处理结束，处理时间{process_time}s")

if __name__ == "__main__":
    
    main()

