#!/usr/bin/python3
# 从输入的文字内提取时间，并且返回需要查询的时间段

import re

def time_analyzer(message: None) -> list:
    
    times = []
    message = message.strip().split(' ')
    for item in message:
        item = item.replace('.','')
        pattern = r'^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\d|3[01])T(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\dZ$'
        if re.match(pattern, item):
            times.append(item)
    
    if len(times) < 2:
        return []

    else:
        return times
    

# 调用 time_analyzer 函数示例
if __name__ == "__main__":

    message = "The system experienced an anomaly from 2025-06-05T16:10:02Z to 2025-06-05T16:31:02Z. Please infer the possible cause."
    result = time_analyzer(message)
    print(result)


