#!/usr/bin/python3
# 根据输入的时间，返回故障时间范围内的trace信息
# 过滤信息1： status_code有异常的trace
# 过滤信息2： duration 超过 P95的trace
# 所有trace只返回最有一个span
# 默认返回异常流量

import pyarrow.parquet as pq
import numpy as np
import pandas as pd
import os
import time
from datetime import datetime
import concurrent.futures
import pytz
import ast

def format_time_to_shanghai(time_str):
    # 解析输入的UTC时间字符串
    dt_utc = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%SZ")
    # 设置为UTC时区
    dt_utc = dt_utc.replace(tzinfo=pytz.UTC)
    # 转换为上海时区
    dt_shanghai = dt_utc.astimezone(pytz.timezone("Asia/Shanghai"))
    # 格式化输出为 年-月-日_小时
    return dt_shanghai.strftime("%Y-%m-%d_%H")

def convert_ndarray(obj):
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: convert_ndarray(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_ndarray(i) for i in obj]
    else:
        return obj

def parse_tags(tags):
    # 处理tags为dict、list或numpy.ndarray的情况
    tag_dict = {}

    # 统一处理tags
    if isinstance(tags, dict):
        # dict类型
        tag_dict = tags
    elif isinstance(tags, list):
        # list类型
        for tag in tags:
            if isinstance(tag, dict) and 'key' in tag and 'value' in tag:
                tag_dict[tag['key']] = tag['value']
    elif isinstance(tags, np.ndarray):
        # numpy.ndarray类型，尝试转为list处理
        for tag in tags.tolist():
            if isinstance(tag, dict) and 'key' in tag and 'value' in tag:
                tag_dict[tag['key']] = tag['value']
    
    return tag_dict

def parse_logs(logs):
    # logs 可能是字符串、list、ndarray
    if isinstance(logs, str):
        try:
            logs = ast.literal_eval(logs)
        except Exception:
            return []
    if isinstance(logs, np.ndarray):
        logs = logs.tolist()
    if not isinstance(logs, list):
        return []
    result = []
    for log in logs:
        if isinstance(log, dict):
            # fields 可能是ndarray
            if 'fields' in log and isinstance(log['fields'], np.ndarray):
                log['fields'] = log['fields'].tolist()
            result.append(log)
        elif isinstance(log, str):
            try:
                log_obj = ast.literal_eval(log)
                if isinstance(log_obj, dict):
                    if 'fields' in log_obj and isinstance(log_obj['fields'], np.ndarray):
                        log_obj['fields'] = log_obj['fields'].tolist()
                    result.append(log_obj)
            except Exception:
                continue
    return result

def parse_process(process):
    # process 可能是字符串或dict
    if isinstance(process, str):
        try:
            process = ast.literal_eval(process)
        except Exception:
            return {"serviceName": "unknown", "tags": []}
    if not isinstance(process, dict):
        return {"serviceName": "unknown", "tags": []}
    # tags 可能是字符串
    tags = process.get('tags', [])
    tags = parse_tags(tags)
    return {"serviceName": process.get('serviceName', 'unknown'), "tags": tags}

def parse_references(refs):
    # refs 可能是字符串、list、ndarray
    if isinstance(refs, str):
        try:
            refs = ast.literal_eval(refs)
        except Exception:
            return []
    if isinstance(refs, np.ndarray):
        refs = refs.tolist()
    if not isinstance(refs, list):
        return []
    result = []
    for ref in refs:
        if isinstance(ref, dict):
            result.append(ref)
        elif isinstance(ref, str):
            try:
                ref_obj = ast.literal_eval(ref)
                if isinstance(ref_obj, dict):
                    result.append(ref_obj)
            except Exception:
                continue
    return result

def process_chunk(
        df_chunk,
        start_time_ts,
        end_time_ts):
    """
    处理单个数据块，返回：
    1. status_code异常的span列表
    2. 按(serviceName, net.peer.ip)聚合的duration列表
    """
    abnormal_status = []
    duration_map = {}
    error_duration_map = {} # 记录故障时间范围内的duration

    for idx, row in df_chunk.iterrows():
        # 解析tags
        tags = parse_tags(row.get('tags', []))

        # 提取status_code, net.peer.ip
        status_code = tags.get('status.code','')
        net_peer_ip = tags.get('net.peer.ip','')

        # 解析process
        process = parse_process(row.get('process', {}))
        service_name = process['serviceName']
        spanKind = tags.get('span.kind', '')
        duration = row.get('duration', 0)
        message = tags.get('status.message','')
        trace_id = row.get('traceId', None)
        span_id = row.get('spanId',None)
        span_start_time = row.get('startTimeMillis', None)

        if spanKind == "client":
            key = (service_name,net_peer_ip)
            trace_relation = f"{service_name} -> {net_peer_ip}"
        else:
            key = (net_peer_ip,service_name)
            trace_relation = f"{net_peer_ip} -> {service_name}"
        
        # 记录status_code异常
        if str(status_code) not in ['0', '0.0', 0, 0.0, '', None]:
            abnormal_status.append({
                'key': trace_relation,
                'status_code': status_code,
                'message': message,
                'traceId': trace_id,
                'spanId': span_id
            })

        try:
            duration = float(duration)
        except Exception:
            duration = 0

        if key not in duration_map:
            duration_map[key] = []
        duration_map[key].append(duration)

        if key not in error_duration_map:
            error_duration_map[key] = []
        
        # print(f"{span_start_time},{start_time_ts},{end_time_ts}")
        if span_start_time > start_time_ts and span_start_time < end_time_ts:
            error_duration_map[key].append(duration)

    return abnormal_status, duration_map, error_duration_map

def merge_duration_maps(map_list):
    merged = {}
    for dmap in map_list:
        for key, durations in dmap.items():
            if key not in merged:
                merged[key] = []
            merged[key].extend(durations)
    return merged

def get_p95_durations(duration_map):
    p95_result = {}
    for key, durations in duration_map.items():
        if not durations:
            continue
        durations_sorted = sorted(durations)
        n = len(durations_sorted)
        # 计算均值
        mean_value = sum(durations_sorted) / n
        # 计算p95索引
        p95_idx = int(n * 0.95)
        if p95_idx >= n:
            p95_idx = n - 1
        p95_value = durations_sorted[p95_idx]
        # 统计大于等于p95的数量
        count_ge_p95 = sum(1 for d in durations_sorted if d >= p95_value)
        # 计算p95占比
        percent_ge_p95 = count_ge_p95 / n if n > 0 else 0
        p95_result[key] = {
            'mean': mean_value,
            'p95': p95_value,
            'count_ge_p95': count_ge_p95,
            'percent_ge_p95': percent_ge_p95
        }
    return p95_result

def check_trace_duration(
    start_time: str,
    end_time: str
) -> list:

    start_time_ts = int(pd.to_datetime(start_time).timestamp() * 1000)
    end_time_ts = int(pd.to_datetime(end_time).timestamp() * 1000)
    
    filedate = format_time_to_shanghai(start_time).split('_')[0]
    filename = f"./data/{filedate}/trace-parquet/trace_jaeger-span_{format_time_to_shanghai(start_time)}-00-00.parquet"
    if not os.path.exists(filename):
        return  f"文件{filename}不存在"
    
    print(f"[提示] 开始处理文件：{filename}")
    df = pq.read_table(filename).to_pandas()
    total_rows = len(df)
    print(f"[提示] 数据总行数为 {total_rows}")
    print(f"[提示] 开始并发处理数据...")

    # 记录每个trace的最后一个span的spanid
    # 这里假设有'traceId', 'spanId', 'startTime'字段
    if 'traceId' in df.columns and 'spanId' in df.columns and 'startTime' in df.columns:
        last_spanid_map = (
            df.sort_values('startTime')
              .groupby('traceId')
              .tail(1)
              .set_index('traceId')['spanId']
              .to_dict()
        )
    else:
        last_spanid_map = {}

    # 切分数据
    chunk_size = 10000
    num_workers = 10
    chunks = []
    for i in range(0, total_rows, chunk_size):
        chunks.append(df.iloc[i:i+chunk_size])
    
    abnormal_status_all = []
    duration_map_list = []
    error_duration_map_list = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(process_chunk, chunk, start_time_ts, end_time_ts) for chunk in chunks]
        for future in concurrent.futures.as_completed(futures):
            abnormal_status, duration_map, error_duration_map = future.result()
            abnormal_status_all.extend(abnormal_status)
            duration_map_list.append(duration_map)
            error_duration_map_list.append(error_duration_map)

    # 合并duration_map
    merged_duration_map = merge_duration_maps(duration_map_list)
    # 计算P95以上的duration
    p95_durations = get_p95_durations(merged_duration_map)
    # 计算故障期间的duration的均值，并按key聚合
    error_merged_duration_map = merge_duration_maps(error_duration_map_list)
    error_duration_mean_map = {}
    for key, durations in error_merged_duration_map.items():
        if durations:
            error_duration_mean_map[key] = sum(durations) / len(durations)
        else:
            error_duration_mean_map[key] = 0

    print(f"[结果] status_code异常数量: {len(abnormal_status_all)}")
    print(f"[结果] 按(serviceName, net.peer.ip)聚合的P95以上duration如下：")
    return_string = []
    for key,durations in p95_durations.items():
        service, ip = key
        error_mean_duration = error_duration_mean_map[key]
        return_string.append(f"源: {service}, 目标: {ip}, P95以上耗时数量: {durations['count_ge_p95']}, 耗时均值: {error_mean_duration:.2f} ms, P95值: {durations['p95']:.2f} ms")
    
    return return_string

def main():

    process_start_time = time.time()
    start_time = '2025-06-05T19:10:07Z'
    end_time = '2025-06-05T19:33:07Z'
    result = check_trace_duration(start_time=start_time,
              end_time=end_time)
    for item in result:
        print(item)
    process_end_time = time.time()
    process_time = process_end_time - process_start_time
    print(f"日志处理结束，处理时间{process_time}s")

if __name__ == "__main__":
    main()
