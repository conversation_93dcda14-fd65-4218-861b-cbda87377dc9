#!/usr/bin/python3
# 分析指标异常情况
# 1、 p9X的异常指标
# 2、 百分比类型，超过经验阈值的异常指标
# TODO 补充以下指标：
# 1. 每个指标对应的component列
# 2. 每个指标对应的单位
# 3. 过滤指标，
#   3.1 某些指标具有相同的趋势，过滤
#   3.2 某些指标是恒定值，如存储总量，过滤
# 4. 考虑通过z-score来判断是否存在离线点


from datetime import datetime
import pandas as pd
import re
import pytz
import os
import glob
import numpy as np
import time
import sys

# 添加配置路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.data_config import get_data_path, get_metric_file_path

# tidb的指标
# tidb数据
# 1、 slow_query慢查询是瞬时指标
# 2、 tidb的数据比较独立，都单独运行在tidb的namespace内
metric_infra_tidb_info = {
    "connection_count": {
        "value_field": "connection_count",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type" 
    },
    "cpu_usage": {
        "value_field": "cpu_usage",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'core',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "duration_avg": {
        "value_field": "duration_avg",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'seconds',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "failed_query_ops": {
        "value_field": "failed_query_ops",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type,type"  
    },
    "qps": {
        "value_field": "qps",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count/s',
        "label_field": "instance,kpi_name,namespace,object_type,type"  
    },
    "slow_query": {
        "value_field": "slow_query",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'seconds',
        "label_field": "instance,kpi_name,namespace,object_type,sql_type"  
    },
    "top_sql_cpu": {
        "value_field": "top_sql_cpu",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "block_cache_size": {
        "value_field": "block_cache_size",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'bytes',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "duration_95th": {
        "value_field": "duration_95th",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": True,
        "unit": 'seconds',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "duration_99th": {
        "value_field": "duration_99th",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": True,
        "unit": 'seconds',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "memory_usage": {
        "value_field": "memory_usage",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'bytes',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "uptime": {
        "value_field": "uptime",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'seconds',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "server_is_up": {
        "value_field": "server_is_up",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type"
    },
    "transaction_retry": {
        "value_field": "transaction_retry",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count/s',
        "label_field": "instance,kpi_name,namespace,object_type"
    },
    # 以下的三个指标在文件内没有查看到
    "statement_avg_queries": {
        "value_field": "statement_avg_queries",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type"
    },
    "ddl_job_count": {
        "value_field": "ddl_job_count",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type"
    },
    "top_sql_cpu": {
        "value_field": "top_sql_cpu",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'seconds/s',
        "label_field": "instance,kpi_name,namespace,object_type"
    }
}

# tidb pd和tikv的指标情况
# tikv没有直接提供使用率，需要计算
metric_infra_tikv_info = {
    "store_size": {
        "value_field": "store_size",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": True,
        "unit": 'bytes',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "available_size": {
        "value_field": "available_size",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'bytes',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "capacity_size": {
        "value_field": "capacity_size",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'bytes',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "cpu_usage": {
        "value_field": "cpu_usage",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'core/s',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "memory_usage": {
        "value_field": "memory_usage",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'bytes',
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "grpc_qps": {
        "value_field": "grpc_qps",
        "timestamp_field": "time",
        "component_field": "type",
        "is_filter": False,
        "unit": 'count/s',
        "label_field": "instance,kpi_name,namespace,object_type,type"  
    },
    "io_util": {
        "value_field": "io_util",
        "timestamp_field": "time",
        "component_field": "device",
        "is_filter": False,
        "unit": '%',
        "label_field": "device,instance,kubernetes_node,kpi_name,object_type"  
    },
    "qps": {
        "value_field": "qps",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count/s',
        "label_field": "instance,kpi_name,namespace,object_type,type"  
    },
    "raft_apply_wait": {
        "value_field": "raft_apply_wait",
        "timestamp_field": "time",
        "component_field": "type",
        "is_filter": False,
        "unit": 'seconds',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "raft_propose_wait": {
        "value_field": "raft_propose_wait",
        "timestamp_field": "time",
        "component_field": "type",
        "is_filter": False,
        "unit": 'seconds',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "read_mbps": {
        "value_field": "read_mbps",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "region_pending": {
        "value_field": "region_pending",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "rocksdb_write_stall": {
        "value_field": "rocksdb_write_stall",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "server_is_up": {
        "value_field": "server_is_up",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "snapshot_apply_count": {
        "value_field": "snapshot_apply_count",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "write_wal_mbps": {
        "value_field": "write_wal_mbps",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "threadpool_readpool_cpu": {
        "value_field": "threadpool_readpool_cpu",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'core/s',
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    }
}

metric_infra_pd_info = {
    "abnormal_region_count": {
        "value_field": "abnormal_region_count",
        "timestamp_field": "time",
        "component_field": "type",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type,type"  
    },
    "cpu_usage": {
        "value_field": "cpu_usage",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'core/s',
        "label_field": "kpi_name,instance,object_type"  
    },
    "memory_usage": {
        "value_field": "memory_usage",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'bytes',
        "label_field": "kpi_name,instance,object_type"  
    },
    "leader_count": {
        "value_field": "leader_count",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type"  
    },
    "leader_primary": {
        "value_field": "leader_primary",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,instance,object_type"  
    },
    "learner_count": {
        "value_field": "learner_count",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": True,
        "unit": 'count',
        "label_field": "kpi_name,instance,object_type"  
    },
    "region_count": {
        "value_field": "region_count",
        "timestamp_field": "time",
        "component_field": "instance",
        "is_filter": True,
        "unit": 'count',
        "label_field": "kpi_name,object_type"  
    },
    "region_health": {
        "value_field": "region_health",
        "timestamp_field": "time",
        "component_field": "type",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type,type"  
    },
    "witness_count": {
        "value_field": "witness_count",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type"  
    },
    "storage_capacity": {
        "value_field": "storage_capacity",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": True,
        "unit": 'bytes',
        "label_field": "kpi_name,object_type"  
    },
    "storage_size": {
        "value_field": "storage_size",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": True,
        "unit": 'bytes',
        "label_field": "kpi_name,object_type"  
    },
    "storage_used_ratio": {
        "value_field": "storage_used_ratio",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": False,
        "unit": '%',
        "label_field": "kpi_name,object_type"  
    },
    "store_down_count": {
        "value_field": "store_down_count",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type"  
    },
    "store_low_space_count": {
        "value_field": "store_low_space_count",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type"  
    },
    "store_slow_count": {
        "value_field": "store_slow_count",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type"  
    },
    "store_unhealth_count": {
        "value_field": "store_unhealth_count",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type"  
    },
    "store_up_count": {
        "value_field": "store_up_count",
        "timestamp_field": "time",
        "component_field": "object_type",
        "is_filter": False,
        "unit": 'count',
        "label_field": "kpi_name,object_type"  
    }
}

metric_infra_node_info = {
    "node_cpu_usage_rate": {
        "value_field": "node_cpu_usage_rate",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": '%',
        "label_field": "instance,kpi_name,kubernetes_node,object_type"
    },
    "node_memory_MemAvailable_bytes":{
        "value_field": "node_memory_MemAvailable_bytes",
        "timestamp_field": "time",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": True,
        "unit": 'bytes',
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_memory_MemTotal_bytes": {
        "value_field": "node_memory_MemTotal_bytes",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": True,
        "unit": 'bytes',
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },    
    "node_memory_usage_rate": {
        "value_field": "node_memory_usage_rate",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": '%',
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_disk_read_bytes_total": {
        "value_field": "node_memory_usage_rate",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "device,instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_disk_written_bytes_total": {
        "value_field": "node_disk_written_bytes_total",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "device,instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_disk_read_time_seconds_total": {
        "value_field": "node_disk_read_time_seconds_total",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'seconds/s',
        "label_field": "device,instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_disk_write_time_seconds_total": {
        "value_field": "node_disk_write_time_seconds_total",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'seconds/s',  
        "label_field": "device,instance,kpi_name,kubernetes_node,object_type"  
    },  
    "node_filesystem_free_bytes": {
        "value_field": "node_filesystem_free_bytes",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": True,
        "unit": 'bytes',  
        "label_field": "device,instance,kpi_name,kubernetes_node,mountpoint,object_type"  
    },
    "node_filesystem_size_bytes": {
        "value_field": "node_filesystem_size_bytes",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": True,
        "unit": 'bytes',  
        "label_field": "device,instance,kpi_name,kubernetes_node,mountpoint,object_type"  
    },    
    "node_filesystem_usage_rate": {
        "value_field": "node_filesystem_usage_rate",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": '%', 
        "label_field": "device,instance,kpi_name,kubernetes_node,mountpoint,object_type"  
    },    
    "node_network_receive_bytes_total": {
        "value_field": "node_network_receive_bytes_total",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'bytes/s', 
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_network_transmit_bytes_total": {
        "value_field": "node_network_transmit_bytes_total",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'bytes/s', 
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_network_receive_packets_total": {
        "value_field": "node_network_receive_packets_total",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'packets/s', 
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_network_transmit_packets_total": {
        "value_field": "node_network_transmit_packets_total",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'packets/s', 
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_sockstat_TCP_inuse": {
        "value_field": "node_sockstat_TCP_inuse",
        "timestamp_field": "time",
        "component_field": "kubernetes_node",
        "is_filter": False,
        "unit": 'count', 
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    }
}

# 数据关联
# 1、 pod指标内instance对应pod的worker节点信息，和infra_node的kubernetes_node信息保持一致
metric_infra_pod_info = {
    "pod_cpu_usage": {
        "value_field": "pod_cpu_usage",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'core/s',
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_memory_working_set_bytes": {
        "value_field": "pod_memory_working_set_bytes",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_network_receive_bytes": {
        "value_field": "pod_network_receive_bytes",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_network_transmit_bytes": {
        "value_field": "pod_network_transmit_bytes",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_network_receive_packets": {
        "value_field": "pod_network_receive_packets",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'packets/s',
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_network_transmit_packets": {
        "value_field": "pod_network_transmit_packets",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'packets/s',
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_fs_reads_bytes": {
        "value_field": "pod_fs_reads_bytes",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "device,instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_fs_writes_bytes": {
        "value_field": "pod_fs_writes_bytes",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'bytes/s',
        "label_field": "device,instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_processes": {
        "value_field": "pod_processes",
        "timestamp_field": "time",
        "component_field": "pod",
        "is_filter": False,
        "unit": 'count',
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    }
}

def format_time_to_shanghai(time_str):
    # 解析输入的UTC时间字符串
    dt_utc = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%SZ")
    # 设置为UTC时区
    dt_utc = dt_utc.replace(tzinfo=pytz.UTC)
    # 转换为上海时区
    dt_shanghai = dt_utc.astimezone(pytz.timezone("Asia/Shanghai"))
    # 格式化输出为 年-月-日_小时
    return dt_shanghai.strftime("%Y-%m-%d")

def analyze_metrics(df, start_time, end_time, entity_type, p9x=0.95):
    """
    分析指标，计算指标的均值，p9x值
    start_time是开始时间
    end_time是结束时间
    p9x: 分位数（如0.95, 0.99等）
    entity_type: 不同类型对象，通常是node，pod，tidb，other，apm
    """
    # 转换时间格式
    df['time'] = pd.to_datetime(df['time'])
    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据
    mask = (df['time'] >= start_dt) & (df['time'] <= end_dt)
    filtered_df = df[mask]
    if filtered_df.empty:
        return []
    
    kpi_stats = []
    if 'apm' in entity_type:
        # apm需要计算的key： client_error，client_error_ratio，error,error_ratio,server_error,server_error_ratio,timeout
        # 增加APM指标及其单位
        apm_keys = [
            {"key": "client_error", "unit": "count"},
            {"key": "client_error_ratio", "unit": "%"},
            {"key": "error", "unit": "count"},
            {"key": "error_ratio", "unit": "%"},
            {"key": "server_error", "unit": "count"},
            {"key": "server_error_ratio", "unit": "%"},
            {"key": "timeout", "unit": "count"},
            {"key": "rrt", "unit": "ms"},
            {"key": "rrt_max", "unit": "ms"}
        ]
        for apm_item in apm_keys:
            key = apm_item["key"]
            unit = apm_item.get("unit", "")
            # 如果是rtt或rtt_max，单位改为ms
            is_rtt = key in ["rrt", "rrt_max"]
            if key in filtered_df.columns:
                if is_rtt:
                    mean_val = filtered_df[key].mean() / 1000
                    max_val = filtered_df[key].max() / 1000
                    min_val = filtered_df[key].min() / 1000
                    std_val = filtered_df[key].std() / 1000
                    # p9x分位数要用当天所有数据（df，不是filtered_df）来算
                    if key in df.columns:
                        p9x_val = df[key].quantile(p9x) / 1000
                    else:
                        p9x_val = None
                    unit_out = "ms"
                else:
                    mean_val = filtered_df[key].mean()
                    max_val = filtered_df[key].max()
                    min_val = filtered_df[key].min()
                    std_val = filtered_df[key].std()
                    if key in df.columns:
                        p9x_val = df[key].quantile(p9x)
                    else:
                        p9x_val = None
                    unit_out = unit
                if std_val and std_val != 0:
                    z_zero = (0 - mean_val) / std_val
                else:
                    z_zero = None
                kpi_stats.append({
                    "kpi": key,
                    "mean": mean_val,
                    f"p{int(p9x*100)}": float(p9x_val) if p9x_val is not None else None,
                    "max": float(max_val) if max_val is not None else None,
                    "min": float(min_val) if min_val is not None else None,
                    "z_zero": float(z_zero) if z_zero is not None else None,
                    "unit": unit_out
                })
        return {"kpi_stats": kpi_stats}

    else: 
        if 'kpi_key' in filtered_df.columns:
            for kpi in filtered_df['kpi_key'].unique():
                kpi_df = filtered_df[filtered_df['kpi_key'] == kpi]
                # 只统计kpi_key对应的value列
                # 例如kpi_key为"cpu_usage"，则取"cpu_usage"这一列
                # 读取对应的json，获取label_field
                import json

                # 定义各类型对应的json文件路径
                type_json_map = {
                    "tidb": metric_infra_tidb_info,
                    "other-pd": metric_infra_pd_info,
                    "other-tikv": metric_infra_tikv_info,
                    "node": metric_infra_node_info,
                    "pod": metric_infra_pod_info
                }
                json_file = type_json_map.get(entity_type, None)
                if json_file is not None:
                    try:
                        metric_info = json_file
                    except Exception as e:
                        metric_info = {}
                else:
                    metric_info = {}

                # 获取label_field
                label_field = None
                if kpi in metric_info:
                    label_field = metric_info[kpi].get("label_field", None)
                    is_filter = metric_info[kpi].get("is_filter", None)
                    component_field = metric_info[kpi].get("component_field", None)
                    unit = metric_info[kpi].get("unit", None)

                # 如果没有label_field，默认用"instance",不进行处理该指标
                if not label_field:
                    label_field = "instance"
                    is_filter = True

                if is_filter:
                    print(f"当前指标{kpi}不再指标内，或当前指标不需要进行检索")
                    continue

                label_fields = [x.strip() for x in label_field.split(",")]

                value_col = kpi  # 列名与kpi_key一致
                if value_col in kpi_df.columns:
                    # 按label_fields聚合
                    groupby_obj = kpi_df.groupby(label_fields)
                    # 针对p9x分位数，需要用当天所有数据（df）中该kpi的分位数，并且也要按label_fields聚合
                    if 'kpi_key' in df.columns:
                        df_kpi_all = df[df['kpi_key'] == kpi]
                    else:
                        df_kpi_all = df

                    # 对全量数据也按label_fields聚合
                    if all(field in df_kpi_all.columns for field in label_fields):
                        df_kpi_all_grouped = df_kpi_all.groupby(label_fields)
                    else:
                        # 如果全量数据没有label_fields，降级为不分组
                        df_kpi_all_grouped = None

                    for group_keys, group_df in groupby_obj:
                        values = group_df[value_col].dropna()
                        # 读取df列为component_field的value
                        component_values = group_df[component_field].unique() if component_field in group_df.columns else []
                        if len(values) > 0:
                            mean_val = values.mean()
                            # p9x分位数用当天所有数据，且分组
                            if (
                                value_col in df_kpi_all.columns
                                and df_kpi_all_grouped is not None
                                and group_keys in df_kpi_all_grouped.groups
                            ):
                                # 获取全量数据中同一组的分组数据
                                df_kpi_all_group = df_kpi_all_grouped.get_group(group_keys)
                                p9x_val = df_kpi_all_group[value_col].dropna().quantile(p9x)
                            else:
                                p9x_val = None
                            # group_keys 可能是tuple，也可能是单值
                            if isinstance(group_keys, tuple):
                                label_dict = dict(zip(label_fields, group_keys))
                            else:
                                label_dict = {label_fields[0]: group_keys}
                            kpi_stats.append({
                                "kpi": kpi,
                                "component": component_values,
                                "unit": unit,
                                "field": value_col,
                                "labels": label_dict,
                                "mean": float(mean_val),
                                f"p{int(p9x*100)}": float(p9x_val) if p9x_val is not None else None,
                                "max": float(values.max()) if len(values) > 0 else None,
                                "min": float(values.min()) if len(values) > 0 else None,
                                "z_zero": float((mean_val - 0) / values.std()) if len(values) > 1 and values.std() != 0 else None
                            })
        return {"kpi_stats": kpi_stats}

def process_directory(directory, start_time, end_time, entity_type):
    """
    处理指定目录下的所有parquet文件
    """
    results = []
    # 查找所有parquet文件
    parquet_files = glob.glob(os.path.join(directory, "*.parquet"))
    for file_path in parquet_files:
        filename = os.path.basename(file_path)
        # print(f"正在处理文件: {filename}")
        
        try:
            # 读取parquet文件
            df = pd.read_parquet(file_path)
            
            # 从文件名提取实体名称
            if entity_type == "tidb":
                # 例如：infra_tidb_block_cache_size_2025-06-14.parquet
                # entity_type: tidb
                # entity_name: block_cache_size
                match = re.search(r'infra_tidb_(.+)_\d{4}-\d{2}-\d{2}\.parquet', filename)
                if match:
                    entity_name = match.group(1)
                else:
                    continue
            elif entity_type == "other-pd": 
                # pod_adservice-0_2025-06-06.parquet
                # print(f"正在处理文件: {filename}")
                if 'pd' in filename:
                    match = re.search(r'infra_pd_(.+)_\d{4}-\d{2}-\d{2}\.parquet', filename)
                    # entity_type: other，需要调整
                    # entity_name: metric_name
                    if match:
                        entity_name = match.group(1)
                    else:
                        continue
                else:
                    continue

            elif entity_type == "other-tikv":     
                if 'tikv' in filename:
                    # print(f"正在处理文件: {filename}")
                    match = re.search(r'infra_tikv_(.+)_\d{4}-\d{2}-\d{2}\.parquet', filename)
                    # entity_type: other，需要调整
                    # entity_name: metric_name
                    if match:
                        entity_name = match.group(1)
                    else:
                        continue   
                else:
                    continue   

            elif entity_type == "node":
                # 支持匹配如 infra_node_node_cpu_usage_rate_2025-06-14.parquet，提取node_cpu_usage_rate
                match = re.search(r'infra_node_([^_]+(?:_[^_]+)*)_\d{4}-\d{2}-\d{2}', filename)
                if match:
                    entity_name = match.group(1)
                else:
                    continue
            elif entity_type == "pod":
                # 支持匹配如 infra_node_node_cpu_usage_rate_2025-06-14.parquet，提取node_cpu_usage_rate
                match = re.search(r'infra_pod_([^_]+(?:_[^_]+)*)_\d{4}-\d{2}-\d{2}', filename)
                if match:
                    entity_name = match.group(1)
                else:
                    continue
            elif entity_type == "apmservice":
                # service_adservice_2025-06-06.parquet
                match = re.search(r'service_([^_]+)_\d{4}-\d{2}-\d{2}', filename)
                if match:
                    entity_name = match.group(1)
                else:
                    continue
            elif entity_type == "apmpod":
                # service_adservice_2025-06-06.parquet
                match = re.search(r'pod_([^_]+)_\d{4}-\d{2}-\d{2}', filename)
                if match:
                    entity_name = match.group(1)
                else:
                    continue
            else:
                continue
            
            # 分析指标
            issues = analyze_metrics(df=df, 
                                     start_time=start_time, 
                                     end_time=end_time,
                                     entity_type=entity_type)
            
            # 修正遍历方式，确保 issues["kpi_stats"] 是列表
            if issues and "kpi_stats" in issues:
                for stat in issues["kpi_stats"]:

                    # if 'io_util' in stat.get('kpi'):
                    #     print(stat)
                    # print(stat)
                    results.append({
                        'entity_type': entity_type,
                        'entity_name': entity_name,
                        'component': stat.get('component') if 'component' in stat else None,
                        'unit': stat.get('unit'),
                        'kpi': stat.get('kpi'),
                        'field': stat.get('field') if 'field' in stat else None,
                        'labels': stat.get('labels') if 'labels' in stat else None,
                        'mean': stat.get('mean'),
                        'p95': stat.get('p95'),
                        'max': stat.get('max'),
                        'min': stat.get('min'),
                        'z_zero': stat.get('z_zero'),
                        'start_time': start_time,
                        'end_time': end_time
                    })

                # if stat.get('mean') > stat.get('p95'):
                #     print(f"类型:{entity_type}，指标名称：{entity_name}，指标：{stat.get('kpi')}，聚合：{stat.get('labels')}，均值：{stat.get('mean')},p95值：{stat.get('p95')}")
                
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")
    
    # for item in results:
    #     print(item)
    return results

def check_metric_pod_restart(
    start_time: str,
    end_time: str,
):
    """增加pod变化worker节点指标"""

    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = get_data_path(date_str, "metric-parquet", "infra/infra_pod")
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_pod_pod_cpu_usage_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据，前后start end时间各增加5分钟
    start_dt_adj = start_dt - pd.Timedelta(minutes=5)
    end_dt_adj = end_dt + pd.Timedelta(minutes=5)
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 按照instance和pod进行分组，统计每个pod的instance数量（基于filtered_df进行过滤）
    if isinstance(filtered_df, list) and len(filtered_df) > 0:
        filtered_df = pd.read_parquet(filtered_df[0])
    if not filtered_df.empty and 'pod' in filtered_df.columns and 'instance' in filtered_df.columns:
        pod_instance_count = filtered_df.groupby('pod')['instance'].nunique().reset_index()
        pod_instance_count.rename(columns={'instance': 'instance_count'}, inplace=True)
        # 转换为json串返回
        result_json = pod_instance_count.to_json(orient='records', force_ascii=False)
        return result_json
    else:
        print("数据中缺少pod或instance字段，无法统计pod的instance数量。")

def check_metric_tidb_pd(
    start_time: str,
    end_time: str,
):

    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = get_data_path(date_str, "metric-parquet")
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    other_dir = os.path.join(date_dir, "other")
    if os.path.exists(other_dir):
        print(f"\n检查other目录: {other_dir}")
        other_results = process_directory(other_dir, start_time, end_time, "other-pd")
        results.extend(other_results)
    return results
    
def check_metric_tidb_tikv(
    start_time: str,
    end_time: str,
):

    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    other_dir = os.path.join(date_dir, "other")
    if os.path.exists(other_dir):
        print(f"\n检查other目录: {other_dir}")
        other_results = process_directory(other_dir, start_time, end_time, "other-tikv")
        results.extend(other_results)
    return results

def check_metric_tidb_tidb(
    start_time: str,
    end_time: str,
):

    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理service目录
    tidb_dir = os.path.join(date_dir, "infra/infra_tidb")
    if os.path.exists(tidb_dir):
        print(f"\n检查tidb目录: {tidb_dir}")
        tidb_results = process_directory(tidb_dir, start_time, end_time, "tidb")
        results.extend(tidb_results)

    return results
    
def check_metric_infra(
        start_time: str,
        end_time: str
):
    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理service目录
    node_dir = os.path.join(date_dir, "infra/infra_node")
    if os.path.exists(node_dir):
        print(f"\n检查node目录: {node_dir}")
        tidb_results = process_directory(node_dir, start_time, end_time, "node")
        results.extend(tidb_results)

    # 处理pod目录
    pod_dir = os.path.join(date_dir, "infra/infra_pod")
    if os.path.exists(pod_dir):
        print(f"\n检查other目录: {pod_dir}")
        other_results = process_directory(pod_dir, start_time, end_time, "pod")
        results.extend(other_results)

    return results

def check_metric_infra_node(
        start_time: str,
        end_time: str
):
    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理service目录
    node_dir = os.path.join(date_dir, "infra/infra_node")
    if os.path.exists(node_dir):
        print(f"\n检查node目录: {node_dir}")
        tidb_results = process_directory(node_dir, start_time, end_time, "node")
        results.extend(tidb_results)

    return results

def check_metric_infra_pod(
        start_time: str,
        end_time: str
):
    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    pod_dir = os.path.join(date_dir, "infra/infra_pod")
    if os.path.exists(pod_dir):
        print(f"\n检查other目录: {pod_dir}")
        other_results = process_directory(pod_dir, start_time, end_time, "pod")
        results.extend(other_results)

    return results

def check_metric_apm(
    start_time: str,
    end_time: str
):
    """
    检查apm数据
    """
    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")

    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理service目录
    service_dir = os.path.join(date_dir, "apm/service")
    if os.path.exists(service_dir):
        print(f"\n检查service目录: {service_dir}")
        tidb_results = process_directory(service_dir, start_time, end_time, "apmservice")
        results.extend(tidb_results)

    # 处理pod目录
    pod_dir = os.path.join(date_dir, "apm/pod")
    if os.path.exists(pod_dir):
        print(f"\n检查pod目录: {pod_dir}")
        other_results = process_directory(pod_dir, start_time, end_time, "apmpod")
        results.extend(other_results)

    return results


def check_metric_apm_service_history(
    start_time: str,
    end_time: str
):
    """
    检查apm service的rrt数据，比较基线数据
    """
    service_history_baseline_data = {'adservice': {'rrt': 8029.88, 'error_ratio': 2.73, 'client_error_ratio': 2.73, 'server_error_ratio': 0.0, 'timeout': 3.0}, 'cartservice': {'rrt': 5475.04, 'error_ratio': 2.16, 'client_error_ratio': 2.16, 'server_error_ratio': 0.0, 'timeout': 11.0}, 'currencyservice': {'rrt': 1429.59, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'productcatalogservice': {'rrt': 7577.69, 'error_ratio': 0.44, 'client_error_ratio': 0.44, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'checkoutservice': {'rrt': 12232.23, 'error_ratio': 4.2, 'client_error_ratio': 4.2, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'recommendationservice': {'rrt': 4928.13, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'shippingservice': {'rrt': 1760.64, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'emailservice': {'rrt': 7259.25, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'paymentservice': {'rrt': 17717.8, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}}
    
    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")

    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理service目录
    service_dir = os.path.join(date_dir, "apm/service")
    if os.path.exists(service_dir):
        print(f"\n检查service目录: {service_dir}")
        apm_pod_results = process_directory(service_dir, start_time, end_time, "apmservice")
        results.extend(apm_pod_results)
    
    # 比较历史数据
    return_data = []
    for entity in results:
        metric_name = entity['kpi']
        if metric_name in ['rrt','error_ratio','client_error_ratio','error_ratio','server_error_ratio','timeout']:
            object_name = entity['entity_name']
            # 只对service数据进行
            if object_name not in service_history_baseline_data.keys():
                continue

            metric_value = float(entity['mean'])
            if metric_name == 'rrt':
                metric_value = metric_value * 1000
            
            if  metric_value > service_history_baseline_data[object_name][metric_name]:
                entity_value_ratio = metric_value/service_history_baseline_data[object_name][metric_name] if service_history_baseline_data[object_name][metric_name] != 0 else metric_value
                entity['baseline_ratio'] = entity_value_ratio
                return_data.append(entity)

    return return_data

def check_metric_apm_pod_history(
    start_time: str,
    end_time: str
):
    """
    检查apm service的rrt数据，比较基线数据
    """
    pod_history_baseline_data = {'adservice-0': {'rrt': 18926.96, 'error_ratio': 8.33, 'client_error_ratio': 8.33, 'server_error_ratio': 0.0, 'timeout': 1.0}, 'adservice-1': {'rrt': 5149.45, 'error_ratio': 3.19, 'client_error_ratio': 3.19, 'server_error_ratio': 0.0, 'timeout': 1.0}, 'adservice-2': {'rrt': 18897.44, 'error_ratio': 8.33, 'client_error_ratio': 8.33, 'server_error_ratio': 0.0, 'timeout': 1.0}, 'cartservice-0': {'rrt': 24780.0, 'error_ratio': 44.44, 'client_error_ratio': 44.44, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'cartservice-1': {'rrt': 5566.07, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 11.0}, 'cartservice-2': {'rrt': 14351.35, 'error_ratio': 25.0, 'client_error_ratio': 25.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'currencyservice-0': {'rrt': 1957.05, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'currencyservice-1': {'rrt': 1550.75, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'currencyservice-2': {'rrt': 4588.25, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'productcatalogservice-0': {'rrt': 14526.2, 'error_ratio': 23.08, 'client_error_ratio': 23.08, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'productcatalogservice-1': {'rrt': 6639.58, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'productcatalogservice-2': {'rrt': 8365.22, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'checkoutservice-2': {'rrt': 12232.23, 'error_ratio': 4.2, 'client_error_ratio': 4.2, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'recommendationservice-0': {'rrt': 4928.13, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'shippingservice-0': {'rrt': 1988.1, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'shippingservice-1': {'rrt': 6904.5, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'shippingservice-2': {'rrt': 5385.3, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'emailservice-0': {'rrt': 12562.5, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'emailservice-1': {'rrt': 7678.42, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'emailservice-2': {'rrt': 9215.17, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'paymentservice-0': {'rrt': 82772.0, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'paymentservice-1': {'rrt': 39131.0, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'paymentservice-2': {'rrt': 18116.0, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}}
    
    results = []
    print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")

    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理service目录
    service_dir = os.path.join(date_dir, "apm/pod")
    if os.path.exists(service_dir):
        print(f"\n检查pod目录: {service_dir}")
        apm_service_results = process_directory(service_dir, start_time, end_time, "apmpod")
        results.extend(apm_service_results)
    
    # 比较历史数据
    return_data = []
    for entity in results:
        metric_name = entity['kpi']
        if metric_name in ['rrt','error_ratio','client_error_ratio','error_ratio','server_error_ratio','timeout']:
            object_name = entity['entity_name']
            # 只对service数据进行
            if object_name not in pod_history_baseline_data.keys():
                continue

            metric_value = float(entity['mean'])
            if metric_name == 'rrt':
                metric_value = metric_value * 1000
            
            if  metric_value > pod_history_baseline_data[object_name][metric_name]:
                entity_value_ratio = metric_value/pod_history_baseline_data[object_name][metric_name] if pod_history_baseline_data[object_name][metric_name] != 0 else metric_value
                entity['baseline_ratio'] = entity_value_ratio
                return_data.append(entity)

    return return_data

def check_node_cpu_rate(
    start_time: str,
    end_time: str,
):
    """增加node的cpu使用率"""

    results = []
    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    # print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/infra/infra_node"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_node_node_cpu_usage_rate_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据，前后start end时间各增加5分钟
    # 增加10分钟是为了防止出现1s的情况
    start_dt_adj = start_dt - pd.Timedelta(minutes=5)
    end_dt_adj = end_dt + pd.Timedelta(minutes=5)
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 按照kubernetes_node进行聚类，计算node_cpu_usage_rate的最大值
    if not filtered_df.empty and 'kubernetes_node' in filtered_df.columns and 'node_cpu_usage_rate' in filtered_df.columns:
        # 按kubernetes_node分组，计算每个节点的CPU使用率最大值
        node_cpu_max = filtered_df.groupby('kubernetes_node')['node_cpu_usage_rate'].max().reset_index()
        node_cpu_max.rename(columns={'node_cpu_usage_rate': 'node_cpu_usage_rate_max'}, inplace=True)
        # 转换为json串返回
        result_json = node_cpu_max.to_json(orient='records', force_ascii=False)
        return result_json
    else:
        print("数据中缺少kubernetes_node或node_cpu_usage_rate字段，无法统计节点的CPU使用率最大值。")
        return []
    
def check_node_filesystem_usage_rate(
    start_time: str,
    end_time: str,
):
    """增加pod变化worker节点指标"""

    results = []
    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    # print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/infra/infra_node"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_node_node_filesystem_usage_rate_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据，前后start end时间各增加5分钟
    # 增加10分钟是为了防止出现1s的情况
    start_dt_adj = start_dt - pd.Timedelta(minutes=5)
    end_dt_adj = end_dt + pd.Timedelta(minutes=5)
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 按照kubernetes_node进行聚类，计算node_cpu_usage_rate的最大值
    if not filtered_df.empty and 'kubernetes_node' in filtered_df.columns and 'node_filesystem_usage_rate' in filtered_df.columns:
        # 按kubernetes_node分组，计算每个节点的CPU使用率最大值
        node_cpu_max = filtered_df.groupby('kubernetes_node')['node_filesystem_usage_rate'].max().reset_index()
        node_cpu_max.rename(columns={'node_filesystem_usage_rate': 'node_filesystem_usage_rate_max'}, inplace=True)
        # 转换为json串返回
        result_json = node_cpu_max.to_json(orient='records', force_ascii=False)
        return result_json
    else:
        print("数据中缺少kubernetes_node或node_memory_usage_rate字段，无法统计节点的CPU使用率最大值。")
        return []
    
def check_metric_pod_process_abnormal(
    start_time: str,
    end_time: str,
):
    """
    增加pod异常process的情况
    但是清洗redis-cart的异常情况
    """

    results = []
    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    # print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/infra/infra_pod"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_pod_pod_processes_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据，前后start end时间各增加5分钟
    # 增加10分钟是为了防止出现1s的情况
    start_dt_adj = start_dt - pd.Timedelta(minutes=5)
    end_dt_adj = end_dt + pd.Timedelta(minutes=5)
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 按照instance和pod进行分组，统计每个pod的instance数量（基于filtered_df进行过滤）
    if isinstance(filtered_df, list) and len(filtered_df) > 0:
        filtered_df = pd.read_parquet(filtered_df[0])
    if not filtered_df.empty and 'pod' in filtered_df.columns and 'pod_processes' in filtered_df.columns:
        # 过滤掉pod名称为redis-cart-0的数据
        filtered_df = filtered_df[filtered_df['pod'] != 'redis-cart-0']
        # reset_index的作用是将groupby聚合后的结果从索引恢复为普通列，方便后续处理和输出
        pod_processes_avg = filtered_df.groupby('pod')['pod_processes'].max().reset_index()
        pod_processes_avg.rename(columns={'pod_processes': 'pod_processes_avg'}, inplace=True)
        # 转换为json串返回
        result_json = pod_processes_avg.to_json(orient='records', force_ascii=False)
        return result_json

    else:
        print("数据中缺少pod或pod_processes字段，无法统计pod的pod_processes数量。")
        return []

def check_node_io_util(
    start_time: str,
    end_time: str,
    io_util: float = 0.9
):
    """
    增加node的io_util使用率，请注意，
    虽然io_util标注的tikv的使用率，
    但是使用的node_exporter进行计算的，所以应该是node的数据
    """

    results = []
    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    # print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/other/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_tikv_io_util_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)
    
    # 过滤时间范围内的数据，前后start end时间各增加5分钟
    # 增加10分钟是为了防止出现1s的情况
    start_dt_adj = start_dt - pd.Timedelta(minutes=5)
    end_dt_adj = end_dt + pd.Timedelta(minutes=5)
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 按照instance和pod进行分组，统计每个pod的instance数量（基于filtered_df进行过滤）
    if isinstance(filtered_df, list) and len(filtered_df) > 0:
        filtered_df = pd.read_parquet(filtered_df[0])
    if not filtered_df.empty and 'io_util' in filtered_df.columns and 'kubernetes_node' in filtered_df.columns:
        # 按kubernetes_nodes分组，计算每个节点的io_util最大值
        tikv_io_util_max = filtered_df.groupby('kubernetes_node')['io_util'].max().reset_index()
        tikv_io_util_max.rename(columns={'io_util': 'io_util_max'}, inplace=True)
        # 转换为json串返回
        result_json = tikv_io_util_max.to_json(orient='records', force_ascii=False)
    else:
        print("数据中缺少kubernetes_node或io_util字段，无法统计tikv的io_util最大值。")
        return []
    
    import json
    return_string = []
    for node in json.loads(result_json):
        if float(node['io_util_max']) > io_util:
            kubernetes_node = node['kubernetes_node']
            print(f"{kubernetes_node}节点磁盘使用率超过预期,开始匹配影响pod")
            file_name = f"{date_str}/metric-parquet/infra/infra_pod/infra_pod_pod_cpu_usage_{date_str}.parquet"
            target_df = pd.read_parquet(file_name)
            target_df['time'] = pd.to_datetime(target_df['time'])
            mask = (target_df['time'] >= start_dt_adj) & (target_df['time'] <= end_dt_adj)
            filtered_target_df = target_df[mask]
            # 过滤instance等于kubernetes_node的数据
            matched_pods_df = filtered_target_df[filtered_target_df['instance'] == kubernetes_node]
            # 提取pod列并去重
            unique_pods = matched_pods_df['pod'].drop_duplicates().tolist()

            return_string.append(f"node节点{kubernetes_node}磁盘使用率超过90%，可能有影响的pod包括：{unique_pods}")  
    
    if len(return_string) > 0:
        return return_string
    else:
        return "没有发现node磁盘使用率高的情况"

def check_pod_network_drop_to_zero(
    start_time: str,
    end_time: str,
    durations: int = 300
):
    """增加pod网络变化指标"""

    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    # print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"/data/phaseone_data/{date_str}/metric-parquet/infra/infra_pod"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    # 处理pod目录
    
    parquet_file = glob.glob(os.path.join(date_dir, f"infra_pod_pod_network_receive_bytes_{date_str}.parquet"))
    df = pd.read_parquet(parquet_file)
    df['time'] = pd.to_datetime(df['time'])

    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)

    start_dt_adj = start_dt
    end_dt_adj = end_dt
    mask = (df['time'] >= start_dt_adj) & (df['time'] <= end_dt_adj)
    filtered_df = df[mask]

    # 检查每个pod在一段时间内流量是否持续为0且持续时间超过300秒
    if not filtered_df.empty and 'pod' in filtered_df.columns and 'time' in filtered_df.columns :
        pod_zero_interrupts = []
        for pod_name, pod_df in filtered_df.groupby('pod'):
            pod_df = pod_df.sort_values('time')
            pod_df = pod_df.reset_index(drop=True)
            # 过滤掉整个时间段内流量一直为0的pod
            if (pod_df['pod_network_receive_bytes'] == 0).all():
                continue
            zero_periods = []
            in_zero = False
            zero_start = None
            for idx, row in pod_df.iterrows():
                if row['pod_network_receive_bytes'] == 0:
                    if not in_zero:
                        in_zero = True
                        zero_start = row['time']
                else:
                    if in_zero:
                        zero_end = row['time']
                        duration = (zero_end - zero_start).total_seconds()
                        if duration > 300:
                            zero_periods.append({
                                'start': zero_start.strftime("%Y-%m-%dT%H:%M:%S"),
                                'end': zero_end.strftime("%Y-%m-%dT%H:%M:%S"),
                                'duration': duration
                            })
                        in_zero = False
                        zero_start = None
            # 处理最后一段为0且到结尾的情况
            if in_zero and zero_start is not None:
                zero_end = pod_df.iloc[-1]['time']
                duration = (zero_end - zero_start).total_seconds()
                if duration > durations:
                    zero_periods.append({
                        'start': zero_start.strftime("%Y-%m-%dT%H:%M:%S"),
                        'end': zero_end.strftime("%Y-%m-%dT%H:%M:%S"),
                        'duration': duration
                    })
            pod_zero_interrupts.append({
                'pod': pod_name,
                'zero_periods': zero_periods,
                'zero_interrupt_count': len(zero_periods)
            })
    
        pod_network_zero = []
        for item in pod_zero_interrupts:
            if item['zero_interrupt_count'] > 0:
                pod_network_zero.append(item)
        return pod_network_zero
        
    else:
        print("数据中缺少pod、time或value字段，无法判断pod的网络流量为0的情况。")
        return []
    
def main():

    process_start_time = time.time()
    start_time = '2025-06-16T23:10:14Z'
    end_time = '2025-06-16T23:26:14Z'
    # check_metric_tidb(start_time=start_time,
    #           end_time=end_time)
    # check_metric_infra(start_time=start_time,
    #           end_time=end_time)
    # result = check_metric_apm_pod_history(start_time=start_time,
    #           end_time=end_time)
    
    result =check_metric_pod_restart(start_time=start_time,end_time=end_time)               
    result =check_metric_tidb_pd(start_time=start_time,end_time=end_time)        
    result =check_metric_tidb_tikv(start_time=start_time,end_time=end_time)        
    result =check_metric_tidb_tidb(start_time=start_time,end_time=end_time)        
    result =check_metric_infra(start_time=start_time,end_time=end_time)        
    result =check_metric_infra_node(start_time=start_time,end_time=end_time)        
    result =check_metric_infra_pod(start_time=start_time,end_time=end_time)        
    result =check_metric_apm(start_time=start_time,end_time=end_time)        
    result =check_metric_apm_service_history(start_time=start_time,end_time=end_time)        
    result =check_metric_apm_pod_history(start_time=start_time,end_time=end_time)        
    result =check_node_cpu_rate(start_time=start_time,end_time=end_time)        
    result =check_node_filesystem_usage_rate(start_time=start_time,end_time=end_time)        
    result =check_metric_pod_process_abnormal(start_time=start_time,end_time=end_time)        
    result =check_node_io_util(start_time=start_time,end_time=end_time)        
    result =check_pod_network_drop_to_zero(start_time=start_time,end_time=end_time)    
      
    print(result)
    
    # import json
    # for item in result:
    #     if item['entity_name'] == "pod_cpu_usage":
    #         print(item)
    
    # for item in result:
    #     if 'entity_name' in item and 'recommendationservice' in item['entity_name'] and item['kpi'] == 'rrt':
    #         print(item)
    
    # for item in result:
    #     if item['max'] > 50:
    #         print(item)

    process_end_time = time.time()
    process_time = process_end_time - process_start_time
    print(f"日志处理结束，处理时间{process_time}s")
    

if __name__ == "__main__":
    main()
