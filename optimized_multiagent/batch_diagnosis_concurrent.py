#!/usr/bin/env python3
"""
并发批量故障诊断脚本
支持多进程并发处理，大幅提升批量诊断效率
"""
import asyncio
import json
import time
import argparse
import sys
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from optimized_fault_diagnosis import run_sync_diagnosis


def process_single_case(case_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理单个故障案例（用于进程池）- 优化错误处理"""
    case_id = case_data.get("uuid", "unknown")
    start_time = time.time()

    try:
        task_str = json.dumps(case_data, ensure_ascii=False, indent=2)
        result = run_sync_diagnosis(task_str)

        # 添加处理时间戳和元数据
        result["processed_at"] = time.time()
        result["case_id"] = case_id
        result["processing_time"] = time.time() - start_time

        # 过滤掉非关键错误
        if "error" in result and "Framework cleanup error" in result["error"]:
            # 如果只是框架清理错误，但有其他有用信息，保留结果
            if any(key in result for key in ["root_cause", "fault_component", "uuid"]):
                result.pop("error", None)  # 移除非关键错误

        return result

    except Exception as e:
        error_msg = str(e)
        # 分类错误类型
        if any(keyword in error_msg for keyword in [
            "task_done() called too many times",
            "Event loop is closed",
            "CancelledError"
        ]):
            error_type = "framework_error"
        else:
            error_type = "processing_error"

        return {
            "error": error_msg,
            "error_type": error_type,
            "uuid": case_id,
            "case_id": case_id,
            "processed_at": time.time(),
            "processing_time": time.time() - start_time
        }


def run_concurrent_batch_diagnosis(
    input_file: str,
    output_file: str,
    max_workers: int = None,
    verbose: bool = False
) -> Dict[str, Any]:
    """运行并发批量诊断"""
    
    print("🚀 并发批量故障诊断系统")
    print("=" * 50)
    
    # 读取输入文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            test_cases = json.load(f)
        print(f"✅ 成功加载 {len(test_cases)} 个测试案例")
    except Exception as e:
        print(f"❌ 读取输入文件失败: {e}")
        return {"error": f"Failed to read input file: {e}"}
    
    # 确定并发数
    if max_workers is None:
        max_workers = min(mp.cpu_count(), len(test_cases), 4)  # 最多4个进程
    
    print(f"🔧 使用 {max_workers} 个并发进程")
    
    # 开始批量处理
    start_time = time.time()
    results = []
    completed_count = 0
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_case = {
            executor.submit(process_single_case, case): case 
            for case in test_cases
        }
        
        # 收集结果
        for future in as_completed(future_to_case):
            case = future_to_case[future]
            try:
                result = future.result()
                results.append(result)
                completed_count += 1
                
                if verbose:
                    case_id = case.get("uuid", "unknown")
                    if "error" in result:
                        print(f"❌ 案例 {case_id}: {result['error']}")
                    else:
                        root_cause = result.get("root_cause", "未知")
                        component = result.get("fault_component", "未知")
                        print(f"✅ 案例 {case_id}: {component} - {root_cause[:50]}...")
                
                # 显示进度
                progress = (completed_count / len(test_cases)) * 100
                print(f"📊 进度: {completed_count}/{len(test_cases)} ({progress:.1f}%)")
                
            except Exception as e:
                case_id = case.get("uuid", "unknown")
                print(f"❌ 处理案例 {case_id} 时出错: {e}")
                results.append({
                    "error": str(e),
                    "uuid": case_id,
                    "case_id": case_id,
                    "processed_at": time.time()
                })
                completed_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 保存结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"✅ 结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")
        return {"error": f"Failed to save results: {e}"}
    
    # 统计结果
    successful_cases = [r for r in results if "error" not in r]
    failed_cases = [r for r in results if "error" in r]
    
    avg_time_per_case = total_time / len(test_cases)
    
    print("\n📊 批量处理统计")
    print("=" * 50)
    print(f"📋 总案例数: {len(test_cases)}")
    print(f"✅ 成功案例: {len(successful_cases)}")
    print(f"❌ 失败案例: {len(failed_cases)}")
    print(f"⏱️ 总耗时: {total_time:.2f}秒")
    print(f"📈 平均每案例: {avg_time_per_case:.2f}秒")
    print(f"🚀 并发效率: {max_workers}x 加速")
    
    if successful_cases:
        print(f"🎯 成功率: {len(successful_cases)/len(test_cases)*100:.1f}%")
    
    return {
        "total_cases": len(test_cases),
        "successful_cases": len(successful_cases),
        "failed_cases": len(failed_cases),
        "total_time": total_time,
        "avg_time_per_case": avg_time_per_case,
        "max_workers": max_workers,
        "success_rate": len(successful_cases)/len(test_cases)*100 if test_cases else 0
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="并发批量故障诊断")
    parser.add_argument("-i", "--input", required=True, help="输入JSON文件路径")
    parser.add_argument("-o", "--output", required=True, help="输出JSON文件路径")
    parser.add_argument("-w", "--workers", type=int, help="并发进程数（默认自动）")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 运行并发批量诊断
    stats = run_concurrent_batch_diagnosis(
        input_file=args.input,
        output_file=args.output,
        max_workers=args.workers,
        verbose=args.verbose
    )
    
    if "error" in stats:
        print(f"❌ 批量处理失败: {stats['error']}")
        sys.exit(1)
    else:
        print("\n🎉 并发批量处理完成！")


if __name__ == "__main__":
    main()
