#!/usr/bin/env python3
"""
测试工具函数的数据路径
"""
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tools.check_log import check_log
from tools.check_trace import check_trace
from tools.check_metric import check_metric_apm
from config.data_config import get_log_file_path, DATA_ROOT

def test_data_paths():
    """测试数据路径配置"""
    print("🔧 测试数据路径配置...")
    
    print(f"📁 DATA_ROOT: {DATA_ROOT}")
    
    # 测试日志路径
    log_path = get_log_file_path("2025-06-08", "20")
    print(f"📄 日志文件路径: {log_path}")
    print(f"📄 日志文件存在: {os.path.exists(log_path)}")
    
    # 检查实际存在的文件
    log_dir = os.path.join(DATA_ROOT, "2025-06-08", "log-parquet")
    if os.path.exists(log_dir):
        files = os.listdir(log_dir)
        print(f"📁 日志目录文件数: {len(files)}")
        if files:
            print(f"📄 示例文件: {files[0]}")
    else:
        print(f"❌ 日志目录不存在: {log_dir}")

def test_log_function():
    """测试日志检查函数"""
    print("\n🔧 测试日志检查函数...")
    
    try:
        result = check_log(
            start_time="2025-06-08T12:10:52Z",
            end_time="2025-06-08T12:20:52Z"
        )
        print(f"✅ 日志检查成功: {type(result)}")
        if result:
            print(f"📊 结果数量: {len(result)}")
        else:
            print("📊 无结果返回")
    except Exception as e:
        print(f"❌ 日志检查失败: {e}")
        import traceback
        traceback.print_exc()

def test_trace_function():
    """测试调用链检查函数"""
    print("\n🔧 测试调用链检查函数...")
    
    try:
        result = check_trace(
            start_time="2025-06-08T12:10:52Z",
            end_time="2025-06-08T12:20:52Z"
        )
        print(f"✅ 调用链检查成功: {type(result)}")
        if result:
            print(f"📊 结果数量: {len(result)}")
        else:
            print("📊 无结果返回")
    except Exception as e:
        print(f"❌ 调用链检查失败: {e}")
        import traceback
        traceback.print_exc()

def test_metric_function():
    """测试指标检查函数"""
    print("\n🔧 测试指标检查函数...")
    
    try:
        result = check_metric_apm(
            start_time="2025-06-08T12:10:52Z",
            end_time="2025-06-08T12:20:52Z"
        )
        print(f"✅ 指标检查成功: {type(result)}")
        if result:
            print(f"📊 结果数量: {len(result)}")
        else:
            print("📊 无结果返回")
    except Exception as e:
        print(f"❌ 指标检查失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 工具函数测试")
    print("=" * 50)
    
    # 测试数据路径
    test_data_paths()
    
    # 测试各个工具函数
    test_log_function()
    test_trace_function()
    test_metric_function()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")

if __name__ == "__main__":
    main()
