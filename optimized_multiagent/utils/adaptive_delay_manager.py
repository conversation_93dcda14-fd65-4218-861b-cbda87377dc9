"""
智能延迟管理器
用于替代硬编码的time.sleep(60)，实现基于API成功率的动态延迟策略
"""
import time
from typing import Optional


class AdaptiveDelayManager:
    """自适应延迟管理器，用于故障诊断场景的API限流控制"""
    
    def __init__(self, base_delay: float = 2.0, max_delay: float = 15.0):
        """
        初始化延迟管理器
        
        Args:
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
        """
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.last_request_time = 0.0
        self.consecutive_successes = 0
        self.consecutive_failures = 0
        self.total_requests = 0
        
    def get_delay(self) -> float:
        """
        计算当前应该延迟的时间
        
        Returns:
            延迟时间（秒）
        """
        current_time = time.time()
        elapsed_since_last = current_time - self.last_request_time
        
        # 根据成功率调整延迟策略
        if self.consecutive_successes >= 10:
            # 连续成功多次，减少延迟
            target_delay = self.base_delay * 0.5
        elif self.consecutive_successes >= 5:
            # 有一定成功率，使用基础延迟
            target_delay = self.base_delay
        elif self.consecutive_failures >= 3:
            # 连续失败，增加延迟
            target_delay = min(self.base_delay * 2, self.max_delay)
        else:
            # 默认使用基础延迟
            target_delay = self.base_delay
            
        # 如果距离上次请求时间已经足够，不需要额外延迟
        needed_delay = max(0, target_delay - elapsed_since_last)
        
        return needed_delay
    
    def record_success(self):
        """记录一次成功的API调用"""
        self.last_request_time = time.time()
        self.consecutive_successes += 1
        self.consecutive_failures = 0
        self.total_requests += 1
        
    def record_failure(self):
        """记录一次失败的API调用"""
        self.last_request_time = time.time()
        self.consecutive_failures += 1
        self.consecutive_successes = 0
        self.total_requests += 1
        
    def get_stats(self) -> dict:
        """获取延迟管理器的统计信息"""
        return {
            "total_requests": self.total_requests,
            "consecutive_successes": self.consecutive_successes,
            "consecutive_failures": self.consecutive_failures,
            "last_request_time": self.last_request_time,
            "current_delay": self.get_delay()
        }
    
    def reset(self):
        """重置延迟管理器状态"""
        self.last_request_time = 0.0
        self.consecutive_successes = 0
        self.consecutive_failures = 0
        self.total_requests = 0


class FaultDiagnosisDelayManager(AdaptiveDelayManager):
    """专门为故障诊断场景优化的延迟管理器"""
    
    def __init__(self):
        # 故障诊断场景的特殊配置
        super().__init__(base_delay=3.0, max_delay=12.0)
        
    def get_delay(self) -> float:
        """
        故障诊断专用的延迟计算逻辑
        考虑到故障诊断的紧急性，延迟策略更加激进
        """
        current_time = time.time()
        elapsed_since_last = current_time - self.last_request_time
        
        # 故障诊断场景下的延迟策略
        if self.consecutive_successes >= 8:
            # 连续成功，最小延迟
            target_delay = 1.0
        elif self.consecutive_successes >= 3:
            # 有成功记录，使用较小延迟
            target_delay = 2.0
        elif self.consecutive_failures >= 2:
            # 有失败记录，增加延迟但不过度
            target_delay = min(6.0, self.base_delay * 1.5)
        else:
            # 初始状态，使用基础延迟
            target_delay = self.base_delay
            
        needed_delay = max(0, target_delay - elapsed_since_last)
        return needed_delay
