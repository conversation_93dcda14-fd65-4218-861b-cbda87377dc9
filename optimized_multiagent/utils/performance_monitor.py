"""
性能监控工具
监控系统各个组件的性能指标
"""
import time
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import threading

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    component: str
    operation: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics: List[PerformanceMetrics] = []
        self.active_operations: Dict[str, PerformanceMetrics] = {}
        self.component_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            "total_calls": 0,
            "total_duration": 0.0,
            "success_count": 0,
            "error_count": 0,
            "avg_duration": 0.0,
            "recent_durations": deque(maxlen=10)
        })
        self._lock = threading.Lock()
    
    def start_operation(self, component: str, operation: str, metadata: Dict[str, Any] = None) -> str:
        """开始监控一个操作"""
        operation_id = f"{component}_{operation}_{time.time()}"
        
        metric = PerformanceMetrics(
            component=component,
            operation=operation,
            start_time=time.time(),
            metadata=metadata or {}
        )
        
        with self._lock:
            self.active_operations[operation_id] = metric
        
        print(f"⏱️ [性能监控] 开始 {component}.{operation}")
        return operation_id
    
    def end_operation(self, operation_id: str, success: bool = True, error_message: str = None):
        """结束监控一个操作"""
        with self._lock:
            if operation_id not in self.active_operations:
                print(f"⚠️ [性能监控] 未找到操作ID: {operation_id}")
                return
            
            metric = self.active_operations.pop(operation_id)
            metric.end_time = time.time()
            metric.duration = metric.end_time - metric.start_time
            metric.success = success
            metric.error_message = error_message
            
            # 添加到历史记录
            self.metrics.append(metric)
            if len(self.metrics) > self.max_history:
                self.metrics.pop(0)
            
            # 更新统计信息
            stats = self.component_stats[metric.component]
            stats["total_calls"] += 1
            stats["total_duration"] += metric.duration
            stats["recent_durations"].append(metric.duration)
            
            if success:
                stats["success_count"] += 1
            else:
                stats["error_count"] += 1
            
            stats["avg_duration"] = stats["total_duration"] / stats["total_calls"]
            
            # 打印性能日志
            status = "✅" if success else "❌"
            print(f"{status} [性能监控] 完成 {metric.component}.{metric.operation} - {metric.duration:.2f}秒")
            
            # 检查是否为慢操作
            if metric.duration > 10.0:
                print(f"🐌 [性能警告] 慢操作检测: {metric.component}.{metric.operation} 耗时 {metric.duration:.2f}秒")
    
    def get_component_stats(self, component: str) -> Dict[str, Any]:
        """获取组件统计信息"""
        with self._lock:
            return self.component_stats[component].copy()
    
    def get_recent_performance(self, component: str = None, limit: int = 10) -> List[PerformanceMetrics]:
        """获取最近的性能数据"""
        with self._lock:
            if component:
                filtered = [m for m in self.metrics if m.component == component]
                return filtered[-limit:]
            else:
                return self.metrics[-limit:]
    
    def print_performance_summary(self):
        """打印性能摘要"""
        print("\n📊 性能监控摘要")
        print("=" * 50)
        
        with self._lock:
            for component, stats in self.component_stats.items():
                success_rate = (stats["success_count"] / stats["total_calls"] * 100) if stats["total_calls"] > 0 else 0
                recent_avg = sum(stats["recent_durations"]) / len(stats["recent_durations"]) if stats["recent_durations"] else 0
                
                print(f"🔧 {component}:")
                print(f"  📞 总调用: {stats['total_calls']}")
                print(f"  ⏱️ 平均耗时: {stats['avg_duration']:.2f}秒")
                print(f"  🎯 最近平均: {recent_avg:.2f}秒")
                print(f"  ✅ 成功率: {success_rate:.1f}%")
                print(f"  ❌ 错误数: {stats['error_count']}")
                print()
    
    def get_bottlenecks(self, threshold: float = 5.0) -> List[Dict[str, Any]]:
        """识别性能瓶颈"""
        bottlenecks = []
        
        with self._lock:
            for component, stats in self.component_stats.items():
                if stats["avg_duration"] > threshold:
                    bottlenecks.append({
                        "component": component,
                        "avg_duration": stats["avg_duration"],
                        "total_calls": stats["total_calls"],
                        "error_rate": stats["error_count"] / stats["total_calls"] if stats["total_calls"] > 0 else 0
                    })
        
        return sorted(bottlenecks, key=lambda x: x["avg_duration"], reverse=True)

# 全局性能监控器实例
global_monitor = PerformanceMonitor()

class PerformanceContext:
    """性能监控上下文管理器"""
    
    def __init__(self, component: str, operation: str, metadata: Dict[str, Any] = None):
        self.component = component
        self.operation = operation
        self.metadata = metadata or {}
        self.operation_id = None
    
    def __enter__(self):
        self.operation_id = global_monitor.start_operation(
            self.component, self.operation, self.metadata
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        success = exc_type is None
        error_message = str(exc_val) if exc_val else None
        global_monitor.end_operation(self.operation_id, success, error_message)

class AsyncPerformanceContext:
    """异步性能监控上下文管理器"""
    
    def __init__(self, component: str, operation: str, metadata: Dict[str, Any] = None):
        self.component = component
        self.operation = operation
        self.metadata = metadata or {}
        self.operation_id = None
    
    async def __aenter__(self):
        self.operation_id = global_monitor.start_operation(
            self.component, self.operation, self.metadata
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        success = exc_type is None
        error_message = str(exc_val) if exc_val else None
        global_monitor.end_operation(self.operation_id, success, error_message)

def monitor_performance(component: str, operation: str, metadata: Dict[str, Any] = None):
    """性能监控装饰器"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                async with AsyncPerformanceContext(component, operation, metadata):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                with PerformanceContext(component, operation, metadata):
                    return func(*args, **kwargs)
            return sync_wrapper
    return decorator

def print_performance_report():
    """打印性能报告"""
    global_monitor.print_performance_summary()
    
    bottlenecks = global_monitor.get_bottlenecks()
    if bottlenecks:
        print("🚨 性能瓶颈识别:")
        for i, bottleneck in enumerate(bottlenecks[:5], 1):
            print(f"  {i}. {bottleneck['component']}: {bottleneck['avg_duration']:.2f}秒 "
                  f"(调用{bottleneck['total_calls']}次, 错误率{bottleneck['error_rate']:.1%})")
    else:
        print("✅ 未发现明显性能瓶颈")
