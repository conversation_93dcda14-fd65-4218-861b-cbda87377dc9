"""
优化的故障诊断系统
使用SelectorGroupChat架构替代RoundRobinGroupChat，提升性能和智能化程度
"""
import asyncio
import json
import time
import re
from typing import Dict, Any, Optional
from concurrent.futures import ProcessPoolExecutor

from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.ui import Console

from agents.agent_factory import create_all_agents
from config.selector_config import get_selector_config
# 移除复杂的延迟管理器，使用简单的固定延迟


class OptimizedFaultDiagnosisSystem:
    """优化的故障诊断系统"""
    
    def __init__(self):
        self.agents = None
        self.team = None
        
    def _extract_json_from_content(self, content: str) -> Optional[Dict]:
        """从Agent响应中提取JSON内容"""
        if not isinstance(content, str):
            return None
            
        # 匹配 ```json ... ``` 或 ``` ... ``` 或直接是 json
        match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", content)
        if match:
            json_str = match.group(1)
        else:
            json_str = content
            
        try:
            return json.loads(json_str)
        except Exception as e:
            print(f"JSON解析失败: {e}, 原始内容: {json_str[:200]}...")
            return None
    
    def _initialize_agents(self):
        """初始化所有Agent"""
        if self.agents is None:
            print("🔧 初始化Agent...")
            self.agents = create_all_agents()
            print("✅ Agent初始化完成")
    
    def _create_team(self):
        """创建SelectorGroupChat团队"""
        if self.team is None:
            self._initialize_agents()

            print("🎯 开始创建SelectorGroupChat团队...")
            config = get_selector_config()

            participants = [
                self.agents["info_agent"],
                self.agents["log_agent"],
                self.agents["trace_agent"],
                self.agents["metric_agent"],
                self.agents["tidb_agent"],
                self.agents["analyze_agent"]
            ]

            print(f"👥 参与者列表: {[agent.name for agent in participants]}")
            print(f"🎛️ 最大轮次: {config['max_turns']}")
            print(f"📏 选择器提示词长度: {len(config['selector_prompt'])}字符")

            # 创建SelectorGroupChat
            print("🔧 正在创建SelectorGroupChat实例...")
            self.team = SelectorGroupChat(
                participants=participants,
                model_client=self.agents["model_client"],
                selector_prompt=config["selector_prompt"],
                termination_condition=config["termination_condition"],
                max_turns=config["max_turns"]
            )
            print("✅ SelectorGroupChat团队创建完成")
            print(f"🎯 团队配置: {len(participants)}个Agent, 最大{config['max_turns']}轮对话")
    
    async def diagnose_single_case(self, task: str) -> Dict[str, Any]:
        """诊断单个故障案例"""
        print(f"🔍 开始诊断案例...")
        
        # 应用简化的延迟策略（避免API限流）
        delay = 2.0  # 固定2秒延迟，避免API限流
        print(f"⏱️ 应用延迟: {delay}秒")
        await asyncio.sleep(delay)
        
        try:
            # 创建团队（如果还没创建）
            self._create_team()
            
            # 重置团队状态
            await self.team.reset()
            
            # 运行诊断
            start_time = time.time()
            analyze_results = {}
            
            print("🚀 启动智能故障诊断...")

            # 使用与原系统相同的流式处理方式
            stream = self.team.run_stream(task=task)

            async for message in stream:
                print(f"📨 收到消息: {message}")

                # 使用与原系统相同的结果提取逻辑
                if hasattr(message, "source") and message.source == "analyze_assistant":
                    result = self._extract_json_from_content(message.content)
                    if result and "uuid" in result:
                        analyze_results = result
                        print("✅ analyze_agent 结果已保存:", result)
                        break
                    else:
                        print("⚠️ 解析analyze_agent结果失败: 未能提取有效JSON或缺少uuid")

                if hasattr(message, "messages"):
                    for msg in message.messages:
                        if hasattr(msg, "source") and msg.source == "analyze_assistant":
                            result = self._extract_json_from_content(msg.content)
                            if result and "uuid" in result:
                                analyze_results = result
                                print("✅ analyze_agent 结果已保存:", result)
                                break
                            else:
                                print("⚠️ 解析analyze_agent结果失败: 未能提取有效JSON或缺少uuid")
                # 检查是否是最终分析结果
                if hasattr(message, "source") and message.source == "analyze_assistant":
                    result = self._extract_json_from_content(message.content)
                    if result and "uuid" in result:
                        analyze_results = result
                        print(f"✅ 获得分析结果: {result}")
                        break
                
                # 检查消息集合中的分析结果
                if hasattr(message, "messages"):
                    for msg in message.messages:
                        if hasattr(msg, "source") and msg.source == "analyze_assistant":
                            result = self._extract_json_from_content(msg.content)
                            if result and "uuid" in result:
                                analyze_results = result
                                print(f"✅ 获得分析结果: {result}")
                                break
            
            end_time = time.time()
            cost_time = end_time - start_time
            
            print(f"✅ 诊断完成，耗时: {cost_time:.2f}秒")

            # 添加性能统计
            analyze_results["performance"] = {
                "cost_time": cost_time
            }
            
            return analyze_results
            
        except Exception as e:
            print(f"❌ 诊断失败: {e}")
            return {"error": str(e), "uuid": "unknown"}
    
    async def close(self):
        """关闭系统资源"""
        if self.agents and "model_client" in self.agents:
            await self.agents["model_client"].close()
            print("🔒 模型客户端已关闭")


def run_sync_diagnosis(task: str) -> Dict[str, Any]:
    """同步运行诊断（用于进程池）"""
    system = OptimizedFaultDiagnosisSystem()
    try:
        result = asyncio.run(system.diagnose_single_case(task))
        asyncio.run(system.close())
        return result
    except Exception as e:
        return {"error": str(e), "uuid": "unknown"}


async def run_single_diagnosis(task: str) -> Dict[str, Any]:
    """运行单个诊断案例"""
    system = OptimizedFaultDiagnosisSystem()
    try:
        result = await system.diagnose_single_case(task)
        return result
    finally:
        await system.close()


async def run_batch_diagnosis(input_file: str, output_file: str, max_workers: int = 3):
    """运行批量诊断"""
    print(f"📊 开始批量诊断，最大并发数: {max_workers}")
    
    # 读取输入数据
    with open(input_file, 'r', encoding="UTF-8") as f:
        data = json.load(f)
    
    tasks = [json.dumps(item, ensure_ascii=False, indent=2) for item in data]
    total_results = []
    
    program_start_time = time.time()
    
    # 使用进程池进行并发处理
    with ProcessPoolExecutor(max_workers=max_workers) as executor, \
         open(output_file, 'w', encoding="UTF-8") as result_file:
        
        futures = []
        
        for i, task in enumerate(tasks):
            print(f"📝 提交任务 {i+1}/{len(tasks)}")
            future = executor.submit(run_sync_diagnosis, task)
            futures.append((future, time.time()))
            
            # 控制并发数
            if len(futures) == max_workers or i == len(tasks) - 1:
                for fut, start_time in futures:
                    result = fut.result()
                    total_results.append(result)
                    
                    # 立即写入结果
                    result_file.write(json.dumps(result, ensure_ascii=False))
                    result_file.write('\n')
                    result_file.flush()
                    
                    end_time = time.time()
                    cost_time = end_time - start_time
                    print(f"✅ 任务完成，耗时: {cost_time:.2f}秒")
                
                futures = []
    
    program_end_time = time.time()
    total_time = program_end_time - program_start_time
    
    print(f"🎉 批量诊断完成！")
    print(f"📊 总任务数: {len(tasks)}")
    print(f"⏱️ 总耗时: {total_time:.2f}秒")
    print(f"📈 平均耗时: {total_time/len(tasks):.2f}秒/任务")
    
    return total_results


if __name__ == "__main__":
    # 示例：单个案例诊断
    sample_task = {
        "Anomaly Description": "The system experienced an anomaly from 2025-06-05T16:10:02Z to 2025-06-05T16:31:02Z. Please infer the possible cause.",
        "uuid": "345fbe93-80"
    }
    
    task_str = json.dumps(sample_task, ensure_ascii=False, indent=2)
    
    print("🔍 运行单个诊断示例...")
    result = asyncio.run(run_single_diagnosis(task_str))
    print(f"📋 诊断结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
