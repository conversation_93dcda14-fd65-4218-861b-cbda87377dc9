"""
批量故障诊断脚本
支持命令行参数，用于批量处理故障案例
"""
import argparse
import asyncio
import json
import sys
import os
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from optimized_fault_diagnosis import run_batch_diagnosis, run_single_diagnosis


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="优化的多智能体故障诊断系统")
    
    parser.add_argument(
        "-i", "--input",
        type=str,
        required=True,
        help="输入文件路径（JSON格式）"
    )
    
    parser.add_argument(
        "-o", "--output", 
        type=str,
        required=True,
        help="输出文件路径"
    )
    
    parser.add_argument(
        "--max-workers",
        type=int,
        default=3,
        help="最大并发进程数（默认：3）"
    )
    
    parser.add_argument(
        "--single",
        action="store_true",
        help="单案例模式（用于测试）"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true", 
        help="详细输出模式"
    )
    
    return parser.parse_args()


def validate_input_file(input_file: str) -> bool:
    """验证输入文件"""
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        if not isinstance(data, list):
            print(f"❌ 输入文件格式错误：应该是JSON数组")
            return False
            
        if len(data) == 0:
            print(f"❌ 输入文件为空")
            return False
            
        print(f"✅ 输入文件验证通过，包含 {len(data)} 个案例")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 输入文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取输入文件失败: {e}")
        return False


async def run_single_case_test(input_file: str, output_file: str):
    """运行单案例测试"""
    print("🧪 单案例测试模式")
    
    # 读取第一个案例
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if not data:
        print("❌ 没有可测试的案例")
        return
    
    first_case = data[0]
    task_str = json.dumps(first_case, ensure_ascii=False, indent=2)
    
    print(f"📝 测试案例: {first_case.get('uuid', 'unknown')}")
    
    # 运行诊断
    result = await run_single_diagnosis(task_str)
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 测试完成，结果已保存到: {output_file}")
    
    # 显示结果摘要
    if "error" in result:
        print(f"❌ 诊断失败: {result['error']}")
    else:
        print(f"🎯 根因: {result.get('root_cause', '未知')}")
        print(f"🔧 故障组件: {result.get('fault_component', '未知')}")
        if "performance" in result:
            perf = result["performance"]
            print(f"⏱️ 耗时: {perf.get('cost_time', 0):.2f}秒")


def create_output_directory(output_file: str):
    """创建输出目录"""
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")


async def main():
    """主函数"""
    args = parse_arguments()
    
    print("🚀 优化的多智能体故障诊断系统")
    print("=" * 50)
    
    # 验证输入文件
    if not validate_input_file(args.input):
        sys.exit(1)
    
    # 创建输出目录
    create_output_directory(args.output)
    
    try:
        if args.single:
            # 单案例测试模式
            await run_single_case_test(args.input, args.output)
        else:
            # 批量处理模式
            print(f"📊 批量处理模式")
            print(f"📥 输入文件: {args.input}")
            print(f"📤 输出文件: {args.output}")
            print(f"🔄 最大并发数: {args.max_workers}")
            print("-" * 50)
            
            results = await run_batch_diagnosis(
                input_file=args.input,
                output_file=args.output,
                max_workers=args.max_workers
            )
            
            # 统计结果
            total_cases = len(results)
            successful_cases = len([r for r in results if "error" not in r])
            failed_cases = total_cases - successful_cases
            
            print("\n📈 处理统计:")
            print(f"  总案例数: {total_cases}")
            print(f"  成功案例: {successful_cases}")
            print(f"  失败案例: {failed_cases}")
            print(f"  成功率: {successful_cases/total_cases*100:.1f}%")
            
            if args.verbose and results:
                print("\n📋 性能统计:")
                times = [r.get("performance", {}).get("cost_time", 0) for r in results if "performance" in r]
                if times:
                    avg_time = sum(times) / len(times)
                    min_time = min(times)
                    max_time = max(times)
                    print(f"  平均耗时: {avg_time:.2f}秒")
                    print(f"  最短耗时: {min_time:.2f}秒")
                    print(f"  最长耗时: {max_time:.2f}秒")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 运行失败: {e}")
        sys.exit(1)
    
    print("\n🎉 任务完成！")


if __name__ == "__main__":
    asyncio.run(main())
