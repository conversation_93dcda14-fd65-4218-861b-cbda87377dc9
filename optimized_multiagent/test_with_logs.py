#!/usr/bin/env python3
"""
带详细日志的故障诊断测试
"""
import asyncio
import json
import time
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from optimized_fault_diagnosis import OptimizedFaultDiagnosisSystem


async def test_with_detailed_logs():
    """带详细日志的测试"""
    print("🚀 开始带详细日志的故障诊断测试")
    print("=" * 60)
    
    # 测试案例
    test_case = {
        "Anomaly Description": "A fault was detected from 2025-06-08T12:10:52Z to 2025-06-08T12:20:52Z. Please analyze its root cause.",
        "uuid": "ad13b37a-146"
    }
    
    print(f"📋 测试案例: {test_case['uuid']}")
    print(f"📝 故障描述: {test_case['Anomaly Description']}")
    print()
    
    # 创建诊断系统
    print("🔧 创建故障诊断系统...")
    system = OptimizedFaultDiagnosisSystem()
    
    # 执行诊断
    print("\n🚀 开始执行故障诊断...")
    start_time = time.time()
    
    try:
        task_str = json.dumps(test_case, ensure_ascii=False, indent=2)
        result = await system.diagnose_single_case(task_str)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎉 诊断完成！")
        print(f"⏱️ 总耗时: {duration:.2f}秒")
        print(f"📊 结果类型: {type(result)}")
        
        if result:
            print(f"✅ 诊断成功")
            if isinstance(result, dict):
                print(f"🔍 结果键: {list(result.keys())}")
                if "uuid" in result:
                    print(f"📝 UUID: {result['uuid']}")
                if "root_cause" in result:
                    print(f"🎯 根因: {result['root_cause'][:200]}...")
            else:
                print(f"📄 结果内容: {str(result)[:300]}...")
        else:
            print("⚠️ 诊断结果为空")
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"\n❌ 诊断失败！")
        print(f"⏱️ 失败前耗时: {duration:.2f}秒")
        print(f"🔥 错误信息: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if hasattr(system, 'agents') and system.agents and "model_client" in system.agents:
                await system.agents["model_client"].close()
                print("🔒 模型客户端已关闭")
        except Exception as e:
            print(f"⚠️ 清理资源时出错: {e}")


async def test_simple_agent():
    """测试单个Agent"""
    print("\n🧪 测试单个Agent...")
    
    try:
        from agents.agent_factory import create_model_client, create_info_agent
        
        print("📡 创建模型客户端...")
        model_client = create_model_client()
        
        print("🤖 创建信息提取Agent...")
        info_agent = create_info_agent(model_client)
        
        print("💬 测试Agent调用...")
        simple_task = "Extract time from: A fault occurred from 2025-06-08T12:10:52Z to 2025-06-08T12:20:52Z"

        start_time = time.time()
        # 使用正确的方式调用Agent
        from autogen_agentchat.messages import TextMessage
        message = TextMessage(content=simple_task, source="user")
        result = await info_agent.on_messages([message], cancellation_token=None)
        end_time = time.time()
        
        print(f"✅ Agent调用成功，耗时: {end_time - start_time:.2f}秒")
        print(f"📋 结果: {result}")
        
        await model_client.close()
        print("🔒 模型客户端已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 单Agent测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🔬 故障诊断系统详细日志测试")
    print("=" * 60)
    
    # 先测试单个Agent
    print("📋 步骤1: 测试单个Agent")
    agent_ok = await test_simple_agent()
    
    if agent_ok:
        print("\n📋 步骤2: 测试完整系统")
        await test_with_detailed_logs()
    else:
        print("\n⚠️ 单Agent测试失败，跳过完整系统测试")
    
    print("\n🎯 测试完成")


if __name__ == "__main__":
    asyncio.run(main())
