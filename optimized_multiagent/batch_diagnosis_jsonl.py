#!/usr/bin/env python3
"""
批量故障诊断脚本 - JSONL格式输出
参考原项目格式，每完成一个案例立即写入文件
"""
import asyncio
import json
import time
import argparse
import sys
import os
from pathlib import Path
from typing import Dict, Any, List
from concurrent.futures import ProcessPoolExecutor

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from optimized_fault_diagnosis import run_sync_diagnosis


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="批量故障诊断 - JSONL格式输出")
    
    parser.add_argument(
        "-i", "--input",
        type=str,
        required=True,
        help="输入JSON文件路径"
    )
    
    parser.add_argument(
        "-o", "--output", 
        type=str,
        required=True,
        help="输出JSONL文件路径"
    )
    
    parser.add_argument(
        "--max-workers",
        type=int,
        default=3,
        help="最大并发进程数（默认：3）"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true", 
        help="详细输出模式"
    )
    
    parser.add_argument(
        "--delay",
        type=float,
        default=2.0,
        help="任务间延迟时间（秒，默认：2.0）"
    )
    
    return parser.parse_args()


def run_batch_diagnosis_jsonl(input_file: str, output_file: str, max_workers: int = 3, verbose: bool = False, delay: float = 2.0):
    """运行批量诊断 - JSONL格式输出，参考原项目"""
    print("🚀 优化的多智能体故障诊断系统 - 批量模式（JSONL格式）")
    print("=" * 60)
    
    # 读取输入文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            test_cases = json.load(f)
        print(f"✅ 输入文件验证通过，包含 {len(test_cases)} 个案例")
    except Exception as e:
        print(f"❌ 读取输入文件失败: {e}")
        return []
    
    program_start_time = time.time()
    total_result = []
    
    # 参考原项目的批量处理方式
    tasks = [json.dumps(item, ensure_ascii=False, indent=2) for item in test_cases]
    
    print(f"📊 处理配置:")
    print(f"  最大并发数: {max_workers}")
    print(f"  任务间延迟: {delay}秒")
    print(f"  输出格式: JSONL (每行一个JSON)")
    print("-" * 60)
    
    # 使用进程池并发，参考原项目的实现
    with ProcessPoolExecutor(max_workers=max_workers) as executor, open(output_file, 'w', encoding="utf-8") as result_file:
        futures = []
        
        for i, task in enumerate(tasks):
            start_time = time.time()
            future = executor.submit(run_sync_diagnosis, task)
            futures.append((future, start_time, test_cases[i]))
            
            # 控制并发数，或者最后一批任务
            if len(futures) == max_workers or i == len(tasks) - 1:
                for fut, st, original_case in futures:
                    try:
                        result = fut.result()
                        
                        # 每完成一个任务就立即写入文件，不需要等全部任务结束
                        result_file.write(json.dumps(result, ensure_ascii=False))
                        result_file.write('\n')
                        result_file.flush()  # 确保立即写入磁盘
                        
                        total_result.append(result)
                        end_time = time.time()
                        cost_time = end_time - st
                        
                        # 输出处理结果
                        case_id = original_case.get("uuid", "unknown")
                        
                        if "error" in result:
                            print(f"❌ 案例 {case_id}: {result['error']}")
                        else:
                            root_cause = result.get("root_cause", "未知")
                            component = result.get("fault_component", "未知")
                            confidence = result.get("confidence", "未知")
                            
                            print(f"✅ 案例 {case_id}:")
                            print(f"   🎯 根因: {root_cause}")
                            print(f"   🔧 故障组件: {component}")
                            print(f"   📊 置信度: {confidence}")
                        
                        if verbose:
                            print(f"📋 完整结果: {json.dumps(result, ensure_ascii=False)}")
                        
                        print(f"本次任务完成，共花费时间{cost_time:.2f}s")
                        print("-" * 40)
                        
                        # 添加延迟避免API限流
                        if delay > 0:
                            time.sleep(delay)
                        
                    except Exception as e:
                        case_id = original_case.get("uuid", "unknown")
                        error_result = {"error": str(e), "uuid": case_id}
                        
                        # 写入错误结果
                        result_file.write(json.dumps(error_result, ensure_ascii=False))
                        result_file.write('\n')
                        result_file.flush()
                        
                        total_result.append(error_result)
                        end_time = time.time()
                        cost_time = end_time - st
                        print(f"❌ 处理案例 {case_id} 时出错: {e}")
                        print(f"本次任务失败，共花费时间{cost_time:.2f}s")
                        print("-" * 40)
                
                futures = []
    
    program_end_time = time.time()
    total_program_time = program_end_time - program_start_time
    
    # 统计结果
    successful_cases = [r for r in total_result if "error" not in r]
    failed_cases = [r for r in total_result if "error" in r]
    
    print("=" * 60)
    print("📊 批量处理完成统计")
    print("=" * 60)
    print(f"📋 总案例数: {len(test_cases)}")
    print(f"✅ 成功案例: {len(successful_cases)}")
    print(f"❌ 失败案例: {len(failed_cases)}")
    print(f"🎯 成功率: {len(successful_cases)/len(test_cases)*100:.1f}%")
    print(f"⏱️ 总耗时: {total_program_time:.2f}秒")
    print(f"📈 平均每案例: {total_program_time/len(test_cases):.2f}秒")
    print(f"📁 结果已保存到: {output_file}")
    print(f"📄 输出格式: JSONL (每行一个JSON对象)")
    
    if successful_cases:
        # 计算性能统计
        times = []
        for result in successful_cases:
            if "performance" in result and "cost_time" in result["performance"]:
                times.append(result["performance"]["cost_time"])
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            print(f"\n📊 诊断性能统计:")
            print(f"   平均诊断时间: {avg_time:.2f}秒")
            print(f"   最快诊断时间: {min_time:.2f}秒")
            print(f"   最慢诊断时间: {max_time:.2f}秒")
    
    return total_result


def main():
    """主函数"""
    args = parse_arguments()
    
    # 验证输入文件
    if not os.path.exists(args.input):
        print(f"❌ 输入文件不存在: {args.input}")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")
    
    # 运行批量诊断
    results = run_batch_diagnosis_jsonl(
        input_file=args.input,
        output_file=args.output,
        max_workers=args.max_workers,
        verbose=args.verbose,
        delay=args.delay
    )
    
    print("\n🎉 批量处理任务完成！")


if __name__ == "__main__":
    main()
