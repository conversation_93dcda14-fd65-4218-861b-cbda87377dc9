import pyarrow.parquet as pq
import pandas as pd
import numpy as np
import json
import sys
import ast

def convert_ndarray(obj):
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: convert_ndarray(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_ndarray(i) for i in obj]
    else:
        return obj

def parse_tags(tags):
    # tags 可能是字符串、list、ndarray
    if isinstance(tags, str):
        try:
            tags = ast.literal_eval(tags)
        except Exception:
            return []
    if isinstance(tags, np.ndarray):
        tags = tags.tolist()
    if not isinstance(tags, list):
        return []
    result = []
    for tag in tags:
        if isinstance(tag, dict):
            result.append(tag)
        elif isinstance(tag, str):
            try:
                tag_obj = ast.literal_eval(tag)
                if isinstance(tag_obj, dict):
                    result.append(tag_obj)
            except Exception:
                continue
    return result

def parse_logs(logs):
    # logs 可能是字符串、list、ndarray
    if isinstance(logs, str):
        try:
            logs = ast.literal_eval(logs)
        except Exception:
            return []
    if isinstance(logs, np.ndarray):
        logs = logs.tolist()
    if not isinstance(logs, list):
        return []
    result = []
    for log in logs:
        if isinstance(log, dict):
            # fields 可能是ndarray
            if 'fields' in log and isinstance(log['fields'], np.ndarray):
                log['fields'] = log['fields'].tolist()
            result.append(log)
        elif isinstance(log, str):
            try:
                log_obj = ast.literal_eval(log)
                if isinstance(log_obj, dict):
                    if 'fields' in log_obj and isinstance(log_obj['fields'], np.ndarray):
                        log_obj['fields'] = log_obj['fields'].tolist()
                    result.append(log_obj)
            except Exception:
                continue
    return result

def parse_process(process):
    # process 可能是字符串或dict
    if isinstance(process, str):
        try:
            process = ast.literal_eval(process)
        except Exception:
            return {"serviceName": "unknown", "tags": []}
    if not isinstance(process, dict):
        return {"serviceName": "unknown", "tags": []}
    # tags 可能是字符串
    tags = process.get('tags', [])
    tags = parse_tags(tags)
    return {"serviceName": process.get('serviceName', 'unknown'), "tags": tags}

def parse_references(refs):
    # refs 可能是字符串、list、ndarray
    if isinstance(refs, str):
        try:
            refs = ast.literal_eval(refs)
        except Exception:
            return []
    if isinstance(refs, np.ndarray):
        refs = refs.tolist()
    if not isinstance(refs, list):
        return []
    result = []
    for ref in refs:
        if isinstance(ref, dict):
            result.append(ref)
        elif isinstance(ref, str):
            try:
                ref_obj = ast.literal_eval(ref)
                if isinstance(ref_obj, dict):
                    result.append(ref_obj)
            except Exception:
                continue
    return result

def main(parquet_file, output_file, ts_start_time, ts_end_time):
    df = pq.read_table(parquet_file).to_pandas()
    total_rows = len(df)
    print(f"[提示] 数据总行数为 {total_rows}，全部导出。")
    print(f"[提示] 数据列名为: {df.columns.tolist()}")

    # print(f"[提示] 数据总行数为 {total_rows}，全部导出。")
    traces = {}
    process_id_map = {}
    process_counter = 1

    print(f"[提示] 开始处理，开始时间：{ts_start_time}, 结束时间：{ts_end_time}")
    # for idx, (_, row) in enumerate(df.iterrows()):
    #     # 需要使用毫秒级时间戳
    #     time = row.get('startTimeMillis')

    for idx, (_, row) in enumerate(df.iterrows()):
        # INSERT_YOUR_CODE
        # 增加时间范围判断，time字段小于ts_start_time则跳过，大于ts_end_time则结束
        time_val = row.get('time')
        if time_val is None:
            time_val = row.get('startTimeMillis')
        if time_val is None:
            # 如果没有时间字段，跳过
            continue
        try:
            time_val = int(time_val)
        except Exception:
            try:
                # 如果是字符串时间，尝试转为时间戳（毫秒）
                time_val = int(pd.to_datetime(time_val).timestamp() * 1000)
            except Exception:
                continue
        if time_val < ts_start_time:
            continue
        if time_val > ts_end_time:
            print(f"检测到 time 超过 ts_end_time，提前结束程序。当前 time: {time_val}, ts_end_time: {ts_end_time}")
            break

        trace_id = row.get('traceID')
        span_id = row.get('spanID')
        print(f"当前行: {idx},当前traceid: {trace_id},spanid: {span_id}")
        if trace_id not in traces:
            traces[trace_id] = {
                "traceID": trace_id,
                "spans": [],
                "processes": {},
                "warnings": None
            }

        # 处理 process
        process = parse_process(row.get('process', {}))
        process_key = json.dumps(process, sort_keys=True, default=str)
        # 修复 bug：processes 需要按 traceID 记录，避免跨 trace 丢失
        if process_key not in process_id_map:
            process_id = f"p{process_counter}"
            process_id_map[process_key] = process_id
            process_counter += 1
        else:
            process_id = process_id_map[process_key]

        # 确保每个 trace 下的 processes 都有对应的 process_id
        if process_id not in traces[trace_id]["processes"]:
            traces[trace_id]["processes"][process_id] = process

        # 处理 tags
        tags = parse_tags(row.get('tags', []))
        # 处理 logs
        logs = parse_logs(row.get('logs', []))
        # 处理 references
        references = parse_references(row.get('references', []))
        # 计算 relativeStartTime
        # jaeger时间需要使用startTime，不然无法识别
        start_time = row.get('startTime')
        all_start_times = df[df['traceID'] == trace_id]['startTime']
        min_start_time = all_start_times.min() if not all_start_times.empty else 0
        relative_start_time = start_time - min_start_time if start_time is not None else 0
        # 计算 depth、hasChildren、childSpanIds
        
        child_span_ids = []
        for _, r in df[df['traceID'] == trace_id].iterrows():
            refs = parse_references(r.get('references', []))
            for ref in refs:
                if ref.get('spanID') == span_id:
                    child_span_ids.append(r.get('spanID'))
        has_children = len(child_span_ids) > 0
        depth = 0
        refs = parse_references(row.get('references', []))
        if isinstance(refs, list) and len(refs) > 0:
            depth = 1
        # 构造 span
        span = {
            "traceID": trace_id,
            "spanID": span_id,
            "operationName": row.get('operationName'),
            "references": references,
            "startTime": start_time,
            "duration": row.get('duration'),
            "tags": tags,
            "logs": logs,
            "processID": process_id,
            "warnings": [],
            "process": process,
            "relativeStartTime": relative_start_time,
            "depth": depth,
            "hasChildren": has_children,
            "childSpanIds": child_span_ids
        }
        traces[trace_id]["spans"].append(span)
    result = {"data": list(traces.values())}
    result = convert_ndarray(result)
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2, default=str)
    print(f"导出完成，文件名：{output_file}")

if __name__ == "__main__":
    import pytz
    from datetime import datetime, timedelta

    def utc_to_cst(utc_str):
        # 解析UTC时间字符串，转换为CST
        utc_dt = datetime.strptime(utc_str, "%Y-%m-%dT%H:%M:%SZ")
        utc_dt = utc_dt.replace(tzinfo=pytz.utc)
        cst_dt = utc_dt.astimezone(pytz.timezone("Asia/Shanghai"))
        return cst_dt

    if len(sys.argv) < 2:
        print("用法: python parquet_to_jaeger_json.py <anomaly_times.txt路径>")
        sys.exit(1)
    anomaly_file = sys.argv[1]

    with open(anomaly_file, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith("#"):
                continue
            parts = [x.strip() for x in line.split(",")]
            if len(parts) < 3:
                print(f"当前时间格式异常，请检查{line}")
                continue
            uuid, start_time_utc, end_time_utc = parts[0], parts[1], parts[2]
            start_cst = utc_to_cst(start_time_utc)
            end_cst = utc_to_cst(end_time_utc)
            # 获取日期范围
            current = start_cst.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = end_cst.replace(hour=0, minute=0, second=0, microsecond=0)
            while current <= end_date:
                
                # 记录文件名称
                parquet_file=""
                date_str = current.strftime("%Y-%m-%d")
                # 计算该日期下需要的小时区间
                if current.date() == start_cst.date():
                    start_hour = start_cst.hour
                else:
                    start_hour = 0
                if current.date() == end_cst.date():
                    end_hour = end_cst.hour
                else:
                    end_hour = 23
                for hour in range(start_hour, end_hour + 1):
                    begin_time = datetime(current.year, current.month, current.day, hour, 0, 0)
                    end_time = begin_time + timedelta(hours=1)
                    begin_str = begin_time.strftime("%H-%M-%S")
                    end_str = end_time.strftime("%H-%M-%S")
                    parquet_dir = f"{date_str}/{date_str}/trace-parquet"
                    parquet_file = f"trace_jaeger-span_{date_str}_{begin_str}.parquet"
                    parquet_path = f"{parquet_dir}/{parquet_file}"
                    print(uuid,start_time_utc,end_time_utc,parquet_path)
                current += timedelta(days=1)

                # INSERT_YOUR_CODE
                # 按照uuid、开始时间、结束时间定义output_file名称
                # 格式: jaeger_json_{uuid}_{start_time}_{end_time}.json
                # 其中start_time和end_time建议去掉特殊字符，统一格式如20250605T161002
                def format_time_str(time_str):
                    return time_str.replace("-", "").replace(":", "").replace("Z", "").replace("T", "T")
            
                output_file = f"jaeger_json_{uuid}_{format_time_str(start_time_utc)}_{format_time_str(end_time_utc)}.json"
                main(
                    parquet_file=parquet_path,
                    output_file=output_file,
                    ts_start_time = pd.to_datetime(start_cst).timestamp() * 1000,
                    ts_end_time= pd.to_datetime(end_cst).timestamp() * 1000,
                ) 


    