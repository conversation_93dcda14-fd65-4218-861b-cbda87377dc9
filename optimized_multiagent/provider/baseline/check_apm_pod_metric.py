#!/usr/bin/python3

import pytz
from datetime import datetime
import pandas as pd
import glob
import os
import json

def format_time_to_shanghai(time_str):
    # 解析输入的UTC时间字符串
    dt_utc = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%SZ")
    # 设置为UTC时区
    dt_utc = dt_utc.replace(tzinfo=pytz.UTC)
    # 转换为上海时区
    dt_shanghai = dt_utc.astimezone(pytz.timezone("Asia/Shanghai"))
    # 格式化输出为 年-月-日_小时
    return dt_shanghai.strftime("%Y-%m-%d")

# INSERT_YOUR_CODE
def read_value_by_object_id(df, object_id_col="object_id", value_col="rtt"):
    """
    读取parquet文件，提取指定object_id分组下的rtt值，输出格式为:
    {object_id: XXX, data: [x, x, ...]}
    """
    
    # 检查必要的列是否存在
    if object_id_col not in df.columns:
        print(f"错误: 列 '{object_id_col}' 不存在。可用列: {list(df.columns)}")
        return []
    
    if value_col not in df.columns:
        print(f"错误: 列 '{value_col}' 不存在。可用列: {list(df.columns)}")
        return []
    
    grouped = df.groupby(object_id_col)
    for obj_id, group in grouped:
        # 去除缺失值
        values = group[value_col].dropna().tolist()
        return {
            "object_id": obj_id,
            "data": values
        }

def get_apm_data(
    start_time: str,
    end_time: str,
    object_id_col="object_id", 
    value_col="rrt"
):
    """增加pod变化worker节点指标"""

    results = []
    # print(f"时间范围: {start_time} 到 {end_time}")
    # 获取对应的日期
    date_str = format_time_to_shanghai(start_time)
    print(f"对应日期: {date_str}")
    # 检查日期目录是否存在
    date_dir = f"{date_str}/metric-parquet/apm/pod/"
    if not os.path.exists(date_dir):
        print(f"目录不存在: {date_dir}")
        return []
    
    service_list = [
                "adservice-0",
                "adservice-1",
                "adservice-2",
                "cartservice-0",
                "cartservice-1",
                "cartservice-2",
                "currencyservice-0",
                "currencyservice-1",
                "currencyservice-2",
                "productcatalogservice-0",
                "productcatalogservice-1",
                "productcatalogservice-2",
                "checkoutservice-0",
                "checkoutservice-1",
                "checkoutservice-2",
                "recommendationservice-0",
                "recommendationservice-1",
                "recommendationservice-2",
                "shippingservice-0",
                "shippingservice-1",
                "shippingservice-2",
                "emailservice-0",
                "emailservice-1",
                "emailservice-2",
                "paymentservice-0",
                "paymentservice-1",
                "paymentservice-2",
                "tidb-pd-0",
                "tidb-tidb-0",
                "tidb-tikv-0",
                "redis-cart-0",
                "frontend-0",
                "frontend-1",
                "frontend-2",
            ]
    
    all_service_data = []
    for service_name in service_list:
        parquet_files = glob.glob(os.path.join(date_dir, f"pod_{service_name}_{date_str}.parquet"))
        
        if not parquet_files:
            print(f"警告: 未找到服务 {service_name} 的parquet文件")
            continue
            
        try:
            df = pd.read_parquet(parquet_files[0])
            
            data = read_value_by_object_id(
                df=df,
                object_id_col=object_id_col,
                value_col=value_col
                )
            
            all_service_data.append(data)
        except Exception as e:
            print(f"错误: 处理服务 {service_name} 时出错: {e}")
            continue
    
    return all_service_data 

def get_all_file(
    value_col="rrt"
):

    data_list = [
        {
            "start_time" : "2025-04-29T16:00:00Z",
            "end_time" : "2025-04-29T16:59:59Z"
        },
        {
            "start_time" : "2025-04-29T17:00:00Z",
            "end_time" : "2025-04-29T17:59:59Z"
        },
        {
            "start_time" : "2025-04-29T18:00:00Z",
            "end_time" : "2025-04-29T18:59:59Z"
        },
        {
            "start_time" : "2025-04-29T20:00:00Z",
            "end_time" : "2025-04-29T20:59:59Z"
        },
        {
            "start_time" : "2025-04-29T21:00:00Z",
            "end_time" : "2025-04-29T21:59:59Z"
        },
        {
            "start_time" : "2025-04-29T22:00:00Z",
            "end_time" : "2025-04-29T22:59:59Z"
        },
        {
            "start_time" : "2025-04-29T23:00:00Z",
            "end_time" : "2025-04-29T23:59:59Z"
        },
        {
            "start_time" : "2025-04-30T00:00:00Z",
            "end_time" : "2025-04-30T00:59:59Z"
        },
        {
            "start_time" : "2025-04-30T01:00:00Z",
            "end_time" : "2025-04-30T01:59:59Z"
        },
        {
            "start_time" : "2025-04-30T02:00:00Z",
            "end_time" : "2025-04-30T02:59:59Z"
        },
        {
            "start_time" : "2025-04-30T03:00:00Z",
            "end_time" : "2025-04-30T03:59:59Z"
        },
        {
            "start_time" : "2025-04-30T04:00:00Z",
            "end_time" : "2025-04-30T04:59:59Z"
        },
        {
            "start_time" : "2025-04-30T05:00:00Z",
            "end_time" : "2025-04-30T05:59:59Z"
        },
        {
            "start_time" : "2025-04-30T06:00:00Z",
            "end_time" : "2025-04-30T06:59:59Z"
        },
        {
            "start_time" : "2025-04-30T07:00:00Z",
            "end_time" : "2025-04-30T07:59:59Z"
        },
        {
            "start_time" : "2025-04-30T08:00:00Z",
            "end_time" : "2025-04-30T08:59:59Z"
        },
        {
            "start_time" : "2025-04-30T09:00:00Z",
            "end_time" : "2025-04-30T09:59:59Z"
        },
        {
            "start_time" : "2025-04-30T10:00:00Z",
            "end_time" : "2025-04-30T10:59:59Z"
        },
        {
            "start_time" : "2025-04-30T11:00:00Z",
            "end_time" : "2025-04-30T11:59:59Z"
        },
        {
            "start_time" : "2025-04-30T12:00:00Z",
            "end_time" : "2025-04-30T12:59:59Z"
        },
        {
            "start_time" : "2025-04-30T13:00:00Z",
            "end_time" : "2025-04-30T13:59:59Z"
        },
        {
            "start_time" : "2025-04-30T14:00:00Z",
            "end_time" : "2025-04-30T14:59:59Z"
        },
        {
            "start_time" : "2025-04-30T15:00:00Z",
            "end_time" : "2025-04-30T15:59:59Z"
        }
    ]

    total_metric_data = {
    }

    for time_range in data_list:

        return_data = get_apm_data(
            start_time=time_range['start_time'],
            end_time=time_range['end_time'],
            value_col=value_col
        )

        for object in return_data:
            service_name = object['object_id']
            service_data = object['data']

            if service_name not in total_metric_data:
                total_metric_data[service_name] = []

            total_metric_data[service_name].extend(service_data)
    
    return total_metric_data

def mean_value(value_col="rrt"):

    data = get_all_file(
        value_col=value_col
    )

    return_mean_data_list = {}
    
    for key,value in data.items():
        # 计算value的均值，p95值，最大值，最小值
        if not value:
            print(f"{key}: 没有数据")
            continue
    
        values = [float(item) for item in value]
        if not values:
            print(f"{key}: 没有有效的value字段")
            continue
    
        values_sorted = sorted(values)
        n = len(values_sorted)
        mean = sum(values_sorted) / n
        p95_index = int(n * 0.95) - 1 if n > 0 else 0
        p95 = values_sorted[p95_index] if n > 0 else None
        max_value = max(values_sorted)
        min_value = min(values_sorted)

        if key not in return_mean_data_list:
            return_mean_data_list[key] = {}
        
        return_mean_data_list[key][value_col] = max_value
    
    return return_mean_data_list
    

if __name__ == "__main__":

    all_apm_data = {}
    for metric in ['rrt','error_ratio','client_error_ratio','error_ratio','server_error_ratio','timeout']:
        data = mean_value(value_col=metric)
        for service_name,value in data.items():
            if service_name not in all_apm_data:
                all_apm_data[service_name] = {}
            all_apm_data[service_name][metric] = value[metric]
    
    print(all_apm_data)
    # data = mean_value(value_col="rrt")
    # for item,value in data.items():
    #     print(item,value)

    # pod apm 数据
    # {'adservice-0': {'rrt': 18926.96, 'error_ratio': 8.33, 'client_error_ratio': 8.33, 'server_error_ratio': 0.0, 'timeout': 1.0}, 'adservice-1': {'rrt': 5149.45, 'error_ratio': 3.19, 'client_error_ratio': 3.19, 'server_error_ratio': 0.0, 'timeout': 1.0}, 'adservice-2': {'rrt': 18897.44, 'error_ratio': 8.33, 'client_error_ratio': 8.33, 'server_error_ratio': 0.0, 'timeout': 1.0}, 'cartservice-0': {'rrt': 24780.0, 'error_ratio': 44.44, 'client_error_ratio': 44.44, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'cartservice-1': {'rrt': 5566.07, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 11.0}, 'cartservice-2': {'rrt': 14351.35, 'error_ratio': 25.0, 'client_error_ratio': 25.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'currencyservice-0': {'rrt': 1957.05, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'currencyservice-1': {'rrt': 1550.75, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'currencyservice-2': {'rrt': 4588.25, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'productcatalogservice-0': {'rrt': 14526.2, 'error_ratio': 23.08, 'client_error_ratio': 23.08, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'productcatalogservice-1': {'rrt': 6639.58, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'productcatalogservice-2': {'rrt': 8365.22, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'checkoutservice-2': {'rrt': 12232.23, 'error_ratio': 4.2, 'client_error_ratio': 4.2, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'recommendationservice-0': {'rrt': 4928.13, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'shippingservice-0': {'rrt': 1988.1, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'shippingservice-1': {'rrt': 6904.5, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'shippingservice-2': {'rrt': 5385.3, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'emailservice-0': {'rrt': 12562.5, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'emailservice-1': {'rrt': 7678.42, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'emailservice-2': {'rrt': 9215.17, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'paymentservice-0': {'rrt': 82772.0, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'paymentservice-1': {'rrt': 39131.0, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}, 'paymentservice-2': {'rrt': 18116.0, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}}