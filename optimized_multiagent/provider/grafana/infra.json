{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 11, "panels": [], "title": "node信息", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node CPU使用率（%）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node_memory_usage_rate{kubernetes_node=~\"$node\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node CPU使用率（%）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["{instance=\"**************\", job=\"aiops_metric_job\", kpi_name=\"{{device}} - Receive 各个网络接口接收速率\", kubernetes_node=\"aiops-k8s-04\", object_type=\"node\"}"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node_network_receive_bytes_total{kubernetes_node=~\"$host_name\"} / 1024", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node 网卡接受速率 (KB/s)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node_network_transmit_bytes_total{kubernetes_node=~\"$host_name\"} / 1024", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node 网卡发送速率 (KB/s)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node_disk_write_time_seconds_total{kubernetes_node=~\"$host_name\"} * 1000", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node 磁盘写时间 （ms）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 21, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node_disk_written_bytes_total{kubernetes_node=~\"$host_name\"} / (1024*1024)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node 磁盘写吞吐 （MB/s）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node_sockstat_TCP_inuse{kubernetes_node=~\"$host_name\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node TCP套接字数", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node_disk_read_time_seconds_total{kubernetes_node=~\"$host_name\"} * 1000", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node 磁盘读时间 （ms）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "node_filesystem_usage_rate{kubernetes_node=~\"$host_name\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node 文件系统使用率", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 41}, "id": 2, "panels": [], "title": "pod信息", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 60}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 42}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_cpu_usage{instance=~\"$host_name\",pod=~\"$pod_name\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod CPU使用率（%）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 42}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_memory_working_set_bytes{instance=~\"$host_name\",pod=~\"$pod_name\"} / (1024 * 1024)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod 内存使用量（GB）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 50}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_network_receive_bytes{instance=~\"$host_name\",pod=~\"$pod_name\"} / (1024)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod 入口流量（KB/s）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 50}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_network_transmit_bytes{instance=~\"$host_name\",pod=~\"$pod_name\"} / 1024", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod 出口流量(KB/s)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 50}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_network_receive_packets{instance=~\"$host_name\",pod=~\"$pod_name\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod 收包数量（个）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 50}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_network_transmit_packets{instance=~\"$host_name\",pod=~\"$pod_name\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod 出口包数（个）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 58}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_fs_reads_bytes{instance=~\"$host_name\",pod=~\"$pod_name\"} / 1024", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod 文件读取速度（KB/s）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 58}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_fs_writes_bytes{instance=~\"$host_name\",pod=~\"$pod_name\"} / 1024", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod 文件写入速度（KB/s）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 58}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "pod_processes{instance=~\"$host_name\",pod=~\"$pod_name\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "pod 进程数量（KB/s）", "type": "timeseries"}], "refresh": false, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "definition": "label_values(kubernetes_node)", "hide": 0, "includeAll": true, "label": "主机节点", "multi": true, "name": "host_name", "options": [], "query": {"query": "label_values(kubernetes_node)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["checkoutservice-1", "checkoutservice-0", "checkoutservice-2"], "value": ["checkoutservice-1", "checkoutservice-0", "checkoutservice-2"]}, "datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "definition": "label_values(pod_cpu_usage{instance=~\"$host_name\"},pod)", "hide": 0, "includeAll": true, "label": "服务名称", "multi": true, "name": "pod_name", "options": [], "query": {"query": "label_values(pod_cpu_usage{instance=~\"$host_name\"},pod)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "2025-06-16T23:10:00.000Z", "to": "2025-06-16T23:26:00.000Z"}, "timepicker": {}, "timezone": "utc", "title": "aiops", "uid": "e9d54a45-5816-4298-b4a1-bcd7ab5cbcbb", "version": 22, "weekStart": ""}