import pyarrow.parquet as pq
import pandas as pd
from prometheus_remote_writer import RemoteWriter
import sys
import os
import re
from pathlib import Path

target_vm_gateway = "http://localhost:8428/api/v1/write"

# infra的数据
metric_infra_node_info = {
    "node_cpu_usage_rate": {
        "value_field": "node_cpu_usage_rate",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,kubernetes_node,object_type"
    },
    "node_memory_MemAvailable_bytes":{
        "value_field": "node_memory_MemAvailable_bytes",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_memory_MemTotal_bytes": {
        "value_field": "node_memory_MemTotal_bytes",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },    
    "node_memory_usage_rate": {
        "value_field": "node_memory_usage_rate",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_disk_read_bytes_total": {
        "value_field": "node_memory_usage_rate",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_disk_written_bytes_total": {
        "value_field": "node_disk_written_bytes_total",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_disk_read_time_seconds_total": {
        "value_field": "node_disk_read_time_seconds_total",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_disk_write_time_seconds_total": {
        "value_field": "node_disk_write_time_seconds_total",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,kubernetes_node,object_type"  
    },  
    "node_filesystem_free_bytes": {
        "value_field": "node_filesystem_free_bytes",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,kubernetes_node,mountpoint,object_type"  
    },
    "node_filesystem_size_bytes": {
        "value_field": "node_filesystem_size_bytes",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,kubernetes_node,mountpoint,object_type"  
    },    
    "node_filesystem_usage_rate": {
        "value_field": "node_filesystem_usage_rate",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,kubernetes_node,mountpoint,object_type"  
    },    
    "node_network_receive_bytes_total": {
        "value_field": "node_network_receive_bytes_total",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_network_transmit_bytes_total": {
        "value_field": "node_network_transmit_bytes_total",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_network_receive_packets_total": {
        "value_field": "node_network_receive_packets_total",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    },
    "node_sockstat_TCP_inuse": {
        "value_field": "node_sockstat_TCP_inuse",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,kubernetes_node,object_type"  
    }
}

# 数据关联
# 1、 pod指标内instance对应pod的worker节点信息，和infra_node的kubernetes_node信息保持一致
metric_infra_pod_info = {
    "pod_cpu_usage": {
        "value_field": "pod_cpu_usage",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_memory_working_set_bytes": {
        "value_field": "pod_memory_working_set_bytes",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_network_receive_bytes": {
        "value_field": "pod_network_receive_bytes",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_network_transmit_bytes": {
        "value_field": "pod_network_transmit_bytes",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_network_receive_packets": {
        "value_field": "pod_network_receive_packets",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_network_transmit_packets": {
        "value_field": "pod_network_transmit_packets",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_fs_reads_bytes": {
        "value_field": "pod_fs_reads_bytes",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_fs_writes_bytes": {
        "value_field": "pod_fs_writes_bytes",
        "timestamp_field": "time",
        "label_field": "device,instance,kpi_name,namespace,pod,object_type"  
    },
    "pod_processes": {
        "value_field": "pod_processes",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,pod,object_type"  
    }
}

# tidb数据
# 1、 slow_query慢查询是瞬时指标
# 2、 tidb的数据比较独立，都单独运行在tidb的namespace内
metric_infra_tidb_info = {
    "connection_count": {
        "value_field": "connection_count",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "cpu_usage": {
        "value_field": "cpu_usage",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "duration_avg": {
        "value_field": "duration_avg",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "failed_query_ops": {
        "value_field": "failed_query_ops",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type"  
    },
    "qps": {
        "value_field": "qps",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type"  
    },
    "slow_query": {
        "value_field": "slow_query",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,sql_type"  
    },
    "top_sql_cpu": {
        "value_field": "top_sql_cpu",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "block_cache_size": {
        "value_field": "block_cache_size",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "duration_95th": {
        "value_field": "duration_95th",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "duration_99th": {
        "value_field": "duration_99th",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "memory_usage": {
        "value_field": "memory_usage",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "uptime": {
        "value_field": "uptime",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    }
}

# tidb pd和tikv的指标情况
metric_infra_other_info = {
    "abnormal_region_count": {
        "value_field": "abnormal_region_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type,type"  
    },
    "cpu_usage": {
        "value_field": "cpu_usage",
        "timestamp_field": "time",
        "label_field": "kpi_name,instance,object_type"  
    },
    "leader_count": {
        "value_field": "leader_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "leader_primary": {
        "value_field": "leader_primary",
        "timestamp_field": "time",
        "label_field": "kpi_name,instance,object_type"  
    },
    "learner_count": {
        "value_field": "learner_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,instance,object_type"  
    },
    "region_count": {
        "value_field": "region_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "region_health": {
        "value_field": "region_health",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type,type"  
    },
    "region_count": {
        "value_field": "region_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "storage_capacity": {
        "value_field": "storage_capacity",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "storage_size": {
        "value_field": "storage_size",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "storage_used_ratio": {
        "value_field": "storage_used_ratio",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "store_down_count": {
        "value_field": "store_down_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "store_low_space_count": {
        "value_field": "store_low_space_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "store_slow_count": {
        "value_field": "store_slow_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "store_unhealth_count": {
        "value_field": "store_unhealth_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "store_up_count": {
        "value_field": "store_up_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "witness_count": {
        "value_field": "witness_count",
        "timestamp_field": "time",
        "label_field": "kpi_name,object_type"  
    },
    "available_size": {
        "value_field": "available_size",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "capacity_size": {
        "value_field": "capacity_size",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "cpu_usage": {
        "value_field": "cpu_usage",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "grpc_qps": {
        "value_field": "grpc_qps",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type"  
    },
    "io_util": {
        "value_field": "io_util",
        "timestamp_field": "time",
        "label_field": "device,instance,kubernetes_node,kpi_name,object_type"  
    },
    "memory_usage": {
        "value_field": "memory_usage",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type"  
    },
    "qps": {
        "value_field": "qps",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type"  
    },
    "raft_apply_wait": {
        "value_field": "raft_apply_wait",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "raft_propose_wait": {
        "value_field": "raft_propose_wait",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "read_mbps": {
        "value_field": "read_mbps",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "region_pending": {
        "value_field": "region_pending",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "rocksdb_write_stall": {
        "value_field": "rocksdb_write_stall",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "server_is_up": {
        "value_field": "server_is_up",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "snapshot_apply_count": {
        "value_field": "snapshot_apply_count",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "store_size": {
        "value_field": "store_size",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    },
    "write_wal_mbps": {
        "value_field": "write_wal_mbps",
        "timestamp_field": "time",
        "label_field": "instance,kpi_name,namespace,object_type,type" 
    }
}


def parquet_to_prometheus_remote_write(
    parquet_file,
    remote_write_url,
    metric_name="custom_metric",
    value_field="value",
    timestamp_field="timestamp",
    label_fields=None,
    username=None,
    password=None
):

    print(f"[1/5] 正在读取 parquet 文件: {parquet_file}")
    df = pq.read_table(parquet_file).to_pandas()
    print(f"[2/5] parquet 文件读取完成，共 {len(df)} 行")
    print(f"[3/5] 初始化 Prometheus Remote Write 客户端")
    client = RemoteWriter(
        url=remote_write_url
    )

    print(f"[4/5] 开始逐行推送数据...")
    for idx, row in df.iterrows():
        # 组装 labels
        labels = {}
        if label_fields:
            for field in label_fields.split(','):
                labels[field] = str(row.get(field, ""))
        labels['__name__'] = metric_name
        labels['job'] = "aiops_metric_job"

        # 取 value 和 timestamp
        try:
            value = float(row[value_field])
            ts_raw = row[timestamp_field]
            if isinstance(ts_raw, (int, float)):
                timestamp = int(ts_raw)
            else:
                # 自动解析字符串时间戳
                try:
                    timestamp = int(pd.to_datetime(ts_raw).timestamp() * 1000)
                    #print(f"  [信息] 第{idx+1}行时间戳解析成功，时间: {timestamp}")
                except Exception as e:
                    #print(f"  [警告] 第{idx+1}行时间戳解析失败，跳过: {e}")
                    continue
        except Exception as e:
            #print(f"  [警告] 第{idx+1}行数据字段异常，跳过: {e}")
            continue

        # 构造 metric
        data = [
            {
                'metric': labels,
                'values': [value],
                'timestamps': [timestamp]
            }
        ]
    
        #推送
        try:
            client.send(data)
        except Exception as e:
            print(f"  [警告] 第{idx+1}行推送失败: {e}")

        if (idx + 1) % 1000 == 0:
            print(f"  已推送 {idx+1} 行...")

    print("[5/5] 全部数据推送完成！")

def extract_metric_name(file_path):
    """从文件路径中提取指标名称"""
    filename = os.path.basename(file_path)
    # 移除日期后缀和.parquet扩展名
    filename = re.sub(r'_\d{4}-\d{2}-\d{2}\.parquet$', '', filename)
        
    # 根据文件前缀提取指标名称
    if filename.startswith('infra_node_'):
        return filename.replace('infra_node_', '')
    elif filename.startswith('infra_pd_'):
        return filename.replace('infra_pd_', '')
    elif filename.startswith('infra_pod_'):
        return filename.replace('infra_pod_', '')
    elif filename.startswith('infra_tidb_'):
        return filename.replace('infra_tidb_', '')
    elif filename.startswith('infra_tikv_'):
        return filename.replace('infra_tikv_', '')
    else:
        return filename

def get_metric_config(metric_name):
    """根据指标名称获取配置信息"""
    # 按优先级查找配置
    if metric_name in metric_infra_node_info:
        return metric_infra_node_info[metric_name]
    elif metric_name in metric_infra_pod_info:
        return metric_infra_pod_info[metric_name]
    elif metric_name in metric_infra_tidb_info:
        return metric_infra_tidb_info[metric_name]
    elif metric_name in metric_infra_other_info:
        return metric_infra_other_info[metric_name]
    else:
        return None
    
def process_parquet_files(filepath):
    """处理parquet文件"""
    base_path = Path(filepath)
    
    # 遍历infra和other文件夹
    for folder in ["infra/infra_node", "infra/infra_pod", "infra/infra_tidb", "other"]:
        folder_path = base_path / folder
        if not folder_path.exists():
            print(f"文件夹不存在: {folder_path}")
            continue
            
        print(f"\n处理文件夹: {folder}")
        print("-" * 50)
        
        # 遍历文件夹中的所有parquet文件
        for parquet_file in folder_path.glob("*.parquet"):
            absolute_path = parquet_file.absolute()
            print(f"文件绝对路径: {absolute_path}")
            
            # 提取指标名称
            metric_name = extract_metric_name(str(absolute_path))
            print(f"提取的指标名称: {metric_name}")
            
            # 获取配置信息
            config = get_metric_config(metric_name)
            if config:
                print(f"找到配置:")
                print(f"  value_field: {config['value_field']}")
                print(f"  timestamp_field: {config['timestamp_field']}")
                print(f"  label_field: {config['label_field']}")
                value_field = config['value_field']
                timestamp_field = config['timestamp_field']
                label_field = config['label_field']

                print(f"开始推送指标名称：{metric_name}")
                parquet_to_prometheus_remote_write(
                    parquet_file=absolute_path,
                    remote_write_url=target_vm_gateway,
                    metric_name=metric_name,
                    value_field=value_field,
                    timestamp_field=timestamp_field,
                    label_fields=label_field
                ) 
                print(f"结束推送指标名称：{metric_name}")

            else:
                print(f"未找到指标 '{metric_name}' 的配置信息")
            
            print()

def all_process_parquet_files():

    filepaths = [
        '2025-06-06/2025-06-06/metric-parquet'
    ]

    for item in filepaths:
        process_parquet_files(filepath=item)


if __name__ == "__main__":

    # 导入的文件名称
    # 目标remote write文件的地址，默认是：http://localhost:8428/api/v1/write
    # 导入的指标名称
    # 导入的值
    # 导入的时间戳
    # 对应的metricname
    # 对应需要记录的label

    # if len(sys.argv) < 4:
    #     print("用法: python parquet_to_prom_remote_write.py <parquet文件路径> <remote_write_url> <metric_name> [value_field] [timestamp_field] [label1,label2,...]")
    #     sys.exit(1)
    # parquet_file = sys.argv[1]
    # remote_write_url = sys.argv[2]
    # metric_name = sys.argv[3]
    # value_field = sys.argv[4] if len(sys.argv) > 4 else "value"
    # timestamp_field = sys.argv[5] if len(sys.argv) > 5 else "timestamp"
    # label_fields = sys.argv[6].split(",") if len(sys.argv) > 6 and sys.argv[6] else None
    all_process_parquet_files()

    # parquet_to_prometheus_remote_write(
    #     parquet_file=parquet_file,
    #     remote_write_url=remote_write_url,
    #     metric_name=metric_name,
    #     value_field=value_field,
    #     timestamp_field=timestamp_field,
    #     label_fields=label_fields
    # ) 