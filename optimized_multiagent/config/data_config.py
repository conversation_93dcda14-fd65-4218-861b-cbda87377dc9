"""
数据路径配置
统一管理所有数据文件的路径配置
"""
import os

# 数据根目录配置
DATA_ROOT = "/data/phaseone_data"

# 如果在开发环境中，可以使用相对路径
if os.path.exists("./data") and os.path.islink("./data"):
    DATA_ROOT = "./data"

def get_data_path(date_str: str, data_type: str, subpath: str = "") -> str:
    """
    获取数据文件路径
    
    Args:
        date_str: 日期字符串，格式如 "2025-06-08"
        data_type: 数据类型，如 "log-parquet", "metric-parquet"
        subpath: 子路径，如 "infra/infra_pod"
    
    Returns:
        完整的数据路径
    """
    if subpath:
        return os.path.join(DATA_ROOT, date_str, data_type, subpath)
    else:
        return os.path.join(DATA_ROOT, date_str, data_type)


def get_log_file_path(date_str: str, hour_str: str) -> str:
    """
    获取日志文件路径
    
    Args:
        date_str: 日期字符串，格式如 "2025-06-08"
        hour_str: 小时字符串，格式如 "20"
    
    Returns:
        日志文件的完整路径
    """
    filename = f"log_filebeat-server_{date_str}_{hour_str}-00-00.parquet"
    return os.path.join(DATA_ROOT, date_str, "log-parquet", filename)


def get_metric_file_path(date_str: str, metric_type: str, filename: str) -> str:
    """
    获取指标文件路径
    
    Args:
        date_str: 日期字符串
        metric_type: 指标类型，如 "infra/infra_pod", "apm"
        filename: 文件名
    
    Returns:
        指标文件的完整路径
    """
    return os.path.join(DATA_ROOT, date_str, "metric-parquet", metric_type, filename)


def get_trace_file_path(date_str: str, filename: str) -> str:
    """
    获取调用链文件路径
    
    Args:
        date_str: 日期字符串
        filename: 文件名
    
    Returns:
        调用链文件的完整路径
    """
    return os.path.join(DATA_ROOT, date_str, "trace-parquet", filename)
