"""
模型配置文件
统一管理所有LLM模型的配置参数
"""
import os
from typing import Dict, Any

# 模型配置
MODEL_CONFIGS = {
    "default": {
        "model": "deepseek-chat",
        "base_url": "https://api.deepseek.com",
        "api_key_env": "DEEPSEEK_API_KEY",
        "temperature": 0.1,
        "max_tokens": 4000,
        "timeout": 30,  # 30秒超时
        "max_retries": 3,
        "retry_delay": 1.0
    },
    
    "fast": {
        "model": "deepseek-chat",
        "base_url": "https://api.deepseek.com", 
        "api_key_env": "DEEPSEEK_API_KEY",
        "temperature": 0.0,  # 更确定性的输出
        "max_tokens": 2000,  # 减少token数量
        "timeout": 15,  # 更短的超时
        "max_retries": 2,
        "retry_delay": 0.5
    },
    
    "analysis": {
        "model": "deepseek-chat",
        "base_url": "https://api.deepseek.com",
        "api_key_env": "DEEPSEEK_API_KEY", 
        "temperature": 0.2,
        "max_tokens": 6000,  # 分析需要更多token
        "timeout": 45,  # 分析允许更长时间
        "max_retries": 3,
        "retry_delay": 2.0
    }
}

# 根据Agent类型选择配置
AGENT_MODEL_MAPPING = {
    "info_assistant": "fast",      # 信息提取用快速配置
    "log_assistant": "default",    # 日志分析用默认配置
    "trace_assistant": "default",  # 调用链分析用默认配置
    "metric_assistant": "default", # 指标分析用默认配置
    "tidb_assistant": "default",   # TiDB分析用默认配置
    "analyze_assistant": "analysis" # 根因分析用分析配置
}

def get_model_config(agent_type: str = "default") -> Dict[str, Any]:
    """
    获取指定Agent类型的模型配置
    
    Args:
        agent_type: Agent类型
        
    Returns:
        模型配置字典
    """
    config_key = AGENT_MODEL_MAPPING.get(agent_type, "default")
    config = MODEL_CONFIGS[config_key].copy()
    
    # 从环境变量获取API密钥
    api_key = os.getenv(config["api_key_env"])
    if not api_key:
        raise ValueError(f"环境变量 {config['api_key_env']} 未设置")
    
    config["api_key"] = api_key
    return config

def get_optimized_model_config() -> Dict[str, Any]:
    """
    获取性能优化的模型配置
    专门用于提升响应速度
    """
    return {
        "model": "deepseek-chat",
        "base_url": "https://api.deepseek.com",
        "api_key": os.getenv("DEEPSEEK_API_KEY"),
        "temperature": 0.0,  # 最确定性输出
        "max_tokens": 1500,  # 限制输出长度
        "timeout": 10,       # 短超时
        "max_retries": 1,    # 减少重试
        "retry_delay": 0.2,  # 快速重试
        "stream": False,     # 禁用流式输出
        "top_p": 0.8,       # 减少随机性
        "frequency_penalty": 0.1,  # 减少重复
        "presence_penalty": 0.1
    }

def get_selector_model_config() -> Dict[str, Any]:
    """
    获取SelectorGroupChat专用的模型配置
    优化选择器的响应速度
    """
    return {
        "model": "deepseek-chat",
        "base_url": "https://api.deepseek.com", 
        "api_key": os.getenv("DEEPSEEK_API_KEY"),
        "temperature": 0.0,  # 选择器需要确定性
        "max_tokens": 500,   # 选择器输出很短
        "timeout": 8,        # 选择器需要快速响应
        "max_retries": 1,
        "retry_delay": 0.1,
        "stream": False,
        "top_p": 0.7
    }

# 性能监控配置
PERFORMANCE_CONFIG = {
    "enable_timing": True,           # 启用时间统计
    "enable_token_counting": True,   # 启用token计数
    "log_slow_requests": True,       # 记录慢请求
    "slow_request_threshold": 10.0,  # 慢请求阈值(秒)
    "enable_caching": True,          # 启用缓存
    "cache_ttl": 300,               # 缓存TTL(秒)
    "max_concurrent_requests": 5,    # 最大并发请求数
    "request_queue_size": 20         # 请求队列大小
}

def validate_model_config(config: Dict[str, Any]) -> bool:
    """
    验证模型配置的有效性
    
    Args:
        config: 模型配置
        
    Returns:
        是否有效
    """
    required_fields = ["model", "base_url", "api_key"]
    
    for field in required_fields:
        if field not in config or not config[field]:
            print(f"❌ 模型配置缺少必需字段: {field}")
            return False
    
    # 验证超时设置
    if config.get("timeout", 0) <= 0:
        print("❌ 超时设置无效")
        return False
        
    # 验证token限制
    if config.get("max_tokens", 0) <= 0:
        print("❌ max_tokens设置无效")
        return False
    
    return True

def print_model_config_summary():
    """打印模型配置摘要"""
    print("📊 模型配置摘要:")
    print(f"  可用配置: {list(MODEL_CONFIGS.keys())}")
    print(f"  Agent映射: {len(AGENT_MODEL_MAPPING)}个Agent类型")
    print(f"  性能监控: {'启用' if PERFORMANCE_CONFIG['enable_timing'] else '禁用'}")
    print(f"  并发限制: {PERFORMANCE_CONFIG['max_concurrent_requests']}个请求")
