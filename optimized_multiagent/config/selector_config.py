"""
智能选择器配置
定义SelectorGroupChat的选择逻辑和提示词
"""

def get_fault_diagnosis_selector_prompt():
    """获取故障诊断专用的选择器提示词 - 强制按顺序执行"""
    return """Select an agent to perform the next task.

{roles}

Current conversation context:
{history}

IMPORTANT: Follow this strict sequence for fault diagnosis:
1. FIRST: info_assistant (extract time and fault ID)
2. SECOND: log_assistant (check error logs)
3. THIRD: trace_assistant (check call traces)
4. FOURTH: metric_assistant (check performance metrics)
5. LAST: analyze_assistant (only after collecting data from steps 2-4)

Never skip to analyze_assistant without collecting actual data first.
If a user just provided a fault description, start with info_assistant.

Select the next agent in sequence from {participants}."""


def get_termination_conditions():
    """获取终止条件配置"""
    from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
    
    # 组合多个终止条件 - 优化以避免异步错误
    text_termination = TextMentionTermination("TERMINATE")
    max_turns_termination = MaxMessageTermination(12)  # 减少到12轮，避免过长对话

    return text_termination | max_turns_termination


def get_fault_diagnosis_candidate_func():
    """获取候选函数，用于过滤可选的Agent - 确保完整的数据收集"""
    def candidate_func(messages):
        from typing import List

        if not messages:
            return ["info_assistant"]

        # 获取所有消息的发送者
        all_speakers = [msg.source for msg in messages if hasattr(msg, 'source')]

        # 如果是用户消息，从info_assistant开始
        if messages[-1].source == "user":
            return ["info_assistant"]

        # 检查哪些Agent已经执行过
        executed_agents = set(all_speakers)

        # 必须按顺序执行的Agent
        required_sequence = ["info_assistant", "log_assistant", "trace_assistant", "metric_assistant"]

        # 找到下一个需要执行的Agent
        for agent in required_sequence:
            if agent not in executed_agents:
                return [agent]

        # 如果所有必需的Agent都执行过，可以选择analyze_assistant
        if all(agent in executed_agents for agent in required_sequence):
            return ["analyze_assistant"]

        # 如果还有未执行的检测Agent，继续执行
        remaining_agents = [agent for agent in required_sequence if agent not in executed_agents]
        if remaining_agents:
            return remaining_agents

        # 默认返回analyze_assistant
        return ["analyze_assistant"]

    return candidate_func

def get_selector_config():
    """获取完整的选择器配置"""
    return {
        "selector_prompt": get_fault_diagnosis_selector_prompt(),
        # 暂时移除候选函数，让选择器自由选择
        # "candidate_func": get_fault_diagnosis_candidate_func(),
        "termination_condition": get_termination_conditions(),
        "max_turns": 15,  # 增加轮次确保完整检测
        "allow_repeated_speaker": False  # 避免同一个Agent连续发言
    }
