"""
智能选择器配置
定义SelectorGroupChat的选择逻辑和提示词
"""

def get_fault_diagnosis_selector_prompt():
    """获取故障诊断专用的选择器提示词"""
    return """你是故障诊断协调专家。根据对话历史，选择下一个最合适的Agent。

可用Agent：
- info_assistant: 提取时间和故障ID
- log_assistant: 检查异常日志
- trace_assistant: 检查调用链异常
- metric_assistant: 检查性能指标
- tidb_assistant: 检查数据库状态
- analyze_assistant: 综合分析根因

选择规则：
1. 如果没有时间信息 → info_assistant
2. 如果有时间但没检查日志 → log_assistant
3. 如果检查了日志但没检查调用链 → trace_assistant
4. 如果检查了调用链但没检查指标 → metric_assistant
5. 如果收集了多个维度数据 → analyze_assistant
6. 如果analyze_assistant已给出结论 → TERMINATE

避免重复：不要选择刚刚执行过的Agent，除非有新的线索。

只输出Agent名称，不要解释。"""


def get_termination_conditions():
    """获取终止条件配置"""
    from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
    
    # 组合多个终止条件
    text_termination = TextMentionTermination("TERMINATE")
    max_turns_termination = MaxMessageTermination(15)  # 最多15轮对话
    
    return text_termination | max_turns_termination


def get_selector_config():
    """获取完整的选择器配置"""
    return {
        "selector_prompt": get_fault_diagnosis_selector_prompt(),
        "termination_condition": get_termination_conditions(),
        "max_turns": 10,  # 减少最大轮次，避免循环
        "allow_repeated_speaker": False  # 避免同一个Agent连续发言
    }
