"""
智能选择器配置
定义SelectorGroupChat的选择逻辑和提示词
"""

def get_fault_diagnosis_selector_prompt():
    """获取故障诊断专用的选择器提示词 - 基于官方文档的简化版本"""
    return """Select an agent to perform the next task.

{roles}

Current conversation context:
{history}

Read the above conversation, then select an agent from {participants} to perform the next task.

Rules:
1. Start with info_assistant to extract time information
2. Then use log_assistant, trace_assistant, metric_assistant to collect data
3. Use analyze_assistant when enough data is collected
4. Avoid selecting the same agent consecutively unless necessary

Only select one agent name."""


def get_termination_conditions():
    """获取终止条件配置"""
    from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
    
    # 组合多个终止条件
    text_termination = TextMentionTermination("TERMINATE")
    max_turns_termination = MaxMessageTermination(15)  # 最多15轮对话
    
    return text_termination | max_turns_termination


def get_fault_diagnosis_candidate_func():
    """获取候选函数，用于过滤可选的Agent"""
    def candidate_func(messages):
        from typing import List

        if not messages:
            return ["info_assistant"]

        # 获取最后几条消息的发送者
        recent_speakers = [msg.source for msg in messages[-3:] if hasattr(msg, 'source')]

        # 如果是用户消息，从info_assistant开始
        if messages[-1].source == "user":
            return ["info_assistant"]

        # 如果info_assistant刚执行过，选择数据收集Agent
        if "info_assistant" in recent_speakers:
            return ["log_assistant", "trace_assistant", "metric_assistant"]

        # 如果已经收集了一些数据，可以选择analyze_assistant
        if len(set(recent_speakers) & {"log_assistant", "trace_assistant", "metric_assistant"}) >= 2:
            return ["analyze_assistant"]

        # 默认返回所有Agent
        return ["info_assistant", "log_assistant", "trace_assistant", "metric_assistant", "tidb_assistant", "analyze_assistant"]

    return candidate_func

def get_selector_config():
    """获取完整的选择器配置"""
    return {
        "selector_prompt": get_fault_diagnosis_selector_prompt(),
        "candidate_func": get_fault_diagnosis_candidate_func(),
        "termination_condition": get_termination_conditions(),
        "max_turns": 12,  # 适当增加轮次
        "allow_repeated_speaker": False  # 避免同一个Agent连续发言
    }
