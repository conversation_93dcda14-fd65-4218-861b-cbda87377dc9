"""
调试版本的故障诊断系统
用于诊断系统卡住的问题
"""
import asyncio
import json
import time
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from agents.agent_factory import create_model_client, create_info_agent
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.conditions import MaxMessageTermination


async def debug_simple_test():
    """简单的调试测试"""
    print("🔧 开始调试测试...")
    
    try:
        # 1. 测试模型客户端创建
        print("📡 测试模型客户端...")
        model_client = create_model_client()
        print("✅ 模型客户端创建成功")
        
        # 2. 测试单个Agent创建
        print("🤖 测试Agent创建...")
        info_agent = create_info_agent(model_client)
        print("✅ Agent创建成功")
        
        # 3. 测试简单的Agent调用
        print("💬 测试Agent调用...")
        simple_task = "Extract time from: A fault occurred from 2025-06-08T12:10:52Z to 2025-06-08T12:20:52Z"
        
        start_time = time.time()
        result = await info_agent.run(simple_task)
        end_time = time.time()
        
        print(f"✅ Agent调用成功，耗时: {end_time - start_time:.2f}秒")
        print(f"📋 结果: {result}")
        
        # 4. 关闭客户端
        await model_client.close()
        print("🔒 模型客户端已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def debug_selector_test():
    """测试SelectorGroupChat"""
    print("🔧 开始SelectorGroupChat调试...")
    
    try:
        # 创建模型客户端
        model_client = create_model_client()
        
        # 创建简单的Agent
        info_agent = create_info_agent(model_client)
        
        # 创建简化的SelectorGroupChat
        print("🎯 创建SelectorGroupChat...")
        team = SelectorGroupChat(
            participants=[info_agent],
            model_client=model_client,
            selector_prompt="Select the info_assistant to extract time information.",
            termination_condition=MaxMessageTermination(2),
            max_turns=2
        )
        print("✅ SelectorGroupChat创建成功")
        
        # 测试运行
        print("🚀 测试运行...")
        simple_task = "Extract time from: A fault occurred from 2025-06-08T12:10:52Z to 2025-06-08T12:20:52Z"
        
        start_time = time.time()
        
        # 使用超时机制
        try:
            result = await asyncio.wait_for(team.run(simple_task), timeout=30.0)
            end_time = time.time()
            print(f"✅ SelectorGroupChat运行成功，耗时: {end_time - start_time:.2f}秒")
            print(f"📋 结果: {result}")
        except asyncio.TimeoutError:
            print("⏰ SelectorGroupChat运行超时（30秒）")
            return False
        
        await model_client.close()
        return True
        
    except Exception as e:
        print(f"❌ SelectorGroupChat测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def debug_data_access():
    """测试数据访问"""
    print("🔧 测试数据访问...")
    
    try:
        # 测试数据目录
        data_paths = [
            "/data/phaseone_data/2025-06-08",
            "/data/phaseone_data/2025-06-08/log-parquet",
            "/data/phaseone_data/2025-06-08/metric-parquet",
            "/data/phaseone_data/2025-06-08/trace-parquet"
        ]
        
        for path in data_paths:
            if os.path.exists(path):
                files = os.listdir(path)
                print(f"✅ {path}: {len(files)} 个文件")
            else:
                print(f"❌ {path}: 不存在")
        
        # 测试具体的数据文件
        log_file = "/data/phaseone_data/2025-06-08/log-parquet/log_filebeat-server_2025-06-08_20-00-00.parquet"
        if os.path.exists(log_file):
            print(f"✅ 日志文件存在: {log_file}")
        else:
            print(f"❌ 日志文件不存在: {log_file}")
            # 列出实际存在的文件
            log_dir = "/data/phaseone_data/2025-06-08/log-parquet"
            if os.path.exists(log_dir):
                actual_files = os.listdir(log_dir)
                print(f"📁 实际文件: {actual_files[:5]}")  # 显示前5个文件
        
        return True
        
    except Exception as e:
        print(f"❌ 数据访问测试失败: {e}")
        return False


async def main():
    """主调试函数"""
    print("🚀 优化系统调试工具")
    print("=" * 50)
    
    # 1. 数据访问测试
    print("\n📊 步骤1: 数据访问测试")
    data_ok = await debug_data_access()
    
    # 2. 简单Agent测试
    print("\n🤖 步骤2: 简单Agent测试")
    agent_ok = await debug_simple_test()
    
    # 3. SelectorGroupChat测试
    if agent_ok:
        print("\n🎯 步骤3: SelectorGroupChat测试")
        selector_ok = await debug_selector_test()
    else:
        print("\n⚠️ 跳过SelectorGroupChat测试（Agent测试失败）")
        selector_ok = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 调试结果总结:")
    print(f"  数据访问: {'✅ 正常' if data_ok else '❌ 异常'}")
    print(f"  Agent功能: {'✅ 正常' if agent_ok else '❌ 异常'}")
    print(f"  SelectorGroupChat: {'✅ 正常' if selector_ok else '❌ 异常'}")
    
    if data_ok and agent_ok and selector_ok:
        print("\n🎉 所有测试通过！系统应该可以正常运行")
        print("💡 建议：尝试增加超时时间或检查网络连接")
    else:
        print("\n⚠️ 发现问题，需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())
