"""
Agent工厂模块
用于创建优化后的各种Agent，包含简化的system_message和合并的功能
"""
import os
import sys
from pathlib import Path
from autogen_agentchat.agents import AssistantAgent
from autogen_core.tools import FunctionTool
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 添加配置路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入工具函数
from tools.time_analyze import time_analyzer
from tools.check_log import check_log
from tools.check_trace import check_trace
from tools.check_metric import (
    check_metric_apm, check_metric_infra_node, check_metric_infra_pod,
    check_metric_tidb_tikv, check_metric_tidb_tidb, check_metric_tidb_pd
)

# 简化导入，专注核心功能


def create_model_client():
    """创建模型客户端"""
    return OpenAIChatCompletionClient(
        model="Pro/deepseek-ai/DeepSeek-V3",
        temperature=0.1,
        api_key="sk-qhukemetcffylfxgsmuckxbcenyvyuoudilowlvrveaixbrj",
        base_url="https://api.siliconflow.cn/v1",
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown"
        }
    )


def create_info_agent(model_client):
    """创建信息提取Agent"""
    return AssistantAgent(
        name="info_assistant",
        description="信息提取助手",
        model_client=model_client,
        tools=[],
        system_message="""你是信息提取专家。
        任务：从输入中提取开始时间、结束时间和uuid。
        输出格式：直接提供提取的信息，无需JSON格式。
        
        示例：
        输入："A fault occurred from 2025-06-06T12:03:18Z to 2025-06-06T12:26:18Z. uuid: 342d4820-100"
        输出：开始时间：2025-06-06T12:03:18Z，结束时间：2025-06-06T12:26:18Z，uuid：342d4820-100
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )


def create_log_agent(model_client):
    """创建日志分析Agent"""
    check_log_tool = FunctionTool(
        check_log,
        description="检测故障时间范围内的异常日志"
    )
    
    return AssistantAgent(
        name="log_assistant",
        description="日志故障检测助手",
        model_client=model_client,
        tools=[check_log_tool],
        system_message="""你是日志分析专家。
        任务：检查故障时间内的异常日志，识别故障组件和原因。
        重点：frontend日志通常不是根因，需要分析后端服务异常。
        输出：JSON格式，包含故障组件、现象、原因。
        如果无异常，返回"未检测到异常日志"。
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )


def create_trace_agent(model_client):
    """创建调用链分析Agent"""
    check_trace_tool = FunctionTool(
        check_trace,
        description="检测故障时间范围内的异常调用链信息"
    )
    
    return AssistantAgent(
        name="trace_assistant",
        description="调用链分析助手",
        model_client=model_client,
        tools=[check_trace_tool],
        system_message="""你是调用链分析专家。
        任务：分析故障时间内的调用链异常，识别响应时间异常的服务。
        重点：关注响应时间超过正常范围的服务调用。
        输出：JSON格式，包含异常服务、响应时间、调用路径。
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )


def create_combined_metric_agent(model_client):
    """创建合并的指标分析Agent（APM + 基础设施）"""
    # 创建工具
    apm_tool = FunctionTool(check_metric_apm, description="检查APM指标数据")
    node_tool = FunctionTool(check_metric_infra_node, description="检查节点基础指标")
    pod_tool = FunctionTool(check_metric_infra_pod, description="检查Pod基础指标")
    
    return AssistantAgent(
        name="metric_assistant",
        description="指标分析助手",
        model_client=model_client,
        tools=[apm_tool, node_tool, pod_tool],
        system_message="""你是指标分析专家。
        任务：根据需要检查APM指标、节点指标或Pod指标。
        策略：
        1. 如果调用链显示某服务异常，优先检查该服务的APM指标
        2. 如果怀疑资源问题，检查节点和Pod指标
        3. 关注CPU、内存、网络等关键指标的异常
        输出：JSON格式，包含异常指标、阈值、影响。
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )


def create_tidb_agent(model_client):
    """创建TiDB分析Agent"""
    tidb_tool = FunctionTool(check_metric_tidb_tidb, description="检查TiDB指标")
    tikv_tool = FunctionTool(check_metric_tidb_tikv, description="检查TiKV指标")
    pd_tool = FunctionTool(check_metric_tidb_pd, description="检查PD指标")
    
    return AssistantAgent(
        name="tidb_assistant",
        description="TiDB数据库分析助手",
        model_client=model_client,
        tools=[tidb_tool, tikv_tool, pd_tool],
        system_message="""你是TiDB数据库专家。
        任务：检查TiDB集群的健康状态和性能指标。
        重点：关注查询延迟、连接数、存储性能等关键指标。
        输出：JSON格式，包含数据库组件、性能指标、异常情况。
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )


def create_analyze_agent(model_client):
    """创建最终分析Agent"""
    return AssistantAgent(
        name="analyze_assistant",
        description="根因分析助手",
        model_client=model_client,
        tools=[],
        system_message="""你是根因分析专家。
        任务：基于所有检测结果进行综合分析，确定故障根因。
        
        分析步骤：
        1. 汇总所有Agent的检测结果
        2. 识别关键异常信号
        3. 分析故障传播路径
        4. 确定最可能的根因
        
        输出格式：
        ```json
        {
            "uuid": "故障ID",
            "root_cause": "根因描述",
            "fault_component": "故障组件",
            "evidence": ["证据1", "证据2"],
            "confidence": "置信度(高/中/低)"
        }
        ```
        
        最后必须输出"TERMINATE"结束对话。
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )


def create_all_agents():
    """创建所有优化后的Agent"""
    print("🔧 开始创建模型客户端...")
    model_client = create_model_client()
    print("✅ 模型客户端创建完成")

    print("🤖 开始创建各个Agent...")

    print("  📋 创建信息提取Agent...")
    info_agent = create_info_agent(model_client)

    print("  📝 创建日志分析Agent...")
    log_agent = create_log_agent(model_client)

    print("  🔗 创建调用链分析Agent...")
    trace_agent = create_trace_agent(model_client)

    print("  📊 创建合并指标分析Agent...")
    metric_agent = create_combined_metric_agent(model_client)

    print("  🗄️ 创建TiDB分析Agent...")
    tidb_agent = create_tidb_agent(model_client)

    print("  🎯 创建根因分析Agent...")
    analyze_agent = create_analyze_agent(model_client)

    print("✅ 所有Agent创建完成")

    agents = {
        "model_client": model_client,
        "info_agent": info_agent,
        "log_agent": log_agent,
        "trace_agent": trace_agent,
        "metric_agent": metric_agent,
        "tidb_agent": tidb_agent,
        "analyze_agent": analyze_agent
    }

    print(f"📊 Agent统计: 共创建{len(agents)-1}个Agent")
    return agents
