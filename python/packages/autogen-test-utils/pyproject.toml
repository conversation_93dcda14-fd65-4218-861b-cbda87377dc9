[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "autogen-test-utils"
version = "0.0.0"
license = {file = "LICENSE-CODE"}
requires-python = ">=3.10"
dependencies = [
    "autogen-core",
    "pytest",
    "opentelemetry-sdk>=1.27.0",
]

[tool.ruff]
extend = "../../pyproject.toml"
include = ["src/**"]

[tool.pyright]
extends = "../../pyproject.toml"
include = ["src"]

[tool.poe]
include = "../../shared_tasks.toml"

[tool.poe.tasks]
mypy = "mypy --config-file $POE_ROOT/../../pyproject.toml src"
test = "true"
