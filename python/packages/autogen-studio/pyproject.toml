[build-system]
requires = ["setuptools", "setuptools-scm"]
build-backend = "setuptools.build_meta"

[project]
name = "autogenstudio"
authors = [
  { name="AutoGen Team", email="<EMAIL>" },
]
description = "AutoGen Studio"
readme = "README.md"
license = {file = "LICENSE-CODE"}
requires-python = ">=3.9"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]


dependencies = [
    "pydantic",
    "pydantic-settings",
    "fastapi",
    "typer",
    "uvicorn",
    "aiofiles",
    "python-dotenv",
    "websockets",
    "numpy < 2.0.0",
    "sqlmodel",
    "psycopg",
    "alembic",
    "loguru",
    "pyyaml",
    "autogen-core>=0.4.2,<0.5",
    "autogen-agentchat>=0.4.2,<0.5",
    "autogen-ext[magentic-one, openai, azure]>=0.4.2,<0.5",
    "azure-identity"
]
optional-dependencies = {web = ["fastapi", "uvicorn"], database = ["psycopg"]}

dynamic = ["version"]

[tool.setuptools]
include-package-data = true


[tool.setuptools.dynamic]
version = {attr = "autogenstudio.version.VERSION"}
readme = {file = ["README.md"]}

[tool.setuptools.packages.find]
include = ["autogenstudio*"]
exclude = ["*.tests*"]
namespaces = false

[tool.setuptools.package-data]
"autogenstudio" = ["*.*"]

[tool.pytest.ini_options]
filterwarnings = [
    "ignore:Deprecated call to `pkg_resources\\.declare_namespace\\('.*'\\):DeprecationWarning",
    "ignore::DeprecationWarning:google.rpc",
]


[project.urls]
"Homepage" = "https://github.com/microsoft/autogen"
"Bug Tracker" = "https://github.com/microsoft/autogen/issues"

[project.scripts]
autogenstudio = "autogenstudio.cli:run"


[tool.ruff]
extend = "../../pyproject.toml"
exclude = ["build", "dist"]
include = [
    "autogenstudio/**"
]

[tool.ruff.lint]
ignore = ["B008"]


[tool.poe.tasks]
fmt = "ruff format"
format.ref = "fmt"
lint = "ruff check"
test = "pytest -n 0 --cov=autogenstudio --cov-report=term-missing"
