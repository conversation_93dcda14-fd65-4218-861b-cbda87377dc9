.drop-target-valid {
    outline: 2px solid #4caf50;
    outline-offset: 4px;
    transition: outline-color 0.2s;
  }
  
  .drop-target-invalid {
    outline: 2px dashed #f44336;
    outline-offset: 4px;
  }
  
  .droppable-zone {
    min-height: 40px;
    border: 2px dashed #ccc;
    border-radius: 4px;
    margin: 8px 0;
  }
  
  .droppable-zone.can-drop {
    border-color: #4caf50;
    background: rgba(76, 175, 80, 0.1);
  }

.my-left-handle{
    @apply bg-accent w-1 h-3 rounded-r-sm -ml-1 border-0 !important; 
}

.my-right-handle{
    @apply bg-accent w-1 h-3  rounded-l-sm -mr-1 border-0 !important; 
} 