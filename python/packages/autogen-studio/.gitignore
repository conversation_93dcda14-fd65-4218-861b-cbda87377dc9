database.sqlite
.cache/*
autogenstudio/web/files/user/*
autogenstudio/test
autogenstudio/database/alembic.ini 
autogenstudio/database/alembic/*
autogenstudio/web/files/ui/*
OAI_CONFIG_LIST
scratch/
autogenstudio/web/workdir/*
autogenstudio/web/ui/*
autogenstudio/web/skills/user/*
.release.sh
.nightly.sh
notebooks/test

notebooks/work_dir/*
notebooks/test.db

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
