---
myst:
  html_meta:
    "description lang=en": |
      AutoGen is a community-driven project. Learn how to get involved, contribute, and connect with the community.
---

# API Reference

```{toctree}
:caption: AutoGen AgentChat
:maxdepth: 2

python/autogen_agentchat
python/autogen_agentchat.messages
python/autogen_agentchat.agents
python/autogen_agentchat.teams
python/autogen_agentchat.base
python/autogen_agentchat.conditions
python/autogen_agentchat.ui
python/autogen_agentchat.state
```

```{toctree}
:caption: AutoGen Core
:maxdepth: 2

python/autogen_core
python/autogen_core.code_executor
python/autogen_core.models
python/autogen_core.model_context
python/autogen_core.tools
python/autogen_core.tool_agent
python/autogen_core.memory
python/autogen_core.exceptions
python/autogen_core.logging
```

```{toctree}
:caption: AutoGen Extensions
:maxdepth: 2

python/autogen_ext.agents.magentic_one
python/autogen_ext.agents.openai
python/autogen_ext.agents.web_surfer
python/autogen_ext.agents.file_surfer
python/autogen_ext.agents.video_surfer
python/autogen_ext.agents.video_surfer.tools
python/autogen_ext.auth.azure
python/autogen_ext.teams.magentic_one
python/autogen_ext.models.cache
python/autogen_ext.models.openai
python/autogen_ext.models.replay
python/autogen_ext.models.azure
python/autogen_ext.models.semantic_kernel
python/autogen_ext.tools.langchain
python/autogen_ext.tools.graphrag
python/autogen_ext.tools.code_execution
python/autogen_ext.tools.semantic_kernel
python/autogen_ext.code_executors.local
python/autogen_ext.code_executors.docker
python/autogen_ext.code_executors.jupyter
python/autogen_ext.code_executors.azure
python/autogen_ext.cache_store.diskcache
python/autogen_ext.cache_store.redis
python/autogen_ext.runtimes.grpc
```
