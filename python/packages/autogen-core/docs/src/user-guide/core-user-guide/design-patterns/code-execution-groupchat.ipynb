{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Code Execution\n", "\n", "In this section we explore creating custom agents to handle code generation and execution. These tasks can be handled using the provided Agent implementations found here {py:meth}`~autogen_agentchat.agents.AssistantAgent`, {py:meth}`~autogen_agentchat.agents.CodeExecutorAgent`; but this guide will show you how to implement custom, lightweight agents that can replace their functionality. This simple example implements two agents that create a plot of Tesla's and Nvidia's stock returns.\n", "\n", "We first define the agent classes and their respective procedures for \n", "handling messages.\n", "We create two agent classes: `Assistant` and `Executor`. The `Assistant`\n", "agent writes code and the `Executor` agent executes the code.\n", "We also create a `Message` data class, which defines the messages that are passed between\n", "the agents.\n", "\n", "```{attention}\n", "Code generated in this example is run within a [Docker](https://www.docker.com/) container. Please ensure Docker is [installed](https://docs.docker.com/get-started/get-docker/) and running prior to running the example. Local code execution is available ({py:class}`~autogen_ext.code_executors.local.LocalCommandLineCodeExecutor`) but is not recommended due to the risk of running LLM generated code in your local environment.\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import re\n", "from dataclasses import dataclass\n", "from typing import List\n", "\n", "from autogen_core import DefaultTopicId, MessageContext, RoutedAgent, default_subscription, message_handler\n", "from autogen_core.code_executor import CodeBlock, CodeExecutor\n", "from autogen_core.models import (\n", "    AssistantMessage,\n", "    ChatCompletionClient,\n", "    LLMMessage,\n", "    SystemMessage,\n", "    UserMessage,\n", ")\n", "\n", "\n", "@dataclass\n", "class Message:\n", "    content: str\n", "\n", "\n", "@default_subscription\n", "class Assistant(RoutedAgent):\n", "    def __init__(self, model_client: ChatCompletionClient) -> None:\n", "        super().__init__(\"An assistant agent.\")\n", "        self._model_client = model_client\n", "        self._chat_history: List[LLMMessage] = [\n", "            SystemMessage(\n", "                content=\"\"\"Write Python script in markdown block, and it will be executed.\n", "Always save figures to file in the current directory. Do not use plt.show(). All code required to complete this task must be contained within a single response.\"\"\",\n", "            )\n", "        ]\n", "\n", "    @message_handler\n", "    async def handle_message(self, message: Message, ctx: MessageContext) -> None:\n", "        self._chat_history.append(UserMessage(content=message.content, source=\"user\"))\n", "        result = await self._model_client.create(self._chat_history)\n", "        print(f\"\\n{'-'*80}\\nAssistant:\\n{result.content}\")\n", "        self._chat_history.append(AssistantMessage(content=result.content, source=\"assistant\"))  # type: ignore\n", "        await self.publish_message(Message(content=result.content), DefaultTopicId())  # type: ignore\n", "\n", "\n", "def extract_markdown_code_blocks(markdown_text: str) -> List[CodeBlock]:\n", "    pattern = re.compile(r\"```(?:\\s*([\\w\\+\\-]+))?\\n([\\s\\S]*?)```\")\n", "    matches = pattern.findall(markdown_text)\n", "    code_blocks: List[CodeBlock] = []\n", "    for match in matches:\n", "        language = match[0].strip() if match[0] else \"\"\n", "        code_content = match[1]\n", "        code_blocks.append(CodeBlock(code=code_content, language=language))\n", "    return code_blocks\n", "\n", "\n", "@default_subscription\n", "class Executor(RoutedAgent):\n", "    def __init__(self, code_executor: CodeExecutor) -> None:\n", "        super().__init__(\"An executor agent.\")\n", "        self._code_executor = code_executor\n", "\n", "    @message_handler\n", "    async def handle_message(self, message: Message, ctx: MessageContext) -> None:\n", "        code_blocks = extract_markdown_code_blocks(message.content)\n", "        if code_blocks:\n", "            result = await self._code_executor.execute_code_blocks(\n", "                code_blocks, cancellation_token=ctx.cancellation_token\n", "            )\n", "            print(f\"\\n{'-'*80}\\nExecutor:\\n{result.output}\")\n", "            await self.publish_message(Message(content=result.output), DefaultTopicId())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You might have already noticed, the agents' logic, whether it is using model or code executor,\n", "is completely decoupled from\n", "how messages are delivered. This is the core idea: the framework provides\n", "a communication infrastructure, and the agents are responsible for their own\n", "logic. We call the communication infrastructure an **Agent Runtime**.\n", "\n", "Agent runtime is a key concept of this framework. Besides delivering messages,\n", "it also manages agents' lifecycle. \n", "So the creation of agents are handled by the runtime.\n", "\n", "The following code shows how to register and run the agents using \n", "{py:class}`~autogen_core.SingleThreadedAgentRuntime`,\n", "a local embedded agent runtime implementation.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--------------------------------------------------------------------------------\n", "Assistant:\n", "```python\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import yfinance as yf\n", "\n", "# Define the ticker symbols for NVIDIA and Tesla\n", "tickers = ['NVDA', 'TSLA']\n", "\n", "# Download the stock data from Yahoo Finance starting from 2024-01-01\n", "start_date = '2024-01-01'\n", "end_date = pd.to_datetime('today').strftime('%Y-%m-%d')\n", "\n", "# Download the adjusted closing prices\n", "stock_data = yf.download(tickers, start=start_date, end=end_date)['Adj Close']\n", "\n", "# Calculate the daily returns\n", "returns = stock_data.pct_change().dropna()\n", "\n", "# Plot the cumulative returns for each stock\n", "cumulative_returns = (1 + returns).cumprod()\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(cumulative_returns.index, cumulative_returns['NVDA'], label='NVIDIA', color='green')\n", "plt.plot(cumulative_returns.index, cumulative_returns['TSLA'], label='Tesla', color='red')\n", "plt.title('NVIDIA vs Tesla Stock Returns YTD (2024)')\n", "plt.xlabel('Date')\n", "plt.ylabel('Cumulative Return')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "\n", "# Save the plot to a file\n", "plt.savefig('nvidia_vs_tesla_ytd_returns.png')\n", "```\n", "\n", "--------------------------------------------------------------------------------\n", "Executor:\n", "Traceback (most recent call last):\n", "  File \"/workspace/tmp_code_fd7395dcad4fbb74d40c981411db604e78e1a17783ca1fab3aaec34ff2c3fdf0.python\", line 1, in <module>\n", "    import pandas as pd\n", "ModuleNotFoundError: No module named 'pandas'\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "Assistant:\n", "It seems like the necessary libraries are not available in your environment. However, since I can't install packages or check the environment directly from here, you'll need to make sure that the appropriate packages are installed in your working environment. Once the modules are available, the script provided will execute properly.\n", "\n", "Here's how you can install the required packages using pip (make sure to run these commands in your terminal or command prompt):\n", "\n", "```bash\n", "pip install pandas matplotlib yfinance\n", "```\n", "\n", "Let me provide you the script again for reference:\n", "\n", "```python\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import yfinance as yf\n", "\n", "# Define the ticker symbols for NVIDIA and Tesla\n", "tickers = ['NVDA', 'TSLA']\n", "\n", "# Download the stock data from Yahoo Finance starting from 2024-01-01\n", "start_date = '2024-01-01'\n", "end_date = pd.to_datetime('today').strftime('%Y-%m-%d')\n", "\n", "# Download the adjusted closing prices\n", "stock_data = yf.download(tickers, start=start_date, end=end_date)['Adj Close']\n", "\n", "# Calculate the daily returns\n", "returns = stock_data.pct_change().dropna()\n", "\n", "# Plot the cumulative returns for each stock\n", "cumulative_returns = (1 + returns).cumprod()\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(cumulative_returns.index, cumulative_returns['NVDA'], label='NVIDIA', color='green')\n", "plt.plot(cumulative_returns.index, cumulative_returns['TSLA'], label='Tesla', color='red')\n", "plt.title('NVIDIA vs Tesla Stock Returns YTD (2024)')\n", "plt.xlabel('Date')\n", "plt.ylabel('Cumulative Return')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "\n", "# Save the plot to a file\n", "plt.savefig('nvidia_vs_tesla_ytd_returns.png')\n", "```\n", "\n", "Make sure to install the packages in the environment where you run this script. Feel free to ask if you have further questions or issues!\n", "\n", "--------------------------------------------------------------------------------\n", "Executor:\n", "[*********************100%***********************]  2 of 2 completed\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "Assistant:\n", "It looks like the data fetching process completed successfully. You should now have a plot saved as `nvidia_vs_tesla_ytd_returns.png` in your current directory. If you have any additional questions or need further assistance, feel free to ask!\n"]}], "source": ["import tempfile\n", "\n", "from autogen_core import SingleThreadedAgentRuntime\n", "from autogen_ext.code_executors.docker import DockerCommandLineCodeExecutor\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "work_dir = tempfile.mkdtemp()\n", "\n", "# Create an local embedded runtime.\n", "runtime = SingleThreadedAgentRuntime()\n", "\n", "async with DockerCommandLineCodeExecutor(work_dir=work_dir) as executor:  # type: ignore[syntax]\n", "    # Register the assistant and executor agents by providing\n", "    # their agent types, the factory functions for creating instance and subscriptions.\n", "    await Assistant.register(\n", "        runtime,\n", "        \"assistant\",\n", "        lambda: Assistant(\n", "            OpenAIChatCompletionClient(\n", "                model=\"gpt-4o\",\n", "                # api_key=\"YOUR_API_KEY\"\n", "            )\n", "        ),\n", "    )\n", "    await Executor.register(runtime, \"executor\", lambda: Executor(executor))\n", "\n", "    # Start the runtime and publish a message to the assistant.\n", "    runtime.start()\n", "    await runtime.publish_message(\n", "        Message(\"Create a plot of NVIDA vs TSLA stock returns YTD from 2024-01-01.\"), DefaultTopicId()\n", "    )\n", "    await runtime.stop_when_idle()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the agent's output, we can see the plot of Tesla's and Nvidia's stock returns\n", "has been created."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Image\n", "\n", "Image(filename=f\"{work_dir}/nvidia_vs_tesla_ytd_returns.png\")  # type: ignore"]}, {"cell_type": "markdown", "metadata": {}, "source": ["AutoGen also supports a distributed agent runtime, which can host agents running on\n", "different processes or machines, with different identities, languages and dependencies.\n", "\n", "To learn how to use agent runtime, communication, message handling, and subscription, please continue\n", "reading the sections following this quick start."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}