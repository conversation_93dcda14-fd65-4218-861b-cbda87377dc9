{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Custom Agents\n", "\n", "You may have agents with behaviors that do not fall into a preset. \n", "In such cases, you can build custom agents.\n", "\n", "All agents in AgentChat inherit from {py:class}`~autogen_agentchat.agents.BaseChatAgent` \n", "class and implement the following abstract methods and attributes:\n", "\n", "- {py:meth}`~autogen_agentchat.agents.BaseChatAgent.on_messages`: The abstract method that defines the behavior of the agent in response to messages. This method is called when the agent is asked to provide a response in {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run`. It returns a {py:class}`~autogen_agentchat.base.Response` object.\n", "- {py:meth}`~autogen_agentchat.agents.BaseChatAgent.on_reset`: The abstract method that resets the agent to its initial state. This method is called when the agent is asked to reset itself.\n", "- {py:attr}`~autogen_agentchat.agents.BaseChatAgent.produced_message_types`: The list of possible {py:class}`~autogen_agentchat.messages.ChatMessage` message types the agent can produce in its response.\n", "\n", "Optionally, you can implement the the {py:meth}`~autogen_agentchat.agents.BaseChatAgent.on_messages_stream` method to stream messages as they are generated by the agent. If this method is not implemented, the agent\n", "uses the default implementation of {py:meth}`~autogen_agentchat.agents.BaseChatAgent.on_messages_stream`\n", "that calls the {py:meth}`~autogen_agentchat.agents.BaseChatAgent.on_messages` method and\n", "yields all messages in the response."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CountDownAgent\n", "\n", "In this example, we create a simple agent that counts down from a given number to zero,\n", "and produces a stream of messages with the current count."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3...\n", "2...\n", "1...\n", "Done!\n"]}], "source": ["from typing import AsyncGenerator, List, Sequence, Tuple\n", "\n", "from autogen_agentchat.agents import BaseChatAgent\n", "from autogen_agentchat.base import Response\n", "from autogen_agentchat.messages import AgentEvent, ChatMessage, TextMessage\n", "from autogen_core import CancellationToken\n", "\n", "\n", "class CountDownAgent(BaseChatAgent):\n", "    def __init__(self, name: str, count: int = 3):\n", "        super().__init__(name, \"A simple agent that counts down.\")\n", "        self._count = count\n", "\n", "    @property\n", "    def produced_message_types(self) -> Sequence[type[ChatMessage]]:\n", "        return (TextMessage,)\n", "\n", "    async def on_messages(self, messages: Sequence[ChatMessage], cancellation_token: CancellationToken) -> Response:\n", "        # Calls the on_messages_stream.\n", "        response: Response | None = None\n", "        async for message in self.on_messages_stream(messages, cancellation_token):\n", "            if isinstance(message, Response):\n", "                response = message\n", "        assert response is not None\n", "        return response\n", "\n", "    async def on_messages_stream(\n", "        self, messages: Sequence[ChatMessage], cancellation_token: CancellationToken\n", "    ) -> AsyncGenerator[AgentEvent | ChatMessage | Response, None]:\n", "        inner_messages: List[AgentEvent | ChatMessage] = []\n", "        for i in range(self._count, 0, -1):\n", "            msg = TextMessage(content=f\"{i}...\", source=self.name)\n", "            inner_messages.append(msg)\n", "            yield msg\n", "        # The response is returned at the end of the stream.\n", "        # It contains the final message and all the inner messages.\n", "        yield Response(chat_message=TextMessage(content=\"Done!\", source=self.name), inner_messages=inner_messages)\n", "\n", "    async def on_reset(self, cancellation_token: CancellationToken) -> None:\n", "        pass\n", "\n", "\n", "async def run_countdown_agent() -> None:\n", "    # Create a countdown agent.\n", "    countdown_agent = CountDownAgent(\"countdown\")\n", "\n", "    # Run the agent with a given task and stream the response.\n", "    async for message in countdown_agent.on_messages_stream([], CancellationToken()):\n", "        if isinstance(message, Response):\n", "            print(message.chat_message.content)\n", "        else:\n", "            print(message.content)\n", "\n", "\n", "# Use asyncio.run(run_countdown_agent()) when running in a script.\n", "await run_countdown_agent()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ArithmeticAgent\n", "\n", "In this example, we create an agent class that can perform simple arithmetic operations\n", "on a given integer. Then, we will use different instances of this agent class\n", "in a {py:class}`~autogen_agentchat.teams.SelectorGroupChat`\n", "to transform a given integer into another integer by applying a sequence of arithmetic operations.\n", "\n", "The `ArithmeticAgent` class takes an `operator_func` that takes an integer and returns an integer,\n", "after applying an arithmetic operation to the integer.\n", "In its `on_messages` method, it applies the `operator_func` to the integer in the input message,\n", "and returns a response with the result."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Callable, Sequence\n", "\n", "from autogen_agentchat.agents import BaseChatAgent\n", "from autogen_agentchat.base import Response\n", "from autogen_agentchat.conditions import MaxMessageTermination\n", "from autogen_agentchat.messages import ChatMessage\n", "from autogen_agentchat.teams import SelectorGroupChat\n", "from autogen_agentchat.ui import Console\n", "from autogen_core import CancellationToken\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "\n", "class ArithmeticAgent(BaseChatAgent):\n", "    def __init__(self, name: str, description: str, operator_func: Callable[[int], int]) -> None:\n", "        super().__init__(name, description=description)\n", "        self._operator_func = operator_func\n", "        self._message_history: List[ChatMessage] = []\n", "\n", "    @property\n", "    def produced_message_types(self) -> Sequence[type[ChatMessage]]:\n", "        return (TextMessage,)\n", "\n", "    async def on_messages(self, messages: Sequence[ChatMessage], cancellation_token: CancellationToken) -> Response:\n", "        # Update the message history.\n", "        # NOTE: it is possible the messages is an empty list, which means the agent was selected previously.\n", "        self._message_history.extend(messages)\n", "        # Parse the number in the last message.\n", "        assert isinstance(self._message_history[-1], TextMessage)\n", "        number = int(self._message_history[-1].content)\n", "        # Apply the operator function to the number.\n", "        result = self._operator_func(number)\n", "        # Create a new message with the result.\n", "        response_message = TextMessage(content=str(result), source=self.name)\n", "        # Update the message history.\n", "        self._message_history.append(response_message)\n", "        # Return the response.\n", "        return Response(chat_message=response_message)\n", "\n", "    async def on_reset(self, cancellation_token: CancellationToken) -> None:\n", "        pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```{note}\n", "The `on_messages` method may be called with an empty list of messages, in which\n", "case it means the agent was called previously and is now being called again,\n", "without any new messages from the caller. So it is important to keep a history\n", "of the previous messages received by the agent, and use that history to generate\n", "the response.\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can create a {py:class}`~autogen_agentchat.teams.SelectorGroupChat` with 5 instances of `ArithmeticAgent`:\n", "\n", "- one that adds 1 to the input integer,\n", "- one that subtracts 1 from the input integer,\n", "- one that multiplies the input integer by 2,\n", "- one that divides the input integer by 2 and rounds down to the nearest integer, and\n", "- one that returns the input integer unchanged.\n", "\n", "We then create a {py:class}`~autogen_agentchat.teams.SelectorGroupChat` with these agents,\n", "and set the appropriate selector settings:\n", "\n", "- allow the same agent to be selected consecutively to allow for repeated operations, and\n", "- customize the selector prompt to tailor the model's response to the specific task."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- user ----------\n", "Apply the operations to turn the given number into 25.\n", "---------- user ----------\n", "10\n", "---------- multiply_agent ----------\n", "20\n", "---------- add_agent ----------\n", "21\n", "---------- multiply_agent ----------\n", "42\n", "---------- divide_agent ----------\n", "21\n", "---------- add_agent ----------\n", "22\n", "---------- add_agent ----------\n", "23\n", "---------- add_agent ----------\n", "24\n", "---------- add_agent ----------\n", "25\n", "---------- Summary ----------\n", "Number of messages: 10\n", "Finish reason: Maximum number of messages 10 reached, current message count: 10\n", "Total prompt tokens: 0\n", "Total completion tokens: 0\n", "Duration: 2.40 seconds\n"]}], "source": ["async def run_number_agents() -> None:\n", "    # Create agents for number operations.\n", "    add_agent = ArithmeticAgent(\"add_agent\", \"Adds 1 to the number.\", lambda x: x + 1)\n", "    multiply_agent = ArithmeticAgent(\"multiply_agent\", \"Multiplies the number by 2.\", lambda x: x * 2)\n", "    subtract_agent = ArithmeticAgent(\"subtract_agent\", \"Subtracts 1 from the number.\", lambda x: x - 1)\n", "    divide_agent = ArithmeticAgent(\"divide_agent\", \"Divides the number by 2 and rounds down.\", lambda x: x // 2)\n", "    identity_agent = ArithmeticAgent(\"identity_agent\", \"Returns the number as is.\", lambda x: x)\n", "\n", "    # The termination condition is to stop after 10 messages.\n", "    termination_condition = MaxMessageTermination(10)\n", "\n", "    # Create a selector group chat.\n", "    selector_group_chat = SelectorGroupChat(\n", "        [add_agent, multiply_agent, subtract_agent, divide_agent, identity_agent],\n", "        model_client=OpenAIChatCompletionClient(model=\"gpt-4o\"),\n", "        termination_condition=termination_condition,\n", "        allow_repeated_speaker=True,  # Allow the same agent to speak multiple times, necessary for this task.\n", "        selector_prompt=(\n", "            \"Available roles:\\n{roles}\\nTheir job descriptions:\\n{participants}\\n\"\n", "            \"Current conversation history:\\n{history}\\n\"\n", "            \"Please select the most appropriate role for the next message, and only return the role name.\"\n", "        ),\n", "    )\n", "\n", "    # Run the selector group chat with a given task and stream the response.\n", "    task: List[ChatMessage] = [\n", "        TextMessage(content=\"Apply the operations to turn the given number into 25.\", source=\"user\"),\n", "        TextMessage(content=\"10\", source=\"user\"),\n", "    ]\n", "    stream = selector_group_chat.run_stream(task=task)\n", "    await <PERSON><PERSON><PERSON>(stream)\n", "\n", "\n", "# Use asyncio.run(run_number_agents()) when running in a script.\n", "await run_number_agents()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the output, we can see that the agents have successfully transformed the input integer\n", "from 10 to 25 by choosing appropriate agents that apply the arithmetic operations in sequence."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}