---
myst:
  html_meta:
    "description lang=en": |
      Examples built using AgentChat, a high-level api for AutoGen
---

# Examples

A list of examples to help you get started with AgentChat.

:::::{grid} 2 2 2 3

::::{grid-item-card} Travel Planning
:img-top: ../../../images/example-travel.jpeg
:img-alt: travel planning example
:link: ./travel-planning.html

^^^
Generating a travel plan using multiple agents.

::::

::::{grid-item-card} Company Research
:img-top: ../../../images/example-company.jpg
:img-alt: company research example
:link: ./company-research.html

^^^
Generating a company research report using multiple agents with tools.

::::

::::{grid-item-card} Literature Review
:img-top: ../../../images/example-literature.jpg
:img-alt: literature review example
:link: ./literature-review.html

^^^
Generating a literature review using agents with tools.

::::

:::::

```{toctree}
:maxdepth: 1
:hidden:

travel-planning
company-research
literature-review

```
