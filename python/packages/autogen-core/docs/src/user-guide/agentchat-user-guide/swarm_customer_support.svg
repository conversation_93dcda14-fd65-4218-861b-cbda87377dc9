<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="338px" height="340px" viewBox="-0.5 -0.5 338 340" class="ge-export-svg-auto" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@media (prefers-color-scheme: dark) {&#xa;svg.ge-export-svg-auto:not(mjx-container &gt; svg) { filter: invert(100%) hue-rotate(180deg); }&#xa;svg.ge-export-svg-auto foreignObject img,&#xa;svg.ge-export-svg-auto image:not(svg.ge-export-svg-auto switch image),&#xa;svg.ge-export-svg-auto svg:not(mjx-container &gt; svg)&#xa;{ filter: invert(100%) hue-rotate(180deg) }&#xa;svg.ge-export-svg-auto { background-color: #000000 !important; }&#xa;}</style></defs><rect fill="#ffffff" width="100%" height="100%" x="0" y="0"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="pQ48hnmP3BsQIGEezLn9-3"><g><rect x="233" y="112" width="104" height="57" rx="8.55" ry="8.55" fill="none" stroke="#ff6666" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 102px; height: 1px; padding-top: 141px; margin-left: 234px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Application/User</div></div></div></foreignObject><text x="285" y="144" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Application/User</text></switch></g></g></g><g data-cell-id="ICtxptakJxyu4_XYJ3g6-9"><g><rect x="8" y="0" width="47" height="26" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 13px; margin-left: 32px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">Team</div></div></div></foreignObject><text x="32" y="17" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Team</text></switch></g></g></g><g data-cell-id="ICtxptakJxyu4_XYJ3g6-14"><g><rect x="0" y="26" width="145" height="313" rx="21.75" ry="21.75" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/></g></g><g data-cell-id="ICtxptakJxyu4_XYJ3g6-15"><g><rect x="12.5" y="45" width="120" height="61" rx="9.15" ry="9.15" fill="none" stroke="#9999ff" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 76px; margin-left: 14px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Travel Agent</div></div></div></foreignObject><text x="73" y="79" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Travel Agent</text></switch></g></g></g><g data-cell-id="ICtxptakJxyu4_XYJ3g6-16"><g><rect x="12.5" y="179" width="120" height="60" rx="9" ry="9" fill="none" stroke="#9999ff" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 209px; margin-left: 14px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="background-color: initial; --darkreader-inline-bgcolor: initial;" data-darkreader-inline-bgcolor="">Flights Refunder Agent</span></div></div></div></div></foreignObject><text x="73" y="213" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Flights Refunder Age...</text></switch></g></g></g><g data-cell-id="ICtxptakJxyu4_XYJ3g6-17"><g><path d="M 72.5 172.63 L 72.5 112.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 72.5 177.88 L 69 170.88 L 72.5 172.63 L 76 170.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 72.5 107.12 L 76 114.12 L 72.5 112.37 L 69 114.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g data-cell-id="ICtxptakJxyu4_XYJ3g6-18"><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 146px; margin-left: 72px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><font style="font-size: 10px;">Handoff Message</font></div></div></div></foreignObject><text x="72" y="150" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">Handoff Message</text></switch></g></g></g></g><g data-cell-id="ICtxptakJxyu4_XYJ3g6-19"><g><path d="M 21 273.5 C 21 269.91 44.06 267 72.5 267 C 86.16 267 99.26 267.68 108.92 268.9 C 118.57 270.12 124 271.78 124 273.5 L 124 305.5 C 124 307.22 118.57 308.88 108.92 310.1 C 99.26 311.32 86.16 312 72.5 312 C 58.84 312 45.74 311.32 36.08 310.1 C 26.43 308.88 21 307.22 21 305.5 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 124 273.5 C 124 275.22 118.57 276.88 108.92 278.1 C 99.26 279.32 86.16 280 72.5 280 C 58.84 280 45.74 279.32 36.08 278.1 C 26.43 276.88 21 275.22 21 273.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 101px; height: 1px; padding-top: 295px; margin-left: 22px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">refund_flight</div></div></div></foreignObject><text x="73" y="299" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">refund_flight</text></switch></g></g></g><g data-cell-id="ICtxptakJxyu4_XYJ3g6-20"><g><path d="M 70.5 239 L 72.05 260.65" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 72.42 265.88 L 68.43 259.15 L 72.05 260.65 L 75.41 258.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="OCi7m8iZUGjjIboypNOs-2"><g><path d="M 151.08 139.96 L 226.63 140.26" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 145.83 139.94 L 152.84 136.46 L 151.08 139.96 L 152.81 143.46 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 231.88 140.28 L 224.87 143.76 L 226.63 140.26 L 224.9 136.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g data-cell-id="OCi7m8iZUGjjIboypNOs-3"><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 139px; margin-left: 195px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><font style="font-size: 10px;">Handoff</font><div><font style="font-size: 10px;">Message</font></div></div></div></div></foreignObject><text x="195" y="143" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">Handoff...</text></switch></g></g></g></g></g></g></g></svg>