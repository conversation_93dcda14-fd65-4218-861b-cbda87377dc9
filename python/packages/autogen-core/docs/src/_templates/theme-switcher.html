{# Displays an icon to switch between light mode, dark mode, and auto (use browser's setting). #}
{# As the theme switcher will only work when JavaScript is enabled, we hide it with `pst-js-only`. #}
<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="{{ _('Color mode') }}" data-bs-title="{{ _('Color mode') }}"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="{{ _('Light') }}"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="{{ _('Dark') }}"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="{{ _('System Settings') }}"></i>
</button>