{# Displays the TOC-subtree for pages nested under the currently active top-level TOCtree element. #}

<nav class="bd-docs-nav bd-links" aria-label="{{ _('Section Navigation') }}">
    <div class="bd-toc-item navbar-nav">
        {{- generate_toctree_html(
        "sidebar",
        show_nav_level=theme_show_nav_level | int,
        maxdepth=theme_navigation_depth | int,
        collapse=theme_collapse_navigation | tobool,
        includehidden=theme_sidebar_includehidden | tobool,
        titles_only=True
        )
        -}}
        <p aria-level="2" class="caption" role="heading"><span class="caption-text">More</span></p>
        <ul class="nav bd-sidenav">
            <li class="toctree-l1">
                <a class="reference internal" href="{{pathto('reference/python/autogen_ext.agents.magentic_one')}}">
                    <i class="fa-solid fa-file-code"></i>
                    API Reference
                </a>
            </li>
            <li class="toctree-l1">
                <a target="_blank" class="reference internal" href="https://pypi.org/project/autogen-ext/">
                    <i class="fa-brands fa-python"></i>
                    PyPi
                    <i class="fa-solid fa-arrow-up-right-from-square fa-2xs"></i>
                </a>
            </li>
            <li class="toctree-l1">
                <a target="_blank" class="reference internal"
                    href="https://github.com/microsoft/autogen/tree/main/python/packages/autogen-ext">
                    <i class="fa-brands fa-github"></i>
                    Source
                    <i class="fa-solid fa-arrow-up-right-from-square fa-2xs"></i>
                </a>
            </li>
        </ul>
    </div>
</nav>
