<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="24.8.9">
  <diagram id="-WALZMLRurTB1BmUSyM2" name="Page-1">
    <mxGraphModel dx="1645" dy="1089" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="EN7TMz6usMrMiW2YQY0C-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="EN7TMz6usMrMiW2YQY0C-2" target="EN7TMz6usMrMiW2YQY0C-12">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="455" y="760" />
              <mxPoint x="230" y="760" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="380" y="460" width="300" height="260" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="EN7TMz6usMrMiW2YQY0C-6" target="EN7TMz6usMrMiW2YQY0C-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="EN7TMz6usMrMiW2YQY0C-6" target="EN7TMz6usMrMiW2YQY0C-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="EN7TMz6usMrMiW2YQY0C-6" target="EN7TMz6usMrMiW2YQY0C-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-6" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="410" y="530" width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="EN7TMz6usMrMiW2YQY0C-7" target="EN7TMz6usMrMiW2YQY0C-12">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="690" y="605" />
              <mxPoint x="690" y="740" />
              <mxPoint x="290" y="740" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-7" value="&lt;b&gt;UserProxyAgent&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="550" y="560" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-8" value="Agent 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="550" y="480" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-9" value="&lt;div&gt;Agent 3&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="550" y="640" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-10" value="RoundRobinGroupChat" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="385" y="470" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="EN7TMz6usMrMiW2YQY0C-12" target="EN7TMz6usMrMiW2YQY0C-2">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="230" y="420" />
              <mxPoint x="455" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="EN7TMz6usMrMiW2YQY0C-12" target="EN7TMz6usMrMiW2YQY0C-7">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="290" y="440" />
              <mxPoint x="690" y="440" />
              <mxPoint x="690" y="575" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-12" value="Application/User" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="200" y="550" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-13" value="Task" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="180" y="495" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-14" value="TaskResult" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="160" y="660" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-15" value="Termination&lt;div&gt;Condition&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="415" y="590" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-16" value="&lt;span style=&quot;text-wrap-mode: wrap;&quot;&gt;Orchestrator&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="415" y="540" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-26" value="User Input Response" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="690" y="530" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EN7TMz6usMrMiW2YQY0C-27" value="Request for User Input" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="690" y="620" width="140" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
