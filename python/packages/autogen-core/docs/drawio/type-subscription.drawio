<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="24.7.14" pages="3">
  <diagram id="ur-R44z3ZgcMblGIPqHk" name="single-tenant-single-topic">
    <mxGraphModel dx="1033" dy="697" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="152" y="190" width="510" height="530" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="322" y="210" width="170" height="150" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-26" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-4" target="qLG5ST6lc1lPdu2IUqXs-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-54" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-4" target="qLG5ST6lc1lPdu2IUqXs-52">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-4" value="Topic_1&lt;div&gt;type=&quot;default&quot;&lt;br&gt;source=&quot;default&quot;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="179.5" y="425" width="110.5" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-5" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-6" target="qLG5ST6lc1lPdu2IUqXs-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-6" value="AgentID_1&lt;div&gt;type=&quot;triage_agent&quot;, key=&quot;default&quot;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="502" y="290" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-7" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-8" target="qLG5ST6lc1lPdu2IUqXs-4">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="147" y="325" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-8" value="Message" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="40" y="440" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-9" value="Publish" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="92" y="425" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-10" value="TriageAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="712" y="301" width="86" height="40" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-11" value="Deliver" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="652" y="286" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-12" value="" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-13" target="qLG5ST6lc1lPdu2IUqXs-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-13" value="Subscription&lt;div&gt;Topic_1 -&amp;gt; AgentID_1&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="339.5" y="290" width="135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-14" value="Auto-generated from TypeSubscription&lt;div&gt;topic_type=&quot;default&quot;&lt;/div&gt;&lt;div&gt;agent_type=&quot;triage_agent&quot;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="319.5" y="220" width="175" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-27" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="322" y="380" width="170" height="150" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-28" target="qLG5ST6lc1lPdu2IUqXs-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-28" value="AgentID_2&lt;div&gt;type=&quot;coder_agent&quot;, key=&quot;default&quot;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="502" y="460" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-29" value="CoderAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="712" y="470" width="86" height="40" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-30" value="Deliver" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="652" y="456" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-31" target="qLG5ST6lc1lPdu2IUqXs-28">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-31" value="Subscription&lt;div&gt;Topic_1 -&amp;gt; AgentID_2&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="339.5" y="460" width="135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-32" value="Auto-generated from TypeSubscription&lt;div&gt;topic_type=&quot;default&quot;&lt;/div&gt;&lt;div&gt;agent_type=&quot;coder_agent&quot;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="319.5" y="390" width="175" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-37" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-4" target="qLG5ST6lc1lPdu2IUqXs-31">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="300" y="405" as="sourcePoint" />
            <mxPoint x="350" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-46" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="322" y="550" width="170" height="150" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-48" target="qLG5ST6lc1lPdu2IUqXs-49">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-48" value="AgentID_3&lt;div&gt;type=&quot;reviewer_agent&quot;, key=&quot;default&quot;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="502" y="630" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-49" value="ReviewerAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="712" y="640" width="86" height="40" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-50" value="Deliver" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="652" y="626" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qLG5ST6lc1lPdu2IUqXs-52" target="qLG5ST6lc1lPdu2IUqXs-48">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-52" value="Subscription&lt;div&gt;Topic_1 -&amp;gt; AgentID_3&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="339.5" y="630" width="135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qLG5ST6lc1lPdu2IUqXs-53" value="Auto-generated from TypeSubscription&lt;div&gt;topic_type=&quot;default&quot;&lt;/div&gt;&lt;div&gt;agent_type=&quot;reviewer_agent&quot;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="319.5" y="560" width="175" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="77Ucu0bl-2KWu5d5-Jic" name="single-tenant-multiple-topics">
    <mxGraphModel dx="1033" dy="697" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="WBeXbWg1uD67umOqo8Zi-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="152" y="190" width="510" height="530" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="322" y="210" width="170" height="150" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-3" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-5" target="WBeXbWg1uD67umOqo8Zi-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-5" value="Topic_1&lt;div&gt;type=&quot;triage&quot;&lt;br&gt;source=&quot;default&quot;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="180" y="290" width="110.5" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-6" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-7" target="WBeXbWg1uD67umOqo8Zi-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-7" value="AgentID_1&lt;div&gt;type=&quot;triage_agent&quot;, key=&quot;default&quot;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="502" y="290" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-8" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-9" target="WBeXbWg1uD67umOqo8Zi-5">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="147" y="325" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-9" value="Message" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="50" y="306" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-10" value="Publish" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="92" y="290" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-11" value="TriageAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="712" y="301" width="86" height="40" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-12" value="Deliver" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="652" y="286" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-13" value="" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-14" target="WBeXbWg1uD67umOqo8Zi-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-14" value="Subscription&lt;div&gt;Topic_1 -&amp;gt; AgentID_1&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="339.5" y="290" width="135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-15" value="Auto-generated from TypeSubscription&lt;div&gt;topic_type=&quot;triage&quot;&lt;/div&gt;&lt;div&gt;agent_type=&quot;triage_agent&quot;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="319.5" y="220" width="175" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-16" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="322" y="380" width="170" height="150" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-18" target="WBeXbWg1uD67umOqo8Zi-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-18" value="AgentID_2&lt;div&gt;type=&quot;coder_agent&quot;, key=&quot;default&quot;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="502" y="460" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-19" value="CoderAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="712" y="470" width="86" height="40" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-20" value="Deliver" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="652" y="456" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-22" target="WBeXbWg1uD67umOqo8Zi-18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-22" value="Subscription&lt;div&gt;Topic_2 -&amp;gt; AgentID_2&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="339.5" y="460" width="135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-23" value="Auto-generated from TypeSubscription&lt;div&gt;topic_type=&quot;coding&quot;&lt;/div&gt;&lt;div&gt;agent_type=&quot;coder_agent&quot;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="319.5" y="390" width="175" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-25" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="322" y="550" width="170" height="150" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-27" target="WBeXbWg1uD67umOqo8Zi-28">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-27" value="AgentID_3&lt;div&gt;type=&quot;reviewer_agent&quot;, key=&quot;default&quot;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="502" y="630" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-28" value="ReviewerAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="712" y="640" width="86" height="40" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-29" value="Deliver" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="652" y="626" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-31" target="WBeXbWg1uD67umOqo8Zi-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-31" value="Subscription&lt;div&gt;Topic_2 -&amp;gt; AgentID_3&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="339.5" y="630" width="135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-32" value="Auto-generated from TypeSubscription&lt;div&gt;topic_type=&quot;coding&quot;&lt;/div&gt;&lt;div&gt;agent_type=&quot;reviewer_agent&quot;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="319.5" y="560" width="175" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-33" value="Topic_2&lt;div&gt;type=&quot;coding&quot;&lt;br&gt;source=&quot;default&quot;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="180" y="540" width="110.5" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-38" value="Message" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="50" y="556" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-39" value="Publish" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="92" y="540" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-40" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-38" target="WBeXbWg1uD67umOqo8Zi-33">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="190" y="330" as="targetPoint" />
            <mxPoint x="120" y="331" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-41" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-33" target="WBeXbWg1uD67umOqo8Zi-31">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="200" y="340" as="targetPoint" />
            <mxPoint x="290" y="580" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WBeXbWg1uD67umOqo8Zi-42" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;" edge="1" parent="1" source="WBeXbWg1uD67umOqo8Zi-33" target="WBeXbWg1uD67umOqo8Zi-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="350" y="670" as="targetPoint" />
            <mxPoint x="301" y="595" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="SEMb81fnvzcG-_hcjzhJ" name="multi-tenant">
    <mxGraphModel dx="1033" dy="697" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="150" y="410" width="510" height="290" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-50" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="420" width="170" height="270" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-42" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="0t9SMKTS3B_TfDHjOXI4-5" target="0t9SMKTS3B_TfDHjOXI4-37" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-5" value="Topic_1&lt;div&gt;type=&quot;github_issues&quot;&lt;br&gt;source=&quot;.../issues/1&quot;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="170" y="520" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-13" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="0t9SMKTS3B_TfDHjOXI4-14" target="0t9SMKTS3B_TfDHjOXI4-24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-14" value="AgentID_1&lt;div&gt;type=&quot;triage_agent&quot;, key=&quot;.,./issues/1&quot;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="500" y="520" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-21" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="0t9SMKTS3B_TfDHjOXI4-22" target="0t9SMKTS3B_TfDHjOXI4-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="145" y="555" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-22" value="Message" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="51" y="536" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-23" value="Publish" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="96" y="516" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-24" value="TriageAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="710" y="531" width="86" height="40" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-28" value="Deliver" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="650" y="516" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-43" value="" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="0t9SMKTS3B_TfDHjOXI4-37" target="0t9SMKTS3B_TfDHjOXI4-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-37" value="Subscription&lt;div&gt;Topic_1 -&amp;gt; AgentID_1&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="337.5" y="520" width="135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-49" value="Auto-generated from TypeSubscription&lt;div&gt;topic_type=&quot;github_issues&quot;&lt;/div&gt;&lt;div&gt;agent_type=&quot;triage_agent&quot;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="317.5" y="430" width="175" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-63" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="0t9SMKTS3B_TfDHjOXI4-64" target="0t9SMKTS3B_TfDHjOXI4-73" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-64" value="Topic_9&lt;div&gt;type=&quot;github_issues&quot;&lt;br&gt;source=&quot;.../issues/9&quot;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="170" y="614" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-65" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="0t9SMKTS3B_TfDHjOXI4-66" target="0t9SMKTS3B_TfDHjOXI4-70" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-66" value="AgentID_9&lt;div&gt;type=&quot;triage_agent&quot;, key=&quot;.,./issues/9&quot;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="500" y="614" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-67" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="0t9SMKTS3B_TfDHjOXI4-68" target="0t9SMKTS3B_TfDHjOXI4-64" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="145" y="649" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-68" value="Message" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="51" y="630" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-69" value="Publish" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="96" y="610" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-70" value="TriageAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="710" y="625" width="86" height="40" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-71" value="Deliver" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="650" y="610" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-72" value="" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="0t9SMKTS3B_TfDHjOXI4-73" target="0t9SMKTS3B_TfDHjOXI4-66" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0t9SMKTS3B_TfDHjOXI4-73" value="Subscription&lt;div&gt;Topic_9 -&amp;gt; AgentID_9&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="337.5" y="614" width="135" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
