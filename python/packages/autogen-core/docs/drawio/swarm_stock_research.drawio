<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0" scale="1" border="0" version="24.8.4">
  <diagram name="Page-1" id="y5g9oLqhGPSVJRIakE2-">
    <mxGraphModel dx="748" dy="618" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2050" pageHeight="2000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="pQ48hnmP3BsQIGEezLn9-2" value="Planner" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;" parent="1" vertex="1">
          <mxGeometry x="607" y="276" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-3" value="Writer" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;" parent="1" vertex="1">
          <mxGeometry x="773" y="381" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-4" value="News&amp;nbsp;&lt;br&gt;&lt;div&gt;&lt;span data-darkreader-inline-color=&quot;&quot; data-darkreader-inline-bgcolor=&quot;&quot; style=&quot;font-family: monospace; font-size: 0px; text-align: start; text-wrap-mode: nowrap; background-color: initial; color: rgb(0, 0, 0); --darkreader-inline-bgcolor: initial; --darkreader-inline-color: #e8e6e3;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22Writer%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeColor%3D%239999FF%3BgradientColor%3Ddefault%3BfillColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22780%22%20y%3D%22377%22%20width%3D%22120%22%20height%3D%2260%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;Analyst&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;" parent="1" vertex="1">
          <mxGeometry x="607" y="409" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-5" value="&lt;div&gt;Financial&lt;/div&gt;&lt;div&gt;Analyst&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;" parent="1" vertex="1">
          <mxGeometry x="434" y="381" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-6" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;" parent="1" source="pQ48hnmP3BsQIGEezLn9-5" target="pQ48hnmP3BsQIGEezLn9-2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="706" y="376" as="sourcePoint" />
            <mxPoint x="756" y="326" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-9" value="Handoff" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pQ48hnmP3BsQIGEezLn9-6" vertex="1" connectable="0">
          <mxGeometry x="-0.0649" y="1" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-7" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="pQ48hnmP3BsQIGEezLn9-4" target="pQ48hnmP3BsQIGEezLn9-2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="706" y="376" as="sourcePoint" />
            <mxPoint x="756" y="326" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-10" value="Handoff" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pQ48hnmP3BsQIGEezLn9-7" vertex="1" connectable="0">
          <mxGeometry x="0.0574" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-8" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.954;exitY=1.021;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0;entryDx=0;entryDy=0;" parent="1" source="pQ48hnmP3BsQIGEezLn9-2" target="pQ48hnmP3BsQIGEezLn9-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="706" y="376" as="sourcePoint" />
            <mxPoint x="756" y="326" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pQ48hnmP3BsQIGEezLn9-11" value="Handoff" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pQ48hnmP3BsQIGEezLn9-8" vertex="1" connectable="0">
          <mxGeometry x="-0.0082" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4B5533AgZox_s6zzr7VT-1" value="&lt;font style=&quot;font-size: 10px;&quot;&gt;get_stock_data&lt;/font&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=5.931034482758548;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="453.5" y="469" width="81" height="49" as="geometry" />
        </mxCell>
        <mxCell id="4B5533AgZox_s6zzr7VT-2" value="&lt;font style=&quot;font-size: 10px;&quot;&gt;get_news&lt;/font&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=5.758620689655288;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="626.5" y="497" width="81" height="47" as="geometry" />
        </mxCell>
        <mxCell id="4B5533AgZox_s6zzr7VT-3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="pQ48hnmP3BsQIGEezLn9-5" target="4B5533AgZox_s6zzr7VT-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="847" y="446" as="sourcePoint" />
            <mxPoint x="897" y="396" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4B5533AgZox_s6zzr7VT-4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="pQ48hnmP3BsQIGEezLn9-4" target="4B5533AgZox_s6zzr7VT-2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="671" y="470" as="sourcePoint" />
            <mxPoint x="721" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
