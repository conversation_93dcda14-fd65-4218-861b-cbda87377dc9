<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="25.0.3">
  <diagram name="Page-1" id="J3B7wUMKLXaSRlGf0apQ">
    <mxGraphModel dx="436" dy="1180" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="2050" pageHeight="2000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-1" value="Concept&amp;nbsp;&lt;span style=&quot;background-color: initial;&quot;&gt;Extractor&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;Agent&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="592" y="394" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-2" value="&lt;div&gt;Writer Agent&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="805" y="394" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-3" value="&lt;div&gt;Format&amp;nbsp;&lt;span style=&quot;background-color: initial;&quot;&gt;Proof&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;Agent&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="1020" y="394" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-4" value="&lt;div&gt;User Agent&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="1216" y="394" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-5" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="712" y="423.75" as="sourcePoint" />
            <mxPoint x="805" y="423.75" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-11" value="Product&lt;div&gt;Concepts&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="sqq-OtPLxvXwgYRML6Fe-5">
          <mxGeometry x="0.1833" y="1" relative="1" as="geometry">
            <mxPoint x="-12" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-6" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="sqq-OtPLxvXwgYRML6Fe-2" target="sqq-OtPLxvXwgYRML6Fe-3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="891" y="420" as="sourcePoint" />
            <mxPoint x="941" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-13" value="Marketing&amp;nbsp;&lt;div&gt;Copy&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="sqq-OtPLxvXwgYRML6Fe-6">
          <mxGeometry x="-0.3085" relative="1" as="geometry">
            <mxPoint x="16" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-7" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="sqq-OtPLxvXwgYRML6Fe-3" target="sqq-OtPLxvXwgYRML6Fe-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1076" y="429" as="sourcePoint" />
            <mxPoint x="1132" y="423.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-14" value="Final&amp;nbsp;&lt;div&gt;Copy&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="sqq-OtPLxvXwgYRML6Fe-7">
          <mxGeometry x="-0.4265" y="1" relative="1" as="geometry">
            <mxPoint x="13" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-9" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="sqq-OtPLxvXwgYRML6Fe-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="652" y="324" as="sourcePoint" />
            <mxPoint x="671" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sqq-OtPLxvXwgYRML6Fe-15" value="Product Description" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="sqq-OtPLxvXwgYRML6Fe-9">
          <mxGeometry x="-0.3657" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
