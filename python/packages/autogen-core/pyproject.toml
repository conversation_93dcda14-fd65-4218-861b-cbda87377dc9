[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "autogen-core"
version = "0.4.4"
license = {file = "LICENSE-CODE"}
description = "Foundational interfaces and agent runtime implementation for AutoGen"
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "pillow>=11.0.0",
    "typing-extensions>=4.0.0",
    "pydantic<3.0.0,>=2.10.0",
    "protobuf~=4.25.1",
    "opentelemetry-api>=1.27.0",
    "jsonref~=1.1.0",
]


[dependency-groups]
dev = [
    "aiofiles",
    "asyncio_atexit",
    "autogen_test_utils",
    "azure-identity",
    "chess",
    "colorama",
    "langchain-openai",
    "langgraph",
    "llama-index-embeddings-azure-openai",
    "llama-index-llms-azure-openai",
    "llama-index-readers-web",
    "llama-index-readers-wikipedia",
    "llama-index-tools-wikipedia",
    "llama-index",
    "markdownify",
    "nbqa",
    "opentelemetry-sdk>=1.27.0",
    "pip",
    "polars",
    "python-dotenv",
    "requests",
    "tavily-python",
    "textual-dev",
    "textual-imageview",
    "textual",
    "types-aiofiles",
    "types-docker",
    "types-pillow",
    "types-protobuf",
    "types-requests",
    "wikipedia",

    # Documentation
    "myst-nb==1.1.2",
    "pydata-sphinx-theme==0.15.4",
    "sphinx-copybutton",
    "sphinx-design",
    "sphinx",
    "sphinxcontrib-apidoc",
    "autodoc_pydantic~=2.2",
    "pygments",
    "sphinxext-rediraffe",

    "autogen_ext==0.4.4",

    # Documentation tooling
    "diskcache",
    "redis",
    "sphinx-autobuild",
]


[tool.ruff]
extend = "../../pyproject.toml"
exclude = ["build", "dist", "src/autogen_core/application/protos", "tests/protos"]
include = ["src/**", "docs/**/*.ipynb", "tests/**"]

[tool.ruff.lint.per-file-ignores]
"docs/**.ipynb" = ["T20"]

[tool.pyright]
extends = "../../pyproject.toml"
include = ["src", "tests"]
exclude = ["src/autogen_core/application/protos", "tests/protos"]
reportDeprecated = true

[tool.pytest.ini_options]
minversion = "6.0"
testpaths = ["tests"]
asyncio_default_fixture_loop_scope = "session"

[tool.nbqa.addopts]
mypy = [
    "--disable-error-code=top-level-await"
]

[tool.poe]
include = "../../shared_tasks.toml"

[tool.poe.tasks]
test = "pytest -n auto --cov=src --cov-report=term-missing --cov-report=xml"
mypy.default_item_type = "cmd"
mypy.sequence = [
    "mypy --config-file ../../pyproject.toml --exclude src/autogen_core/application/protos --exclude tests/protos src tests",
    "nbqa mypy docs/src --config-file ../../pyproject.toml",
]

# Docs
docs-clean = "rm -rf docs/build"

# Inline tables are WAY easier to read but for some reason they break pyright. So we have to write it out this way.
# Example of inline table:
# docs-build = [
#     "docs-apidoc-all",
#     { cmd = "sphinx-build docs/src docs/build" }
# ]

docs-build = "sphinx-build docs/src docs/build"

docs-serve = "sphinx-autobuild --watch src docs/src docs/build --port 8000 --jobs auto"

docs-check = "sphinx-build --fail-on-warning docs/src docs/build"

docs-check-examples = "sphinx-build -b code_lint docs/src docs/build"

