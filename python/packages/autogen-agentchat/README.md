# AutoGen AgentChat

- [Documentation](https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/index.html)

AgentChat is a high-level API for building multi-agent applications.
It is built on top of the [`autogen-core`](https://microsoft.github.io/autogen/stable/user-guide/core-user-guide/index.html) package.
For beginner users, AgentChat is the recommended starting point.
For advanced users, [`autogen-core`](https://microsoft.github.io/autogen/stable/user-guide/core-user-guide/index.html)'s event-driven
programming model provides more flexibility and control over the underlying components.

AgentChat provides intuitive defaults, such as **Agents** with preset
behaviors and **Teams** with predefined [multi-agent design patterns](https://microsoft.github.io/autogen/stable/user-guide/core-user-guide/design-patterns/intro.html).
