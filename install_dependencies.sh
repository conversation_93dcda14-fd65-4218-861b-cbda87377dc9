#!/bin/bash
# AIOps智能故障诊断系统依赖安装脚本
# 自动安装所有必需的Python包

set -e  # 遇到错误时退出

echo "🚀 AIOps智能故障诊断系统 - 依赖安装脚本"
echo "============================================================"

# 检查Python版本
echo "🔍 检查Python版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}')
echo "✅ Python版本: $python_version"

# 检查是否在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 检测到虚拟环境: $VIRTUAL_ENV"
else
    echo "⚠️ 建议在虚拟环境中安装依赖"
    echo "创建虚拟环境: python3 -m venv venv && source venv/bin/activate"
    read -p "是否继续安装? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 安装已取消"
        exit 1
    fi
fi

# 升级pip
echo "📦 升级pip..."
python3 -m pip install --upgrade pip

# 安装核心依赖
echo "🔧 安装核心依赖..."
python3 -m pip install -r requirements.txt

# 验证关键包安装
echo "🧪 验证关键包安装..."
python3 -c "
import sys
required_packages = [
    'autogen_core',
    'pandas', 
    'numpy',
    'pydantic',
    'aiofiles',
    'psutil'
]

missing_packages = []
for package in required_packages:
    try:
        __import__(package)
        print(f'✅ {package}')
    except ImportError:
        print(f'❌ {package}')
        missing_packages.append(package)

if missing_packages:
    print(f'\\n❌ 缺少包: {missing_packages}')
    sys.exit(1)
else:
    print('\\n🎉 所有核心包安装成功!')
"

# 检查可选依赖
echo "🔍 检查可选依赖..."
optional_packages=("scikit-learn" "matplotlib" "seaborn")
for package in "${optional_packages[@]}"; do
    if python3 -c "import $package" 2>/dev/null; then
        echo "✅ $package (可选)"
    else
        echo "⚪ $package (可选，未安装)"
    fi
done

echo ""
echo "📊 安装完成统计:"
echo "  核心依赖: ✅ 已安装"
echo "  AIOps诊断系统: ✅ 就绪"
echo "  优化多智能体系统: ✅ 就绪"

echo ""
echo "🎯 下一步:"
echo "  1. 配置环境变量 (如API密钥)"
echo "  2. 运行测试: cd optimized_multiagent && python batch_diagnosis.py --help"
echo "  3. 查看文档: docs/ 目录"

echo ""
echo "🎉 AIOps智能故障诊断系统依赖安装完成!"
