#!/usr/bin/env python3
"""
AIOps智能故障诊断系统依赖检查脚本
检查所有必需的Python包是否正确安装
"""

import sys
import importlib
import subprocess
from typing import List, Tuple, Dict

def check_package(package_name: str, import_name: str = None) -> Tuple[bool, str]:
    """检查单个包是否安装"""
    if import_name is None:
        import_name = package_name.replace('-', '_')
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'unknown')
        return True, version
    except ImportError:
        return False, 'not installed'

def get_package_version(package_name: str) -> str:
    """通过pip获取包版本"""
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'show', package_name], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if line.startswith('Version:'):
                    return line.split(':', 1)[1].strip()
        return 'unknown'
    except:
        return 'unknown'

def main():
    """主检查函数"""
    print("🔍 AIOps智能故障诊断系统 - 依赖检查")
    print("=" * 60)
    
    # 核心依赖包
    core_packages = [
        ('autogen-core', 'autogen_core'),
        ('autogen-ext', 'autogen_ext'),
        ('autogen-agentchat', 'autogen_agentchat'),
        ('openai', 'openai'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('pydantic', 'pydantic'),
        ('aiofiles', 'aiofiles'),
        ('PyYAML', 'yaml'),
        ('requests', 'requests'),
        ('psutil', 'psutil'),
        ('networkx', 'networkx'),
    ]

    # 可选依赖包 (保留少量真正可能需要的)
    optional_packages = [
        ('websockets', 'websockets'),
    ]
    
    print("📦 检查核心依赖...")
    core_missing = []
    core_installed = []
    
    for package_name, import_name in core_packages:
        installed, version = check_package(package_name, import_name)
        if installed:
            print(f"✅ {package_name:<20} {version}")
            core_installed.append(package_name)
        else:
            print(f"❌ {package_name:<20} {version}")
            core_missing.append(package_name)
    
    print(f"\n📦 检查可选依赖...")
    optional_installed = []
    optional_missing = []
    
    for package_name, import_name in optional_packages:
        installed, version = check_package(package_name, import_name)
        if installed:
            print(f"✅ {package_name:<20} {version} (可选)")
            optional_installed.append(package_name)
        else:
            print(f"⚪ {package_name:<20} {version} (可选)")
            optional_missing.append(package_name)
    
    # 检查Python版本
    print(f"\n🐍 Python版本检查...")
    python_version = sys.version_info
    version_str = f"{python_version.major}.{python_version.minor}.{python_version.micro}"
    print(f"✅ Python {version_str}")

    # 检查是否满足最低要求
    if python_version < (3, 12):
        print(f"❌ Python版本不满足要求!")
        print(f"   当前版本: {version_str}")
        print(f"   要求版本: >= 3.12.0")
        print(f"   AIOps系统需要Python 3.12或更高版本")
        print(f"\n🔧 升级建议:")
        print(f"   Ubuntu/Debian: sudo apt update && sudo apt install python3.12")
        print(f"   CentOS/RHEL: sudo yum install python3.12")
        print(f"   macOS: brew install python@3.12")
        print(f"   或使用pyenv: pyenv install 3.12.0 && pyenv global 3.12.0")
        return False
    else:
        print(f"✅ Python版本满足要求 (>= 3.12)")
    
    # 总结
    print(f"\n📊 依赖检查总结:")
    print(f"  核心依赖: {len(core_installed)}/{len(core_packages)} 已安装")
    print(f"  可选依赖: {len(optional_installed)}/{len(optional_packages)} 已安装")
    
    if core_missing:
        print(f"\n❌ 缺少核心依赖: {', '.join(core_missing)}")
        print(f"安装命令: pip install {' '.join(core_missing)}")
        return False
    else:
        print(f"\n🎉 所有核心依赖已正确安装!")
        
    if optional_missing:
        print(f"\n💡 可安装的可选依赖: {', '.join(optional_missing)}")
        print(f"安装命令: pip install {' '.join(optional_missing)}")
    
    # 检查AIOps系统特定模块
    print(f"\n🔧 检查AIOps系统模块...")
    try:
        import os
        
        # 检查aiops_diagnosis目录
        aiops_path = "/home/<USER>/aiopsagent/autogen-0.4.4/aiops_diagnosis"
        if os.path.exists(aiops_path):
            print(f"✅ aiops_diagnosis 目录存在")
            sys.path.insert(0, os.path.dirname(aiops_path))
            
            # 尝试导入关键模块
            try:
                from aiops_diagnosis.optimized_autogen_system import OptimizedAutoGenAIOpsSystem
                print(f"✅ OptimizedAutoGenAIOpsSystem 可导入")
            except ImportError as e:
                print(f"⚠️ OptimizedAutoGenAIOpsSystem 导入失败: {e}")

            # 检查其他关键模块
            try:
                from aiops_diagnosis.autogen_agents.simple_metric_agent import SimpleMetricAgent
                print(f"✅ SimpleMetricAgent 可导入")
            except ImportError as e:
                print(f"⚠️ SimpleMetricAgent 导入失败: {e}")

        else:
            print(f"❌ aiops_diagnosis 目录不存在: {aiops_path}")
            
    except Exception as e:
        print(f"❌ 模块检查失败: {e}")
    
    print(f"\n🎯 系统状态:")
    if not core_missing:
        print(f"  ✅ 核心功能: 就绪")
        print(f"  ✅ AIOps诊断系统: 可用")
        print(f"  ✅ AutoGen框架: 可用")
        print(f"\n🚀 AIOps智能故障诊断系统已准备就绪!")
        print(f"📝 使用方法:")
        print(f"  - 进入aiops_diagnosis目录")
        print(f"  - 运行: python a2a_batch_diagnosis.py --help")
        return True
    else:
        print(f"  ❌ 核心功能: 需要安装缺失依赖")
        print(f"\n📝 请运行: pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
