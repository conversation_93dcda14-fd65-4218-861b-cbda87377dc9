# AIOps 智能故障诊断系统依赖

# 核心 AutoGen 框架
autogen-core>=0.4.4
autogen-ext>=0.4.4
autogen-agentchat>=0.4.4

# LLM 客户端支持
openai>=1.0.0

# 数据处理和分析
pandas>=2.0.0
pyarrow>=10.0.0
numpy>=1.24.0,<2.0.0
networkx>=3.0.0

# 异步处理
aiofiles>=23.0.0
asyncio-atexit>=1.0.0

# 配置管理
pydantic>=2.10.0,<3.0.0
python-dotenv>=1.0.0
PyYAML>=6.0

# 日志和监控
opentelemetry-api>=1.27.0
opentelemetry-sdk>=1.27.0

# 网络请求
requests>=2.31.0
httpx>=0.25.0

# 数据验证
jsonschema>=4.0.0
jsonref>=1.1.0

# 系统监控和资源管理
psutil>=5.9.0

# 工具库
typing-extensions>=4.0.0
protobuf>=4.25.1
pillow>=11.0.0
pathlib>=1.0.0

# Web框架支持 (如果需要API服务)
websockets>=11.0.0

# AIOps诊断系统特有依赖
# 正则表达式增强
regex>=2023.0.0

# 时区处理
pytz>=2023.3

# 开发和测试 (可选)
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
