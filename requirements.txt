# AIOps 智能故障诊断系统依赖

# 核心 AutoGen 框架
autogen-core>=0.4.4
autogen-ext>=0.4.4
autogen-agentchat>=0.4.4

# LLM 客户端支持
openai>=1.0.0
anthropic>=0.25.0

# 数据处理和分析
pandas>=2.0.0
pyarrow>=10.0.0
numpy>=1.24.0,<2.0.0
scipy>=1.10.0
networkx>=3.0.0

# 异步处理
aiofiles>=23.0.0
asyncio-atexit>=1.0.0

# 配置管理
pydantic>=2.10.0,<3.0.0
python-dotenv>=1.0.0
PyYAML>=6.0

# 日志和监控
structlog>=23.0.0
opentelemetry-api>=1.27.0
opentelemetry-sdk>=1.27.0
loguru>=0.7.0

# 网络请求
requests>=2.31.0
httpx>=0.25.0

# 数据验证
jsonschema>=4.0.0
jsonref>=1.1.0

# 系统监控和资源管理
psutil>=5.9.0

# 工具库
typing-extensions>=4.0.0
protobuf>=4.25.1
pillow>=11.0.0
pathlib>=1.0.0

# 数据库支持 (如果需要)
sqlmodel>=0.0.8
psycopg>=3.1.0
alembic>=1.12.0

# Web框架支持 (如果需要API服务)
fastapi>=0.100.0
uvicorn>=0.23.0
websockets>=11.0.0

# 开发和测试
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
black>=23.0.0
isort>=5.12.0
mypy>=1.5.0


# 自然语言处理 (如果需要文本分析)
# nltk>=3.8.0

# 高级数据处理 (如果需要更快的数据处理)
# polars>=0.20.0

# AIOps诊断系统特有依赖
# 时间序列分析和统计
statsmodels>=0.14.0

# 数据格式支持
openpyxl>=3.1.0

# 并发和多进程
concurrent-futures>=3.1.1

# 缓存支持
diskcache>=5.6.3

# 正则表达式增强
regex>=2023.0.0

# 文件监控和路径处理
watchdog>=3.0.0

# 内存优化
memory-profiler>=0.61.0

# 数据压缩
lz4>=4.3.0

# 时区处理
pytz>=2023.3

# 额外的可选依赖 - 生产环境推荐
# 数据库连接 (如果需要直接连接数据库)
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0

# 消息队列 (如果需要异步消息处理)
# celery>=5.3.0
# redis>=4.6.0

# 监控和追踪 (生产环境推荐)
# prometheus-client>=0.17.0
# jaeger-client>=4.8.0
