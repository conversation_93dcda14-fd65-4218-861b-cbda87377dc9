#!/usr/bin/env python3
"""
增强版Trace Agent - 优化trace分析性能
基于现有的TraceAnalysisAgent，保持异常检测逻辑，优化执行效率
"""

import asyncio
import pandas as pd
import glob
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from autogen_core import MessageContext

# 导入现有系统的基础类
from .base_aiops_agent import (
    BaseAIOpsAgent, AnalysisRequest, AnalysisResponse,
    SuspiciousEntity, DataLocation, TimeRange, EntityInfo, AnomalyFeature
)
from .trace_analysis_agent import AutoGenTraceAnalysisAgent

# Duration单位常量定义 (原始数据单位：微秒μs)
MICROSECONDS_TO_MILLISECONDS = 1000  # 微秒转毫秒的转换因子
SEVERE_LATENCY_THRESHOLD_US = 1_000_000  # 1秒 = 1,000,000微秒
EXTREME_LATENCY_THRESHOLD_US = 5_000_000  # 5秒 = 5,000,000微秒
# 注意：所有duration相关的阈值和计算都应使用微秒单位，与DeepFlow文档保持一致
from ..utils.file_path_locator import FilePathLocator
from ..knowledge.global_component_manager import get_global_component_manager
from ..utils.unit_normalizer import UnitNormalizer


class EnhancedTraceAnalysisAgent(AutoGenTraceAnalysisAgent):
    """增强版Trace分析Agent - 优化性能，保持检测精度"""
    
    def __init__(self, enable_llm: bool = True, data_root_path: str = "/data/phaseone"):
        super().__init__(enable_llm, data_root_path)  # 传递data_root_path给父类
        self.data_root_path = data_root_path
        self.file_locator = FilePathLocator(data_root_path)
        self.component_manager = get_global_component_manager(data_root_path)  # 传递data_root_path
        
        # 性能优化配置
        self.max_llm_calls = 3  # 最多3次LLM调用
        self.fast_detection_threshold = 0.8  # 快速检测置信度阈值
        self.max_anomaly_samples = 2000  # 最大异常样本数

        # 🎯 基于实际数据的优化阈值
        self.optimized_thresholds = {
            'duration': {
                'p95_multiplier': 2.0,  # P95的2倍作为异常阈值
                'p99_multiplier': 1.5,  # P99的1.5倍作为严重异常阈值
                'absolute_high': 100000,  # 100ms绝对高延迟阈值
                'absolute_critical': 500000  # 500ms绝对严重阈值
            },
            'business_patterns': {
                'critical_operations': [
                    'hipstershop.Frontend/PlaceOrder',
                    'hipstershop.CheckoutService/PlaceOrder',
                    'hipstershop.PaymentService/Charge',
                    'hipstershop.CartService/GetCart',
                    'hipstershop.ProductCatalogService/GetProduct'
                ],
                'high_frequency_operations': [
                    'hipstershop.Frontend/GetCart',
                    'hipstershop.Frontend/ViewCart',
                    'hipstershop.ProductCatalogService/ListProducts'
                ]
            },
            'error_detection': {
                'timeout_keywords': ['timeout', 'deadline', 'context deadline exceeded'],
                'connection_keywords': ['connection', 'refused', 'reset', 'broken pipe'],
                'resource_keywords': ['memory', 'cpu', 'disk', 'resource']
            }
        }

    async def _handle_analysis_request(self, message, ctx) -> 'AnalysisResponse':
        """处理调用链分析请求 - 🔧 基于trace字段特点的故障检测"""
        if message.analysis_type != "traces":
            return self._create_error_response(message, "Invalid analysis type for trace analyzer")

        try:
            self.logger.info(f"🚀 EnhancedTraceAgent开始分析案例: {message.case_uuid}")

            # 🔧 修复：EnhancedTraceAgent通过DataLoader加载traces数据
            traces_data = await self._load_trace_data(message.case_uuid, message.start_time, message.end_time)
            if traces_data is None or traces_data.empty:
                self.logger.error("❌ 无traces数据可用于分析")
                return self._create_error_response(message, "No traces data available")

            # 🔧 使用基于trace字段特点的综合故障检测
            detection_results = await self._comprehensive_trace_fault_detection(traces_data)

            # 构建标准化输出
            enhanced_results = self._build_standardized_trace_output(
                detection_results, message.case_uuid, message.start_time, message.end_time, traces_data
            )

            return AnalysisResponse(
                case_uuid=message.case_uuid,
                start_time=message.start_time,
                end_time=message.end_time,
                data=message.data,
                analysis_type="traces",
                results={
                    "detection_results": detection_results,
                    "standardized_output": enhanced_results
                },
                success=True
            )

        except Exception as e:
            self.logger.error(f"❌ EnhancedTraceAgent分析失败: {e}")
            return self._create_error_response(message, str(e))

    def _is_span_anomalous_by_service_type(self, service_name: str, duration_ms: float, span) -> bool:
        """基于HipsterShop服务类型的智能异常检测 - 🔧 基于实际架构"""
        try:
            # 🎯 基于HipsterShop架构的服务分类和阈值
            service_lower = service_name.lower()

            # 1. Frontend服务：用户交互入口，阈值较宽松
            if 'frontend' in service_lower:
                return duration_ms > 2000  # 2秒

            # 2. 数据库/缓存服务：要求快速响应
            elif any(db in service_lower for db in ['redis', 'cart', 'tidb']):
                return duration_ms > 100   # 100毫秒

            # 3. 查询服务：中等阈值
            elif any(svc in service_lower for svc in ['productcatalog', 'recommendation', 'ad']):
                return duration_ms > 500   # 500毫秒

            # 4. 业务处理服务：中等阈值
            elif any(svc in service_lower for svc in ['checkout', 'payment', 'shipping', 'email']):
                return duration_ms > 800   # 800毫秒

            # 5. 汇率服务：快速响应
            elif 'currency' in service_lower:
                return duration_ms > 200   # 200毫秒

            # 6. 通用阈值
            else:
                return duration_ms > 1000  # 1秒

        except Exception as e:
            self.logger.debug(f"智能异常检测失败: {e}")
            # 降级到简单阈值
            return duration_ms > 1000

    def _extract_service_name_from_process_field(self, trace) -> str:
        """从process字段中提取serviceName - 🔧 基于实际数据结构"""
        try:
            process = trace.get('process', {})
            if isinstance(process, dict):
                return process.get('serviceName', 'unknown')
            elif isinstance(process, str):
                # 如果process是字符串，尝试解析JSON
                import json
                try:
                    process_dict = json.loads(process)
                    return process_dict.get('serviceName', 'unknown')
                except:
                    return 'unknown'
            return 'unknown'
        except Exception as e:
            self.logger.debug(f"提取serviceName失败: {e}")
            return 'unknown'

    def _extract_pod_name_from_process(self, trace) -> str:
        """从process字段中提取Pod名称 - 🔧 基于实际数据结构"""
        try:
            process = trace.get('process', {})
            if isinstance(process, dict):
                # 尝试从hostname或tags中提取Pod名
                hostname = process.get('hostname', '')
                if hostname and hostname != 'unknown':
                    return hostname

                # 尝试从tags中提取
                tags = process.get('tags', [])
                if isinstance(tags, list):
                    for tag in tags:
                        if isinstance(tag, dict):
                            key = tag.get('key', '')
                            if key in ['hostname', 'pod.name', 'k8s.pod.name']:
                                return tag.get('value', 'unknown')

                # 如果没有找到，使用serviceName作为fallback
                service_name = process.get('serviceName', 'unknown')
                return f"{service_name}-pod" if service_name != 'unknown' else 'unknown'

            return 'unknown'
        except Exception as e:
            self.logger.debug(f"提取Pod名称失败: {e}")
            return 'unknown'

    def _extract_host_info_from_process_tags(self, trace) -> Dict[str, str]:
        """从process.tags中提取完整的主机信息 - 🔧 基于实际数据结构"""
        try:
            process = trace.get('process', {})
            if not isinstance(process, dict):
                return {'pod_name': 'unknown', 'node_name': 'unknown', 'ip': 'unknown', 'namespace': 'unknown'}

            tags = process.get('tags', [])

            # 🔧 正确处理numpy.ndarray类型
            if hasattr(tags, 'tolist'):
                tags = tags.tolist()
            elif not isinstance(tags, list):
                tags = list(tags)

            # 提取所有主机相关信息
            host_info = {
                'pod_name': 'unknown',
                'node_name': 'unknown',
                'ip': 'unknown',
                'namespace': 'unknown'
            }

            for tag in tags:
                if isinstance(tag, dict):
                    key = tag.get('key', '')
                    value = tag.get('value', '')

                    if key == 'name':  # Pod名称
                        host_info['pod_name'] = value
                    elif key == 'node_name':  # 节点名称
                        host_info['node_name'] = value
                    elif key == 'ip':  # IP地址
                        host_info['ip'] = value
                    elif key == 'namespace':  # 命名空间
                        host_info['namespace'] = value

            # 如果没有找到Pod名称，尝试构造一个
            if host_info['pod_name'] == 'unknown':
                service_name = process.get('serviceName', 'unknown')
                host_info['pod_name'] = f"{service_name}-unknown" if service_name != 'unknown' else 'unknown'

            return host_info

        except Exception as e:
            self.logger.debug(f"从process.tags提取主机信息失败: {e}")
            return {'pod_name': 'unknown', 'node_name': 'unknown', 'ip': 'unknown', 'namespace': 'unknown'}

    async def _load_traces_data(self, case_uuid: str, start_time: str, end_time: str) -> pd.DataFrame:
        """加载调用链数据 - 🔧 基于DataLoaderAgent的实现"""
        try:
            self.logger.info(f"🔍 开始加载traces数据: {case_uuid} ({start_time} - {end_time})")

            # 获取需要加载的文件列表
            files = self._get_time_range_files(start_time, end_time)
            all_traces = []

            self.logger.info(f"Found {len(files['traces'])} trace files to load")

            for trace_file in files['traces']:
                try:
                    df = pd.read_parquet(trace_file)
                    if not df.empty:
                        all_traces.append(df)
                        self.logger.debug(f"Loaded trace data from {trace_file}: {len(df)} records")
                except Exception as e:
                    self.logger.warning(f"Error reading {trace_file}: {e}")

            if not all_traces:
                self.logger.warning("No traces data loaded")
                return pd.DataFrame()

            # 合并所有调用链数据
            combined_df = pd.concat(all_traces, ignore_index=True)

            # 使用UTC时间精确过滤数据
            filtered_df = self._filter_trace_data_by_utc_time(combined_df, start_time, end_time)

            self.logger.info(f"✅ 加载完成: {len(filtered_df)} trace records from {len(all_traces)} files after time filtering")

            # 🔧 新增：分析实际数据分布，动态设置阈值
            if not filtered_df.empty:
                self._analyze_actual_data_distribution(filtered_df)

            return filtered_df

        except Exception as e:
            self.logger.error(f"❌ 加载traces数据失败: {e}")
            return pd.DataFrame()

    def _get_time_range_files(self, start_utc: str, end_utc: str) -> dict:
        """根据UTC时间范围精确确定需要加载的数据文件 - 🔧 基于实际数据目录结构"""
        from datetime import timedelta
        from pathlib import Path

        start_dt = pd.to_datetime(start_utc)
        end_dt = pd.to_datetime(end_utc)

        files = {'metrics': [], 'traces': [], 'logs': []}

        # 转换为北京时间
        start_beijing_dt = start_dt + timedelta(hours=8)
        end_beijing_dt = end_dt + timedelta(hours=8)

        self.logger.info(f"UTC时间范围: {start_utc} 到 {end_utc}")
        self.logger.info(f"北京时间范围: {start_beijing_dt} 到 {end_beijing_dt}")

        # 生成需要的日期列表
        current_date = start_beijing_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = end_beijing_dt.replace(hour=0, minute=0, second=0, microsecond=0)

        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')

            # 确定当前日期的小时范围
            if current_date == start_beijing_dt.replace(hour=0, minute=0, second=0, microsecond=0):
                start_hour = start_beijing_dt.hour
            else:
                start_hour = 0

            if current_date == end_beijing_dt.replace(hour=0, minute=0, second=0, microsecond=0):
                end_hour = end_beijing_dt.hour
            else:
                end_hour = 23

            # 遍历小时
            for hour in range(start_hour, end_hour + 1):
                hour_str = f"{hour:02d}"

                # 构建trace文件路径 - 基于实际数据目录结构
                trace_pattern = f"/data/phaseone_data/{date_str}/trace-parquet/trace_jaeger-span_{date_str}_{hour_str}-00-00.parquet"
                trace_files = glob.glob(trace_pattern)
                files['traces'].extend(trace_files)

            current_date += timedelta(days=1)

        self.logger.info(f"找到文件: traces={len(files['traces'])}")
        return files

    def _filter_trace_data_by_utc_time(self, df: pd.DataFrame, start_time: str, end_time: str) -> pd.DataFrame:
        """使用UTC时间精确过滤trace数据 - 🔧 基于DataLoaderAgent的实现"""
        try:
            if df.empty:
                return df

            # 解析时间字符串为datetime对象
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))

            # 转换为毫秒时间戳
            start_ms = int(start_dt.timestamp() * 1000)
            end_ms = int(end_dt.timestamp() * 1000)

            # 检查时间字段
            if 'startTimeMillis' in df.columns:
                time_column = 'startTimeMillis'
            elif 'startTime' in df.columns:
                time_column = 'startTime'
                # 如果startTime是纳秒，转换为毫秒
                if df[time_column].max() > 1e12:  # 纳秒级别
                    df = df.copy()
                    df[time_column] = df[time_column] / 1e6
            else:
                self.logger.warning("No time column found in trace data")
                return df

            # 过滤数据
            mask = (df[time_column] >= start_ms) & (df[time_column] <= end_ms)
            filtered_df = df[mask].copy()

            self.logger.debug(f"Time filtering: {len(df)} -> {len(filtered_df)} records")
            return filtered_df

        except Exception as e:
            self.logger.error(f"Time filtering failed: {e}")
            return df

    def _analyze_actual_data_distribution(self, traces_data: pd.DataFrame):
        """分析实际数据分布，动态设置阈值 - 🔧 基于实际数据的智能阈值"""
        try:
            self.logger.info("🔍 开始分析实际数据分布...")

            if 'duration' not in traces_data.columns:
                self.logger.warning("⚠️ 数据中没有duration字段")
                return

            # 转换为毫秒
            duration_ms = traces_data['duration'] / 1000

            # 基础统计信息
            stats = {
                'count': len(traces_data),
                'mean': duration_ms.mean(),
                'median': duration_ms.median(),
                'p90': duration_ms.quantile(0.9),
                'p95': duration_ms.quantile(0.95),
                'p99': duration_ms.quantile(0.99),
                'max': duration_ms.max()
            }

            self.logger.info(f"📊 Duration统计 (毫秒): 平均={stats['mean']:.2f}, P95={stats['p95']:.2f}, P99={stats['p99']:.2f}, 最大={stats['max']:.2f}")

            # 🔧 动态设置阈值：基于P95和P99
            dynamic_thresholds = {
                'p95_threshold': stats['p95'],
                'p99_threshold': stats['p99'],
                'outlier_threshold': stats['p99'] * 1.5,  # P99的1.5倍作为离群值
                'extreme_threshold': max(stats['p99'] * 2, 1000)  # P99的2倍或1秒，取较大值
            }

            # 更新optimized_thresholds
            self.optimized_thresholds['duration']['dynamic_p95'] = dynamic_thresholds['p95_threshold']
            self.optimized_thresholds['duration']['dynamic_p99'] = dynamic_thresholds['p99_threshold']
            self.optimized_thresholds['duration']['dynamic_outlier'] = dynamic_thresholds['outlier_threshold']
            self.optimized_thresholds['duration']['dynamic_extreme'] = dynamic_thresholds['extreme_threshold']

            self.logger.info(f"🎯 动态阈值设置: P95={dynamic_thresholds['p95_threshold']:.2f}ms, P99={dynamic_thresholds['p99_threshold']:.2f}ms, 离群值={dynamic_thresholds['outlier_threshold']:.2f}ms")

        except Exception as e:
            self.logger.error(f"❌ 数据分布分析失败: {e}")

    def _analyze_trace_chain_anomalies(self, traces_data: pd.DataFrame, trace_id: str, p95_threshold_ms: float, p99_threshold_ms: float) -> Dict[str, Any]:
        """分析单个调用链中的所有异常 - 🎯 核心调用链分析方法"""
        try:
            # 获取同一个traceID的所有span
            same_trace_spans = traces_data[traces_data['traceID'] == trace_id].copy()

            if same_trace_spans.empty:
                return {"trace_id": trace_id, "anomaly_spans": [], "error": "No spans found"}

            # 按startTime排序，构建调用顺序
            if 'startTime' in same_trace_spans.columns:
                same_trace_spans = same_trace_spans.sort_values('startTime')

            anomaly_spans = []
            normal_spans = []

            # 分析每个span
            for idx, span in same_trace_spans.iterrows():
                duration_ms = span.get('duration', 0) / 1000
                service_name = self._extract_service_name_from_process_field(span)
                operation_name = span.get('operationName', 'unknown')
                span_id = span.get('spanID', 'unknown')

                # 🔧 新增：提取完整的主机信息
                host_info = self._extract_host_info_from_process_tags(span)

                span_info = {
                    'idx': idx,
                    'service_name': service_name,
                    'operation_name': operation_name,
                    'duration_ms': duration_ms,
                    'span_id': span_id,
                    'pod_name': host_info['pod_name'],
                    'node_name': host_info['node_name'],
                    'ip': host_info['ip'],
                    'namespace': host_info['namespace'],
                    'is_severe': duration_ms > p99_threshold_ms,
                    'is_moderate': p95_threshold_ms < duration_ms <= p99_threshold_ms,
                    'is_normal': duration_ms <= p95_threshold_ms
                }

                # 分类span
                if duration_ms > p95_threshold_ms:  # P95以上认为是异常
                    anomaly_spans.append(span_info)
                else:
                    normal_spans.append(span_info)

            # 构建调用链分析结果
            chain_analysis = {
                "trace_id": trace_id,
                "total_spans": len(same_trace_spans),
                "anomaly_spans": anomaly_spans,
                "normal_spans": normal_spans[:3],  # 只保留前3个正常span作为对比
                "chain_summary": {
                    "severe_count": len([s for s in anomaly_spans if s['is_severe']]),
                    "moderate_count": len([s for s in anomaly_spans if s['is_moderate']]),
                    "total_chain_duration": same_trace_spans['duration'].sum() / 1000,
                    "max_duration": max([s['duration_ms'] for s in anomaly_spans]) if anomaly_spans else 0
                }
            }

            # 构建故障传播路径
            if len(anomaly_spans) > 1:
                chain_analysis["fault_propagation_path"] = self._build_fault_propagation_path(anomaly_spans)

            return chain_analysis

        except Exception as e:
            self.logger.error(f"❌ 调用链分析失败: {e}")
            return {"trace_id": trace_id, "anomaly_spans": [], "error": str(e)}

    def _build_fault_propagation_path(self, anomaly_spans: List[Dict]) -> List[str]:
        """构建故障传播路径"""
        try:
            # 按严重程度排序
            sorted_spans = sorted(anomaly_spans, key=lambda x: x['duration_ms'], reverse=True)

            path = []
            for span in sorted_spans:
                severity = "🔥" if span['is_severe'] else "⚠️"
                path.append(f"{severity} {span['service_name']}({span['duration_ms']:.1f}ms)")

            return path
        except Exception as e:
            self.logger.debug(f"构建故障传播路径失败: {e}")
            return []

    def _build_trace_chain_for_anomaly(self, traces_data: pd.DataFrame, trace_id: str, anomaly_idx: int) -> Dict[str, Any]:
        """为异常trace构建完整的调用链信息 - 🔧 只包含异常节点"""
        try:
            # 获取同一个traceID的所有span
            same_trace_spans = traces_data[traces_data['traceID'] == trace_id]

            if same_trace_spans.empty:
                return {"error": "No spans found for trace"}

            # 🔧 只分析异常的span，不包含整个调用链
            anomaly_span = traces_data.iloc[anomaly_idx]

            # 构建异常节点信息
            chain_info = {
                "trace_id": trace_id,
                "total_spans": len(same_trace_spans),
                "anomaly_span": {
                    "service": self._extract_service_name_from_process_field(anomaly_span),
                    "operation": anomaly_span.get('operationName', 'unknown'),
                    "duration_ms": anomaly_span.get('duration', 0) / 1000,
                    "span_id": anomaly_span.get('spanID', 'unknown')
                },
                "related_spans": []
            }

            # 🔧 只包含同一调用链中的其他异常span（如果有的话）
            outlier_threshold_ms = self.optimized_thresholds['duration'].get('dynamic_outlier', 1000)

            for _, span in same_trace_spans.iterrows():
                span_duration_ms = span.get('duration', 0) / 1000
                if span_duration_ms > outlier_threshold_ms and span.get('spanID') != anomaly_span.get('spanID'):
                    chain_info["related_spans"].append({
                        "service": self._extract_service_name_from_process_field(span),
                        "operation": span.get('operationName', 'unknown'),
                        "duration_ms": span_duration_ms,
                        "span_id": span.get('spanID', 'unknown')
                    })

            # 限制related_spans数量
            chain_info["related_spans"] = chain_info["related_spans"][:5]

            return chain_info

        except Exception as e:
            self.logger.debug(f"构建调用链信息失败: {e}")
            return {"error": str(e)}

    async def _comprehensive_trace_fault_detection(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """基于trace字段特点的综合故障检测 - 🎯 核心检测方法"""
        try:
            self.logger.info("🚀 启动基于trace字段的综合故障检测")

            detection_results = {
                "error_evidence": [],
                "duration_anomalies": [],
                "trace_chain_faults": [],
                "service_fault_summary": {},
                "confidence": 0.0
            }

            if traces_data.empty:
                self.logger.warning("⚠️ 无trace数据可用于故障检测")
                return detection_results

            # 1. 基于tags/logs的错误检测
            self.logger.info("🔍 第1步：基于tags/logs的错误检测")
            error_evidence = await self._extract_error_evidence_from_tags_logs(traces_data)
            detection_results["error_evidence"] = error_evidence

            # 2. 基于duration的智能阈值检测
            self.logger.info("🔍 第2步：基于duration的智能阈值检测")
            duration_stats = self._calculate_duration_statistics(traces_data)
            duration_anomalies_result = self._detect_dynamic_latency_anomalies(traces_data, duration_stats)

            # 🔧 新策略：基于调用链分析构建duration_anomalies
            duration_anomalies = []

            # 从duration_anomalies_result中获取调用链分析结果
            trace_chain_analysis = duration_anomalies_result.get('trace_chain_analysis', [])

            for chain_analysis in trace_chain_analysis:
                trace_id = chain_analysis['trace_id']
                anomaly_spans = chain_analysis['anomaly_spans']
                chain_summary = chain_analysis['chain_summary']

                # 🔧 修改：为调用链中的每个异常span创建单独的证据记录
                if anomaly_spans:
                    self.logger.info(f"🔗 构建调用链异常: {trace_id[:8]}... - 包含{len(anomaly_spans)}个异常span")

                    for span_info in anomaly_spans:
                        duration_anomalies.append({
                            "type": "trace_chain_span_fault",
                            "service": span_info['service_name'],
                            "trace_id": trace_id,
                            "span_id": span_info['span_id'],
                            "operation_name": span_info['operation_name'],
                            "duration_ms": span_info['duration_ms'],
                            "severity": "critical" if span_info['is_severe'] else "high",
                            "description": f"调用链中的异常span: {span_info['service_name']} - {span_info['operation_name']}",
                            "source": "trace_chain_analysis",
                            "pod_name": span_info.get('pod_name', 'unknown'),
                            "node_name": span_info.get('node_name', 'unknown'),
                            "ip": span_info.get('ip', 'unknown'),
                            "namespace": span_info.get('namespace', 'unknown'),
                            "chain_context": {
                                "total_chain_spans": len(anomaly_spans),
                                "chain_severe_count": chain_summary['severe_count'],
                                "chain_moderate_count": chain_summary['moderate_count'],
                                "total_chain_duration": chain_summary['total_chain_duration'],
                                "fault_propagation_path": chain_analysis.get('fault_propagation_path', []),
                                "span_position_in_chain": anomaly_spans.index(span_info) + 1
                            }
                        })

                        pod_name = span_info.get('pod_name', 'unknown')
                        node_name = span_info.get('node_name', 'unknown')
                        self.logger.info(f"  📍 Span {anomaly_spans.index(span_info) + 1}/{len(anomaly_spans)}: {span_info['service_name']} ({pod_name}@{node_name}) - {span_info['duration_ms']:.1f}ms")

            detection_results["duration_anomalies"] = duration_anomalies

            # 3. 基于调用链关联的故障传播检测
            self.logger.info("🔍 第3步：基于调用链关联的故障传播检测")
            trace_chain_faults = self._trace_chain_fault_propagation_detection(traces_data)
            detection_results["trace_chain_faults"] = trace_chain_faults

            # 4. 聚合服务级别的故障总结
            self.logger.info("🔍 第4步：聚合服务级别的故障总结")
            service_fault_summary = self._aggregate_service_fault_summary(
                error_evidence, duration_anomalies, trace_chain_faults
            )
            detection_results["service_fault_summary"] = service_fault_summary

            # 5. 计算整体置信度
            total_faults = len(error_evidence) + len(duration_anomalies) + len(trace_chain_faults)
            detection_results["confidence"] = min(0.95, 0.3 + total_faults * 0.1)

            self.logger.info(f"✅ 综合故障检测完成:")
            self.logger.info(f"   - 错误证据: {len(error_evidence)}个")
            self.logger.info(f"   - Duration异常: {len(duration_anomalies)}个")
            self.logger.info(f"   - 调用链故障: {len(trace_chain_faults)}个")
            self.logger.info(f"   - 整体置信度: {detection_results['confidence']:.3f}")

            return detection_results

        except Exception as e:
            self.logger.error(f"❌ 综合故障检测失败: {e}")
            return {
                "error_evidence": [],
                "duration_anomalies": [],
                "trace_chain_faults": [],
                "service_fault_summary": {},
                "confidence": 0.0
            }

    async def _extract_error_evidence_from_tags_logs(self, traces_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """从tags和logs字段中提取错误信息作为异常证据"""
        error_evidence = []

        try:
            self.logger.info("🔍 开始从tags和logs中提取错误证据...")

            for _, trace in traces_data.iterrows():
                service_name = self._extract_service_name_from_process_field(trace)
                pod_instance = self._extract_pod_name_from_process(trace)

                # 分析tags字段中的错误信息
                tags_errors = self._analyze_tags_for_errors(trace, service_name, pod_instance)
                error_evidence.extend(tags_errors)

                # 分析logs字段中的错误信息
                logs_errors = self._analyze_logs_for_errors(trace, service_name, pod_instance)
                error_evidence.extend(logs_errors)

            # 去重和聚合相似的错误
            error_evidence = self._deduplicate_error_evidence(error_evidence)

            if error_evidence:
                self.logger.info(f"📊 从tags/logs中提取到 {len(error_evidence)} 个错误证据")
                for i, evidence in enumerate(error_evidence[:3], 1):
                    service = evidence.get('service', 'unknown')
                    error_type = evidence.get('error_type', 'unknown')
                    severity = evidence.get('severity', 'unknown')
                    self.logger.info(f"   {i}. {service} - {error_type} ({severity})")
            else:
                self.logger.info("✅ tags/logs中未发现明显错误信息")

        except Exception as e:
            self.logger.error(f"❌ 错误证据提取失败: {e}")

        return error_evidence

    def _analyze_tags_for_errors(self, trace, service_name: str, pod_instance: str) -> List[Dict[str, Any]]:
        """分析trace的tags字段中的错误信息 - 🔧 修复numpy.ndarray处理"""
        errors = []

        try:
            tags_list = trace.get('tags', [])

            # 🔧 修复：正确处理numpy.ndarray类型
            if hasattr(tags_list, '__iter__') and not isinstance(tags_list, str):
                # 转换为Python list以便处理
                if hasattr(tags_list, 'tolist'):
                    tags_list = tags_list.tolist()
                elif not isinstance(tags_list, list):
                    tags_list = list(tags_list)
            else:
                return errors

            # 检查gRPC状态码
            grpc_status = None
            error_flag = False
            span_kind = None

            for tag in tags_list:
                if not isinstance(tag, dict):
                    continue

                key = tag.get('key', '')
                value = tag.get('value', '')

                # 检查gRPC状态码
                if key in ['rpc.grpc.status_code', 'grpc.status_code']:
                    try:
                        grpc_status = int(value)
                    except (ValueError, TypeError):
                        grpc_status = None

                # 检查OpenTelemetry状态码
                elif key == 'status.code':
                    try:
                        otel_status = int(value)
                        if otel_status != 0:  # 非0表示错误
                            errors.append({
                                "type": "otel_error",
                                "service": service_name,
                                "pod_instance": pod_instance,
                                "error_type": "otel_status_error",
                                "severity": "high",
                                "description": f"OpenTelemetry错误状态码: {otel_status}",
                                "evidence": f"status.code {otel_status} 在 {service_name}",
                                "source": "tags_analysis"
                            })
                    except (ValueError, TypeError):
                        pass

                # 检查错误标志
                elif key == 'error' and str(value).lower() in ['true', '1', 'yes']:
                    error_flag = True

                # 检查span类型
                elif key == 'span.kind':
                    span_kind = value

                # 检查HTTP状态码
                elif key in ['http.status_code', 'http.response.status_code']:
                    try:
                        http_status = int(value)
                        if http_status >= 400:
                            errors.append({
                                "type": "http_error",
                                "service": service_name,
                                "pod_instance": pod_instance,
                                "error_type": "http_error",
                                "severity": "high" if http_status >= 500 else "medium",
                                "description": f"HTTP错误状态码: {http_status}",
                                "evidence": f"HTTP状态码 {http_status} 在 {service_name}",
                                "source": "tags_analysis"
                            })
                    except (ValueError, TypeError):
                        pass

            # 处理gRPC错误
            if grpc_status is not None and grpc_status != 0:
                severity = "critical" if grpc_status in [2, 3, 4, 14] else "high"
                errors.append({
                    "type": "grpc_error",
                    "service": service_name,
                    "pod_instance": pod_instance,
                    "error_type": "grpc_error",
                    "severity": severity,
                    "description": f"gRPC错误状态码: {grpc_status}",
                    "evidence": f"gRPC状态码 {grpc_status} 在 {service_name}",
                    "source": "tags_analysis"
                })

            # 处理通用错误标志
            if error_flag:
                errors.append({
                    "type": "span_error",
                    "service": service_name,
                    "pod_instance": pod_instance,
                    "error_type": "span_error",
                    "severity": "medium",
                    "description": f"Span错误标志",
                    "evidence": f"错误标志在 {service_name}",
                    "source": "tags_analysis"
                })

        except Exception as e:
            self.logger.debug(f"分析tags错误失败: {e}")

        return errors

    def _analyze_logs_for_errors(self, trace, service_name: str, pod_instance: str) -> List[Dict[str, Any]]:
        """分析trace的logs字段中的错误信息 - 🔧 修复numpy.ndarray处理"""
        errors = []

        try:
            logs_list = trace.get('logs', [])

            # 🔧 修复：正确处理numpy.ndarray类型
            if hasattr(logs_list, '__iter__') and not isinstance(logs_list, str):
                # 转换为Python list以便处理
                if hasattr(logs_list, 'tolist'):
                    logs_list = logs_list.tolist()
                elif not isinstance(logs_list, list):
                    logs_list = list(logs_list)
            else:
                return errors

            for log_entry in logs_list:
                if not isinstance(log_entry, dict):
                    continue

                fields = log_entry.get('fields', [])

                # 🔧 修复：正确处理fields的numpy.ndarray类型
                if hasattr(fields, '__iter__') and not isinstance(fields, str):
                    if hasattr(fields, 'tolist'):
                        fields = fields.tolist()
                    elif not isinstance(fields, list):
                        fields = list(fields)
                else:
                    continue

                for field in fields:
                    if not isinstance(field, dict):
                        continue

                    key = field.get('key', '')
                    value = str(field.get('value', ''))

                    # 检查错误关键词
                    if any(error_word in key.lower() for error_word in ['error', 'exception', 'fail']):
                        if any(error_indicator in value.lower() for error_indicator in ['error', 'exception', 'fail', 'timeout']):
                            errors.append({
                                "type": "log_error",
                                "service": service_name,
                                "pod_instance": pod_instance,
                                "error_type": "log_error",
                                "severity": "medium",
                                "description": f"日志错误: {key}={value}",
                                "evidence": f"日志错误在 {service_name}: {key}={value}",
                                "source": "logs_analysis"
                            })

        except Exception as e:
            self.logger.debug(f"分析logs错误失败: {e}")

        return errors

    def _deduplicate_error_evidence(self, error_evidence: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重和聚合相似的错误证据"""
        if not error_evidence:
            return []

        # 简单的去重逻辑：基于service和error_type
        seen = set()
        deduplicated = []

        for evidence in error_evidence:
            key = (evidence.get('service', 'unknown'), evidence.get('error_type', 'unknown'))
            if key not in seen:
                seen.add(key)
                deduplicated.append(evidence)

        return deduplicated

    def _trace_chain_fault_propagation_detection(self, traces_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """基于调用链关联的故障传播检测 - 🔧 简化版本"""
        trace_chain_faults = []

        try:
            if 'traceID' not in traces_data.columns:
                return trace_chain_faults

            # 按traceID分组分析
            trace_groups = traces_data.groupby('traceID')

            for trace_id, group in trace_groups:
                if len(group) < 2:  # 单span的trace跳过
                    continue

                # 检查这个调用链是否有多个异常span
                anomaly_count = 0
                services_in_chain = []

                for _, span in group.iterrows():
                    service_name = self._extract_service_name_from_process_field(span)
                    duration_ms = span.get('duration', 0) / 1000

                    services_in_chain.append(service_name)

                    # 检查是否异常
                    if self._is_span_anomalous_by_service_type(service_name, duration_ms, span):
                        anomaly_count += 1

                # 如果有多个异常span，认为是调用链故障
                if anomaly_count > 1:
                    trace_chain_faults.append({
                        "type": "trace_chain_fault",
                        "trace_id": trace_id,
                        "services_in_chain": list(set(services_in_chain)),
                        "anomaly_span_count": anomaly_count,
                        "total_span_count": len(group),
                        "severity": "high" if anomaly_count > 2 else "medium",
                        "description": f"调用链故障: {anomaly_count}个异常span",
                        "source": "trace_chain_fault_detection"
                    })

            return trace_chain_faults

        except Exception as e:
            self.logger.error(f"❌ 调用链故障传播检测失败: {e}")
            return []

    def _aggregate_service_fault_summary(self, error_evidence: List, duration_anomalies: List, trace_chain_faults: List) -> Dict[str, Any]:
        """聚合服务级别的故障总结 - 🔧 简化版本"""
        service_fault_summary = {}

        try:
            # 聚合所有故障信息
            all_faults = error_evidence + duration_anomalies + trace_chain_faults

            for fault in all_faults:
                service = fault.get('service', 'unknown')
                if service == 'unknown':
                    # 对于调用链故障，取第一个服务
                    services = fault.get('services_in_chain', [])
                    if services:
                        service = services[0]

                if service not in service_fault_summary:
                    service_fault_summary[service] = {
                        'fault_count': 0,
                        'max_severity': 'low',
                        'fault_types': set(),
                        'descriptions': []
                    }

                service_fault_summary[service]['fault_count'] += 1
                service_fault_summary[service]['fault_types'].add(fault.get('type', 'unknown'))
                service_fault_summary[service]['descriptions'].append(fault.get('description', ''))

                # 更新最高严重程度
                severity = fault.get('severity', 'low')
                if self._compare_severity(severity, service_fault_summary[service]['max_severity']) > 0:
                    service_fault_summary[service]['max_severity'] = severity

            # 转换set为list以便JSON序列化
            for service in service_fault_summary:
                service_fault_summary[service]['fault_types'] = list(service_fault_summary[service]['fault_types'])

            return service_fault_summary

        except Exception as e:
            self.logger.error(f"❌ 服务故障聚合失败: {e}")
            return {}

    def _compare_severity(self, severity1: str, severity2: str) -> int:
        """比较严重程度，返回1表示severity1更严重，-1表示severity2更严重，0表示相等"""
        severity_order = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        level1 = severity_order.get(severity1, 1)
        level2 = severity_order.get(severity2, 1)

        if level1 > level2:
            return 1
        elif level1 < level2:
            return -1
        else:
            return 0

    def _build_standardized_trace_output(self, detection_results: Dict, case_uuid: str, start_time: str, end_time: str, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """构建标准化的trace输出格式"""
        try:
            suspicious_entities = []

            # 从检测结果中提取可疑实体
            for evidence in detection_results.get('error_evidence', []):
                # 🔧 修复：安全提取服务名，避免unknown值
                service_name = self._safe_extract_service_from_evidence(evidence)

                suspicious_entities.append({
                    'entity': {
                        'service': service_name,  # 🔧 修复：使用安全提取的服务名
                        'namespace': 'hipstershop',
                        'pod': evidence.get('pod_instance', 'unknown')
                    },
                    'anomaly_feature': {
                        'pattern': evidence.get('error_type', 'trace_error'),
                        'severity': evidence.get('severity', 'medium'),
                        'confidence': 0.8,
                        'source': evidence.get('source', 'trace_analysis')
                    },
                    'confidence': 0.8,
                    'description': evidence.get('description', ''),
                    'time_range': {
                        'start': start_time,
                        'end': end_time
                    },
                    'data_location': {
                        'detection_method': 'trace_error_analysis',
                        'evidence_type': evidence.get('type', 'unknown')
                    }
                })

            for anomaly in detection_results.get('duration_anomalies', []):
                # 🔧 修复：为每个异常构建包含raw_data_evidence的完整证据
                raw_data_sample = {
                    'service': anomaly.get('service', 'unknown'),
                    'trace_id': anomaly.get('trace_id', 'unknown'),
                    'span_id': anomaly.get('span_id', 'unknown'),
                    'operation_name': anomaly.get('operation_name', 'unknown'),
                    'duration_ms': anomaly.get('duration_ms', 0),
                    'severity': anomaly.get('severity', 'medium'),
                    'source': anomaly.get('source', 'trace_analysis'),
                    'pod_name': anomaly.get('pod_name', 'unknown'),
                    'node_name': anomaly.get('node_name', 'unknown'),
                    'ip': anomaly.get('ip', 'unknown'),
                    'namespace': anomaly.get('namespace', 'unknown'),
                    'chain_context': anomaly.get('chain_context', {}),
                    # 🔧 新增：为ReasoningAgent提供完整的process信息
                    'process': {
                        'serviceName': anomaly.get('service', 'unknown'),
                        'hostname': anomaly.get('pod_name', 'unknown'),  # 使用Pod名称作为hostname
                        'tags': [
                            {'key': 'name', 'value': anomaly.get('pod_name', 'unknown')},
                            {'key': 'node_name', 'value': anomaly.get('node_name', 'unknown')},
                            {'key': 'ip', 'value': anomaly.get('ip', 'unknown')},
                            {'key': 'namespace', 'value': anomaly.get('namespace', 'unknown')},
                            {'key': 'service', 'value': anomaly.get('service', 'unknown')}
                        ]
                    }
                }

                suspicious_entities.append({
                    'entity_type': 'service',
                    'entity_name': anomaly.get('service', 'unknown'),
                    'anomaly_type': 'duration_anomaly',
                    'severity': anomaly.get('severity', 'medium'),
                    'description': anomaly.get('description', ''),
                    'confidence': 0.7,
                    'raw_data_evidence': [raw_data_sample]  # 🔧 关键修复：包含原始数据
                })

            return {
                'suspicious_entities': suspicious_entities,
                'confidence': detection_results.get('confidence', 0.5),
                'analysis_summary': f"检测到 {len(suspicious_entities)} 个可疑实体"
            }

        except Exception as e:
            self.logger.error(f"❌ 构建标准化输出失败: {e}")
            return {
                'suspicious_entities': [],
                'confidence': 0.0,
                'analysis_summary': "分析失败"
            }

    def _calculate_duration_statistics(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """计算duration统计信息 - 复用现有逻辑"""
        duration_info = {}
        if 'duration' in traces_data.columns:
            durations = traces_data['duration'].dropna()
            if len(durations) > 0:
                durations_ms = durations / MICROSECONDS_TO_MILLISECONDS  # 微秒转换为毫秒
                duration_info = {
                    'count': len(durations),
                    'mean_ms': float(durations_ms.mean()),
                    'median_ms': float(durations_ms.median()),
                    'std_ms': float(durations_ms.std()),
                    'min_ms': float(durations_ms.min()),
                    'max_ms': float(durations_ms.max()),
                    'p95_ms': float(durations_ms.quantile(0.95)),
                    'p99_ms': float(durations_ms.quantile(0.99))
                }
        return duration_info

    def _calculate_enhanced_duration_statistics(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """🎯 计算增强的duration统计信息 - 包含业务语义分析"""
        try:
            duration_info = self._calculate_duration_statistics(traces_data)

            if 'duration' in traces_data.columns and not traces_data.empty:
                durations = traces_data['duration'].dropna()
                durations_ms = durations / 1000  # 微秒转毫秒

                # 🎯 新增：基于实际数据的动态阈值
                duration_info.update({
                    'p90_ms': float(durations_ms.quantile(0.90)),
                    'p99_9_ms': float(durations_ms.quantile(0.999)),
                    'iqr_ms': float(durations_ms.quantile(0.75) - durations_ms.quantile(0.25)),
                    'outlier_threshold_ms': float(durations_ms.quantile(0.75) + 1.5 * (durations_ms.quantile(0.75) - durations_ms.quantile(0.25))),
                    'severe_outlier_threshold_ms': float(durations_ms.quantile(0.75) + 3 * (durations_ms.quantile(0.75) - durations_ms.quantile(0.25)))
                })

                # 🎯 新增：按操作类型分组的统计
                if 'operationName' in traces_data.columns:
                    operation_stats = {}
                    for operation in self.optimized_thresholds['business_patterns']['critical_operations']:
                        op_data = traces_data[traces_data['operationName'] == operation]
                        if not op_data.empty:
                            op_durations_ms = op_data['duration'] / 1000
                            operation_stats[operation] = {
                                'count': len(op_data),
                                'mean_ms': float(op_durations_ms.mean()),
                                'p95_ms': float(op_durations_ms.quantile(0.95)),
                                'p99_ms': float(op_durations_ms.quantile(0.99))
                            }
                    duration_info['operation_stats'] = operation_stats

            return duration_info

        except Exception as e:
            self.logger.error(f"❌ 增强duration统计计算失败: {e}")
            return self._calculate_duration_statistics(traces_data)

    def _detect_dynamic_latency_anomalies(self, traces_data: pd.DataFrame, duration_stats: Dict[str, Any]) -> Dict[str, Any]:
        """🎯 动态延迟异常检测 - 基于数据分布和业务语义"""
        try:
            anomalies = {
                'count': 0,
                'severe_count': 0,
                'outlier_spans': [],
                'severe_outlier_spans': [],
                'business_critical_slow': []
            }

            if 'duration' not in traces_data.columns or traces_data.empty:
                return anomalies

            # 🔧 新策略：基于调用链的故障传播检测
            self.logger.info(f"🔍 开始基于调用链的故障传播检测: {len(traces_data)}条trace")

            # 使用动态计算的阈值
            p95_threshold_ms = self.optimized_thresholds['duration'].get('dynamic_p95', 100)
            p99_threshold_ms = self.optimized_thresholds['duration'].get('dynamic_p99', 500)
            outlier_threshold_ms = self.optimized_thresholds['duration'].get('dynamic_outlier', 1000)

            self.logger.info(f"🎯 使用分层阈值: P95={p95_threshold_ms:.2f}ms, P99={p99_threshold_ms:.2f}ms, 离群值={outlier_threshold_ms:.2f}ms")

            # 第一步：找到严重异常的span（P99以上）
            severe_anomalies = []
            for idx, trace in traces_data.iterrows():
                try:
                    duration_us = trace.get('duration', 0)
                    duration_ms = duration_us / 1000

                    if duration_ms > p99_threshold_ms:  # 严重异常
                        severe_anomalies.append({
                            'idx': idx,
                            'duration_ms': duration_ms,
                            'service_name': self._extract_service_name_from_process_field(trace),
                            'trace_id': trace.get('traceID', 'unknown'),
                            'span_id': trace.get('spanID', 'unknown'),
                            'operation_name': trace.get('operationName', 'unknown'),
                            'trace': trace
                        })
                except Exception as e:
                    self.logger.debug(f"处理trace {idx}时出错: {e}")
                    continue

            # 只取前5个最严重的异常
            severe_anomalies.sort(key=lambda x: x['duration_ms'], reverse=True)
            top_severe_anomalies = severe_anomalies[:5]

            self.logger.info(f"🚨 发现{len(severe_anomalies)}个严重异常，选择最严重的{len(top_severe_anomalies)}个进行调用链分析")

            # 第二步：对每个严重异常，分析其完整调用链
            trace_chain_anomalies = []
            processed_trace_ids = set()

            for severe_anomaly in top_severe_anomalies:
                trace_id = severe_anomaly['trace_id']
                if trace_id in processed_trace_ids:
                    continue
                processed_trace_ids.add(trace_id)

                # 分析这个调用链中的所有异常
                chain_analysis = self._analyze_trace_chain_anomalies(traces_data, trace_id, p95_threshold_ms, p99_threshold_ms)
                if chain_analysis['anomaly_spans']:
                    trace_chain_anomalies.append(chain_analysis)

                    self.logger.info(f"🔗 调用链{trace_id[:8]}...分析: {len(chain_analysis['anomaly_spans'])}个异常span")

            # 第三步：构建最终的异常列表
            for chain in trace_chain_anomalies:
                for span_info in chain['anomaly_spans']:
                    anomalies['outlier_spans'].append(span_info['idx'])
                    anomalies['count'] += 1

                    if span_info['duration_ms'] > p99_threshold_ms:
                        anomalies['severe_outlier_spans'].append(span_info['idx'])
                        anomalies['severe_count'] += 1

            # 保存调用链分析结果
            anomalies['trace_chain_analysis'] = trace_chain_anomalies

            self.logger.info(f"✅ 调用链分析完成: {len(trace_chain_anomalies)}条异常调用链, {anomalies['count']}个异常span")

            # 2. 业务关键操作的延迟异常
            if 'operationName' in traces_data.columns:
                for operation in self.optimized_thresholds['business_patterns']['critical_operations']:
                    op_data = traces_data[traces_data['operationName'] == operation]
                    if not op_data.empty:
                        # 使用该操作的P95作为阈值
                        op_p95_us = op_data['duration'].quantile(0.95)
                        op_threshold_us = op_p95_us * self.optimized_thresholds['duration']['p95_multiplier']

                        slow_ops = op_data[op_data['duration'] > op_threshold_us]
                        if not slow_ops.empty:
                            anomalies['business_critical_slow'].extend(slow_ops.index.tolist())
                            anomalies['count'] += len(slow_ops)

            # 3. 绝对阈值检测（作为兜底）
            absolute_high_us = self.optimized_thresholds['duration']['absolute_high']
            absolute_critical_us = self.optimized_thresholds['duration']['absolute_critical']

            high_latency_spans = traces_data[traces_data['duration'] > absolute_high_us]
            critical_latency_spans = traces_data[traces_data['duration'] > absolute_critical_us]

            anomalies['count'] += len(high_latency_spans)
            anomalies['severe_count'] += len(critical_latency_spans)

            return anomalies

        except Exception as e:
            self.logger.error(f"❌ 动态延迟异常检测失败: {e}")
            return {'count': 0, 'severe_count': 0, 'outlier_spans': [], 'severe_outlier_spans': [], 'business_critical_slow': []}

    def _detect_enhanced_errors(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """🎯 增强的错误检测 - gRPC + HTTP + 业务错误"""
        try:
            errors = {
                'total_errors': 0,
                'grpc_errors': [],
                'http_errors': [],
                'timeout_errors': [],
                'business_errors': [],
                'error_details': []
            }

            if traces_data.empty:
                return errors

            # 1. gRPC状态码错误检测
            grpc_errors = self._detect_grpc_status_errors_enhanced(traces_data)
            if not grpc_errors.empty:
                errors['grpc_errors'] = grpc_errors.index.tolist()
                errors['total_errors'] += len(grpc_errors)
                errors['error_details'].extend([
                    {'type': 'grpc_error', 'span_id': idx, 'operation': row.get('operationName', 'unknown')}
                    for idx, row in grpc_errors.iterrows()
                ])

            # 2. HTTP错误检测
            http_errors = self._detect_http_errors_enhanced(traces_data)
            if not http_errors.empty:
                errors['http_errors'] = http_errors.index.tolist()
                errors['total_errors'] += len(http_errors)
                errors['error_details'].extend([
                    {'type': 'http_error', 'span_id': idx, 'operation': row.get('operationName', 'unknown')}
                    for idx, row in http_errors.iterrows()
                ])

            # 3. 超时错误检测（基于logs和tags）
            timeout_errors = self._detect_timeout_errors_enhanced(traces_data)
            if not timeout_errors.empty:
                errors['timeout_errors'] = timeout_errors.index.tolist()
                errors['total_errors'] += len(timeout_errors)
                errors['error_details'].extend([
                    {'type': 'timeout_error', 'span_id': idx, 'operation': row.get('operationName', 'unknown')}
                    for idx, row in timeout_errors.iterrows()
                ])

            # 4. 业务错误检测（基于operationName和特定模式）
            business_errors = self._detect_business_errors_enhanced(traces_data)
            if not business_errors.empty:
                errors['business_errors'] = business_errors.index.tolist()
                errors['total_errors'] += len(business_errors)
                errors['error_details'].extend([
                    {'type': 'business_error', 'span_id': idx, 'operation': row.get('operationName', 'unknown')}
                    for idx, row in business_errors.iterrows()
                ])

            return errors

        except Exception as e:
            self.logger.error(f"❌ 增强错误检测失败: {e}")
            return {'total_errors': 0, 'grpc_errors': [], 'http_errors': [], 'timeout_errors': [], 'business_errors': [], 'error_details': []}

    def _detect_business_critical_anomalies(self, traces_data: pd.DataFrame, duration_stats: Dict[str, Any]) -> Dict[str, Any]:
        """🎯 业务关键操作异常检测"""
        try:
            anomalies = {
                'count': 0,
                'critical_operation_failures': [],
                'high_frequency_operation_issues': [],
                'payment_checkout_issues': []
            }

            if 'operationName' not in traces_data.columns or traces_data.empty:
                return anomalies

            # 1. 关键业务操作异常检测
            for operation in self.optimized_thresholds['business_patterns']['critical_operations']:
                op_data = traces_data[traces_data['operationName'] == operation]
                if not op_data.empty:
                    # 检查该操作的错误率和延迟
                    op_errors = self._detect_operation_specific_errors(op_data, operation)
                    if op_errors > 0:
                        anomalies['critical_operation_failures'].append({
                            'operation': operation,
                            'error_count': op_errors,
                            'total_count': len(op_data)
                        })
                        anomalies['count'] += op_errors

            # 2. 高频操作问题检测
            for operation in self.optimized_thresholds['business_patterns']['high_frequency_operations']:
                op_data = traces_data[traces_data['operationName'] == operation]
                if len(op_data) > 100:  # 只检查高频操作
                    # 检查延迟分布异常
                    if self._is_operation_latency_abnormal(op_data):
                        anomalies['high_frequency_operation_issues'].append({
                            'operation': operation,
                            'count': len(op_data),
                            'issue_type': 'latency_distribution_abnormal'
                        })
                        anomalies['count'] += 1

            # 3. 支付和结账流程特殊检测
            payment_ops = traces_data[traces_data['operationName'].str.contains('Payment|Checkout', case=False, na=False)]
            if not payment_ops.empty:
                payment_issues = self._detect_payment_checkout_issues(payment_ops)
                if payment_issues > 0:
                    anomalies['payment_checkout_issues'].append({
                        'issue_count': payment_issues,
                        'total_payment_ops': len(payment_ops)
                    })
                    anomalies['count'] += payment_issues

            return anomalies

        except Exception as e:
            self.logger.error(f"❌ 业务关键异常检测失败: {e}")
            return {'count': 0, 'critical_operation_failures': [], 'high_frequency_operation_issues': [], 'payment_checkout_issues': []}

    def _detect_trace_chain_anomalies(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """🎯 调用链结构异常检测"""
        try:
            anomalies = {
                'count': 0,
                'orphaned_spans': [],
                'circular_references': [],
                'incomplete_traces': [],
                'abnormal_depth_traces': []
            }

            if 'traceID' not in traces_data.columns or traces_data.empty:
                return anomalies

            # 按traceID分组分析
            for trace_id, trace_spans in traces_data.groupby('traceID'):
                # 1. 检查孤立span（没有正确的父子关系）
                orphaned = self._detect_orphaned_spans(trace_spans)
                if orphaned > 0:
                    anomalies['orphaned_spans'].append({'trace_id': trace_id, 'orphaned_count': orphaned})
                    anomalies['count'] += orphaned

                # 2. 检查异常深度的调用链（过深或过浅）
                depth_issue = self._detect_abnormal_trace_depth(trace_spans)
                if depth_issue:
                    anomalies['abnormal_depth_traces'].append({'trace_id': trace_id, 'depth_issue': depth_issue})
                    anomalies['count'] += 1

                # 3. 检查不完整的调用链
                if self._is_trace_incomplete(trace_spans):
                    anomalies['incomplete_traces'].append({'trace_id': trace_id, 'span_count': len(trace_spans)})
                    anomalies['count'] += 1

            return anomalies

        except Exception as e:
            self.logger.error(f"❌ 调用链结构异常检测失败: {e}")
            return {'count': 0, 'orphaned_spans': [], 'circular_references': [], 'incomplete_traces': [], 'abnormal_depth_traces': []}

    def _detect_grpc_status_errors_enhanced(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """🎯 增强的gRPC状态码错误检测"""
        try:
            error_indices = []
            for idx, row in traces_data.iterrows():
                tags = row.get('tags', [])
                if isinstance(tags, str):
                    import json
                    try:
                        tags = json.loads(tags)
                    except:
                        continue

                if isinstance(tags, list):
                    for tag in tags:
                        if (isinstance(tag, dict) and
                            tag.get('key') == 'rpc.grpc.status_code' and
                            tag.get('value', 0) != 0):
                            error_indices.append(idx)
                            break

            return traces_data.iloc[error_indices] if error_indices else pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ gRPC错误检测失败: {e}")
            return pd.DataFrame()

    def _detect_http_errors_enhanced(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """🎯 增强的HTTP错误检测"""
        try:
            error_indices = []
            for idx, row in traces_data.iterrows():
                tags = row.get('tags', [])
                if isinstance(tags, str):
                    import json
                    try:
                        tags = json.loads(tags)
                    except:
                        continue

                if isinstance(tags, list):
                    for tag in tags:
                        if isinstance(tag, dict):
                            # 检查HTTP状态码
                            if (tag.get('key') == 'http.status_code' and
                                isinstance(tag.get('value'), (int, str))):
                                try:
                                    status_code = int(tag.get('value'))
                                    if status_code >= 400:  # 4xx和5xx错误
                                        error_indices.append(idx)
                                        break
                                except:
                                    continue

            return traces_data.iloc[error_indices] if error_indices else pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ HTTP错误检测失败: {e}")
            return pd.DataFrame()

    def _detect_timeout_errors_enhanced(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """🎯 增强的超时错误检测"""
        try:
            error_indices = []
            timeout_keywords = self.optimized_thresholds['error_detection']['timeout_keywords']

            for idx, row in traces_data.iterrows():
                # 检查tags中的超时信息
                tags = row.get('tags', [])
                logs = row.get('logs', [])

                # 检查tags
                if isinstance(tags, str):
                    for keyword in timeout_keywords:
                        if keyword.lower() in tags.lower():
                            error_indices.append(idx)
                            break

                # 检查logs
                if isinstance(logs, str):
                    for keyword in timeout_keywords:
                        if keyword.lower() in logs.lower():
                            error_indices.append(idx)
                            break

            return traces_data.iloc[error_indices] if error_indices else pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ 超时错误检测失败: {e}")
            return pd.DataFrame()

    def _detect_business_errors_enhanced(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """🎯 业务错误检测"""
        try:
            error_indices = []

            for idx, row in traces_data.iterrows():
                operation_name = row.get('operationName', '')

                # 检查特定的业务错误模式
                if any(error_pattern in operation_name.lower() for error_pattern in ['error', 'fail', 'exception']):
                    error_indices.append(idx)

            return traces_data.iloc[error_indices] if error_indices else pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ 业务错误检测失败: {e}")
            return pd.DataFrame()

    def _detect_operation_specific_errors(self, op_data: pd.DataFrame, operation: str) -> int:
        """检测特定操作的错误数量"""
        try:
            error_count = 0

            # 检查gRPC错误
            grpc_errors = self._detect_grpc_status_errors_enhanced(op_data)
            error_count += len(grpc_errors)

            # 检查HTTP错误
            http_errors = self._detect_http_errors_enhanced(op_data)
            error_count += len(http_errors)

            # 检查超时错误
            timeout_errors = self._detect_timeout_errors_enhanced(op_data)
            error_count += len(timeout_errors)

            return error_count

        except Exception as e:
            self.logger.error(f"❌ 操作特定错误检测失败: {e}")
            return 0

    def _is_operation_latency_abnormal(self, op_data: pd.DataFrame) -> bool:
        """检查操作延迟分布是否异常"""
        try:
            if 'duration' not in op_data.columns or len(op_data) < 10:
                return False

            durations = op_data['duration']

            # 检查是否有异常的延迟分布（如双峰分布、极端离群值等）
            p95 = durations.quantile(0.95)
            p50 = durations.quantile(0.50)

            # 如果P95是P50的10倍以上，认为分布异常
            if p95 > p50 * 10:
                return True

            # 检查是否有超过P99的3倍的极端值
            p99 = durations.quantile(0.99)
            extreme_values = durations[durations > p99 * 3]
            if len(extreme_values) > len(durations) * 0.01:  # 超过1%的极端值
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 延迟分布异常检测失败: {e}")
            return False

    def _detect_payment_checkout_issues(self, payment_ops: pd.DataFrame) -> int:
        """检测支付和结账流程问题"""
        try:
            issues = 0

            # 检查支付操作的错误率
            payment_errors = self._detect_grpc_status_errors_enhanced(payment_ops)
            issues += len(payment_errors)

            # 检查支付操作的超时
            payment_timeouts = self._detect_timeout_errors_enhanced(payment_ops)
            issues += len(payment_timeouts)

            # 检查支付操作的异常延迟
            if 'duration' in payment_ops.columns and not payment_ops.empty:
                # 支付操作延迟超过5秒认为异常
                slow_payments = payment_ops[payment_ops['duration'] > 5000000]  # 5秒 = 5,000,000微秒
                issues += len(slow_payments)

            return issues

        except Exception as e:
            self.logger.error(f"❌ 支付结账问题检测失败: {e}")
            return 0

    def _detect_orphaned_spans(self, trace_spans: pd.DataFrame) -> int:
        """检测孤立的span（没有正确父子关系的span）"""
        try:
            if 'references' not in trace_spans.columns or len(trace_spans) <= 1:
                return 0

            orphaned_count = 0
            span_ids = set(trace_spans['spanID'].tolist())

            for idx, row in trace_spans.iterrows():
                references = row.get('references', [])
                if isinstance(references, str):
                    import json
                    try:
                        references = json.loads(references)
                    except:
                        references = []

                # 检查是否有有效的父span引用
                has_valid_parent = False
                if isinstance(references, list):
                    for ref in references:
                        if isinstance(ref, dict) and ref.get('refType') == 'CHILD_OF':
                            parent_span_id = ref.get('spanID')
                            if parent_span_id in span_ids:
                                has_valid_parent = True
                                break

                # 如果不是根span但没有有效父span，则认为是孤立的
                if not has_valid_parent and isinstance(references, list) and len(references) > 0:  # 有references但没有有效父span
                    orphaned_count += 1

            return orphaned_count

        except Exception as e:
            self.logger.error(f"❌ 孤立span检测失败: {e}")
            return 0

    def _detect_abnormal_trace_depth(self, trace_spans: pd.DataFrame) -> str:
        """检测异常的调用链深度"""
        try:
            if len(trace_spans) < 2:
                return "too_shallow"

            # 简单的深度估算：span数量
            span_count = len(trace_spans)

            # 基于经验值判断
            if span_count < 3:
                return "too_shallow"  # 调用链过浅
            elif span_count > 100:
                return "too_deep"     # 调用链过深
            else:
                return ""  # 正常

        except Exception as e:
            self.logger.error(f"❌ 调用链深度检测失败: {e}")
            return ""

    def _is_trace_incomplete(self, trace_spans: pd.DataFrame) -> bool:
        """检查调用链是否不完整"""
        try:
            # 检查是否缺少关键的开始或结束span
            operation_names = trace_spans['operationName'].tolist() if 'operationName' in trace_spans.columns else []

            # 如果有Frontend操作但没有对应的后端服务操作，可能不完整
            has_frontend = any('Frontend' in op for op in operation_names)
            has_backend = any(service in op for op in operation_names
                            for service in ['ProductCatalog', 'Currency', 'Cart', 'Payment'])

            if has_frontend and not has_backend:
                return True

            # 检查span的时间跨度是否异常短（可能被截断）
            if 'startTime' in trace_spans.columns and 'duration' in trace_spans.columns:
                if len(trace_spans) > 5:  # 只对复杂调用链检查
                    total_duration = trace_spans['duration'].sum()
                    if total_duration < 1000:  # 总时长小于1ms，可能不完整
                        return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 调用链完整性检测失败: {e}")
            return False
    
    def _detect_grpc_errors(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """检测gRPC错误 - 复用现有逻辑"""
        try:
            grpc_errors = []
            for idx, trace in traces_data.iterrows():
                tags = trace.get('tags', [])
                if isinstance(tags, str):
                    import json
                    tags = json.loads(tags)
                
                # 检查gRPC状态码
                for tag in tags:
                    if (tag.get('key') == 'rpc.grpc.status_code' and 
                        tag.get('value', 0) != 0):
                        grpc_errors.append(idx)
                        break
            
            return traces_data.iloc[grpc_errors] if grpc_errors else pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"gRPC error detection failed: {e}")
            return pd.DataFrame()

    def _optimized_anomaly_filtering(self, traces_data: pd.DataFrame, duration_info: Dict) -> pd.DataFrame:
        """优化的异常筛选 - 基于P99统计阈值和频次过滤"""
        try:
            if traces_data.empty:
                return traces_data

            self.logger.info("🔄 开始优化异常筛选...")

            # 1. 基于P99的严格Duration异常筛选
            duration_anomalies = self._filter_p99_duration_anomalies(traces_data, duration_info)
            if not duration_anomalies.empty:
                self.logger.info(f"   P99 Duration异常: {len(duration_anomalies)} 个span")

            # 2. 增强的故障检测（基于gRPC状态码、references关系和logs事件）
            enhanced_faults = self._enhanced_fault_detection(traces_data)
            if not enhanced_faults.empty:
                self.logger.info(f"   增强故障检测: {len(enhanced_faults)} 个span")

            # 3. 传统gRPC错误筛选（作为补充）
            grpc_anomalies = self._detect_grpc_errors(traces_data)
            if not grpc_anomalies.empty:
                self.logger.info(f"   传统gRPC错误: {len(grpc_anomalies)} 个span")

            # 4. 频次过滤的异常调用链筛选
            trace_chain_anomalies = self._filter_frequent_anomalous_traces(traces_data, duration_info)
            if not trace_chain_anomalies.empty:
                self.logger.info(f"   频繁异常调用链: {len(trace_chain_anomalies)} 个span")

            # 5. 合并异常数据并应用频次过滤
            anomaly_traces = []
            if not duration_anomalies.empty:
                anomaly_traces.append(duration_anomalies)
            if not enhanced_faults.empty:
                anomaly_traces.append(enhanced_faults)
            if not grpc_anomalies.empty:
                anomaly_traces.append(grpc_anomalies)
            if not trace_chain_anomalies.empty:
                anomaly_traces.append(trace_chain_anomalies)

            if anomaly_traces:
                combined_anomalies = pd.concat(anomaly_traces, ignore_index=True)
                # 去重
                filtered_traces = combined_anomalies.drop_duplicates(subset=['traceID', 'spanID'])

                # 应用频次过滤
                final_traces = self._apply_frequency_filtering(filtered_traces)
                self.logger.info(f"🔍 优化筛选汇总: {len(final_traces)} 个高频异常span")
                return final_traces
            else:
                # 如果没有检测到异常，返回P99以上的数据
                if 'duration' in traces_data.columns and duration_info.get('p99_ms'):
                    p99_threshold_us = duration_info['p99_ms'] * 1000
                    fallback_traces = traces_data[traces_data['duration'] >= p99_threshold_us]
                    self.logger.info(f"🔍 未检测到明显异常，返回P99以上: {len(fallback_traces)} 个span")
                    return fallback_traces
                else:
                    return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ 优化异常筛选失败: {e}")
            # 降级到原有筛选逻辑
            return self._intelligent_anomaly_filtering(traces_data, duration_info)

    def _filter_p99_duration_anomalies(self, traces_data: pd.DataFrame, duration_info: Dict) -> pd.DataFrame:
        """基于P99统计阈值的严格Duration异常筛选"""
        try:
            if 'duration' not in traces_data.columns or not duration_info.get('p99_ms'):
                return pd.DataFrame()

            anomaly_conditions = []

            # 条件1: P99统计异常（筛选出最慢的1%）
            p99_threshold_us = duration_info['p99_ms'] * 1000  # 毫秒 → 微秒
            condition1 = traces_data['duration'] > p99_threshold_us

            # 条件2: 绝对阈值 - 超过5秒的严重延迟（降低阈值以捕获更多异常）
            # 注意：duration单位为微秒(μs)
            condition2 = traces_data['duration'] > EXTREME_LATENCY_THRESHOLD_US

            # 条件3: 极端离群值 - 超过平均值5倍标准差（更严格）
            if duration_info.get('std_ms', 0) > 0:
                mean_us = duration_info['mean_ms'] * 1000
                std_us = duration_info['std_ms'] * 1000
                condition3 = traces_data['duration'] > (mean_us + 5 * std_us)
            else:
                condition3 = pd.Series([False] * len(traces_data))

            # 合并条件（OR逻辑）
            combined_condition = condition1 | condition2 | condition3
            duration_anomalies = traces_data[combined_condition]

            return duration_anomalies

        except Exception as e:
            self.logger.error(f"❌ P99 Duration异常筛选失败: {e}")
            return pd.DataFrame()

    def _filter_frequent_anomalous_traces(self, traces_data: pd.DataFrame, duration_info: Dict) -> pd.DataFrame:
        """筛选频繁出现的异常调用链"""
        try:
            if 'traceID' not in traces_data.columns:
                return pd.DataFrame()

            # 按traceID分组，统计每个trace的异常指标
            trace_anomaly_scores = {}

            for trace_id, group in traces_data.groupby('traceID'):
                if len(group) < 2:  # 单span的trace跳过
                    continue

                anomaly_score = 0

                # 计算该trace的异常得分
                for _, span in group.iterrows():
                    # Duration异常得分
                    duration = span.get('duration', 0)
                    if duration_info.get('p95_ms'):
                        p95_threshold_us = duration_info['p95_ms'] * 1000
                        if duration > p95_threshold_us:
                            anomaly_score += 1

                    # 错误指示器得分
                    if self._check_error_indicators_strict(span):
                        anomaly_score += 2  # 错误权重更高

                # 计算异常比例
                anomaly_ratio = anomaly_score / len(group)
                if anomaly_ratio > 0.5:  # 50%以上的span有异常（更严格）
                    trace_anomaly_scores[trace_id] = anomaly_ratio

            # 只保留出现频次>=2的异常trace（频次过滤）
            trace_frequency = {}
            for trace_id in trace_anomaly_scores.keys():
                # 统计相同服务和操作的trace频次
                trace_spans = traces_data[traces_data['traceID'] == trace_id]
                if not trace_spans.empty:
                    service_ops = set()
                    for _, span in trace_spans.iterrows():
                        service = self._extract_service_name(span)
                        operation = span.get('operationName', 'unknown')
                        service_ops.add(f"{service}:{operation}")

                    # 统计相同服务操作组合的频次
                    for service_op in service_ops:
                        trace_frequency[service_op] = trace_frequency.get(service_op, 0) + 1

            # 收集高频异常trace的所有span
            frequent_anomalous_traces = []
            for trace_id, anomaly_ratio in trace_anomaly_scores.items():
                trace_spans = traces_data[traces_data['traceID'] == trace_id]

                # 检查是否为高频异常
                is_frequent = False
                for _, span in trace_spans.iterrows():
                    service = self._extract_service_name(span)
                    operation = span.get('operationName', 'unknown')
                    service_op = f"{service}:{operation}"
                    if trace_frequency.get(service_op, 0) >= 2:  # 频次阈值
                        is_frequent = True
                        break

                if is_frequent:
                    frequent_anomalous_traces.extend(trace_spans.to_dict('records'))

            return pd.DataFrame(frequent_anomalous_traces) if frequent_anomalous_traces else pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ 频繁异常调用链筛选失败: {e}")
            return pd.DataFrame()

    def _check_error_indicators_strict(self, span) -> bool:
        """严格的错误指示器检查"""
        try:
            # 1. 检查严重duration异常（提高阈值）
            duration = span.get('duration', 0)
            # 注意：duration单位为微秒(μs)
            if duration > SEVERE_LATENCY_THRESHOLD_US:
                return True

            # 2. 检查明确的错误标识
            tags_str = str(span.get('tags', ''))
            critical_errors = [
                'error', 'exception', 'timeout', 'failed', 'failure',
                '500', '502', '503', '504',
                'connection_refused', 'connection_timeout',
                'circuit_breaker'
            ]
            if any(error in tags_str.lower() for error in critical_errors):
                return True

            # 3. 检查logs中的错误信息
            logs = span.get('logs', [])
            if isinstance(logs, list):
                for log in logs:
                    if isinstance(log, dict):
                        fields = log.get('fields', [])
                        for field in fields:
                            if isinstance(field, dict):
                                value = str(field.get('value', '')).lower()
                                if any(error in value for error in critical_errors):
                                    return True

            return False

        except Exception as e:
            self.logger.error(f"严格错误指示器检查失败: {e}")
            return False

    def _apply_frequency_filtering(self, anomaly_traces: pd.DataFrame) -> pd.DataFrame:
        """应用频次过滤，优先保留重复出现的异常模式"""
        try:
            if anomaly_traces.empty:
                return anomaly_traces

            # 统计异常模式的频次
            pattern_frequency = {}

            for _, span in anomaly_traces.iterrows():
                service = self._extract_service_name(span)
                operation = span.get('operationName', 'unknown')

                # 创建异常模式标识
                pattern = f"{service}:{operation}"
                pattern_frequency[pattern] = pattern_frequency.get(pattern, 0) + 1

            # 过滤低频异常（只保留频次>=3的模式）
            frequent_patterns = {pattern for pattern, freq in pattern_frequency.items() if freq >= 3}

            if not frequent_patterns:
                # 如果没有高频模式，降低阈值到2
                frequent_patterns = {pattern for pattern, freq in pattern_frequency.items() if freq >= 2}

            if not frequent_patterns:
                # 如果仍然没有，保留duration最高的前20%
                if 'duration' in anomaly_traces.columns:
                    threshold = anomaly_traces['duration'].quantile(0.8)
                    return anomaly_traces[anomaly_traces['duration'] >= threshold]
                else:
                    return anomaly_traces.head(min(100, len(anomaly_traces)))  # 最多100个

            # 保留高频异常模式的span
            filtered_spans = []
            for _, span in anomaly_traces.iterrows():
                service = self._extract_service_name(span)
                operation = span.get('operationName', 'unknown')
                pattern = f"{service}:{operation}"

                if pattern in frequent_patterns:
                    filtered_spans.append(span)

            result = pd.DataFrame(filtered_spans) if filtered_spans else pd.DataFrame()

            self.logger.info(f"📊 频次过滤结果: {len(frequent_patterns)} 个高频模式, {len(result)} 个span")
            return result

        except Exception as e:
            self.logger.error(f"❌ 频次过滤失败: {e}")
            return anomaly_traces

    def _extract_service_name(self, span) -> str:
        """提取服务名称 - 修复客户端span的服务识别问题"""
        try:
            # 🔧 修复：检查span类型，对客户端span从operationName提取目标服务
            tags = span.get('tags', [])
            span_kind = None

            # 提取span.kind - 处理numpy array和list两种格式
            try:
                if hasattr(tags, '__iter__'):  # 可迭代对象（包括numpy array）
                    for tag in tags:
                        if isinstance(tag, dict) and tag.get('key') == 'span.kind':
                            span_kind = tag.get('value', '')
                            break
            except Exception as e:
                self.logger.debug(f"解析tags失败: {e}")

            # 🔧 调试：输出span信息
            operation_name = span.get('operationName', '')
            self.logger.debug(f"🔍 分析span: {operation_name}, span_kind: {span_kind}")

            # 对于客户端span，从operationName提取目标服务（真正的故障服务）
            if span_kind == 'client':
                if 'hipstershop.' in operation_name:
                    # 格式：hipstershop.PaymentService/Charge
                    service_part = operation_name.split('hipstershop.')[1].split('/')[0]
                    if service_part.endswith('Service'):
                        service_part = service_part[:-7]  # 移除'Service'

                    # 映射到标准服务名
                    service_mapping = {
                        'Payment': 'paymentservice',
                        'ProductCatalog': 'productcatalogservice',
                        'Currency': 'currencyservice',
                        'Frontend': 'frontend',
                        'Checkout': 'checkoutservice',
                        'Cart': 'cartservice',
                        'Recommendation': 'recommendationservice',
                        'Ad': 'adservice',
                        'Shipping': 'shippingservice',
                        'Email': 'emailservice'
                    }

                    target_service = service_mapping.get(service_part, service_part.lower())
                    # 性能优化：移除调试日志
                    return target_service

            # 对于服务端span或无法识别的span，使用process.serviceName
            process = span.get('process', {})
            if isinstance(process, dict):
                service_name = process.get('serviceName', 'unknown')
                self.logger.debug(f"🔍 使用process.serviceName: {service_name}")
                return service_name
            return 'unknown'
        except Exception as e:
            self.logger.debug(f"提取服务名称失败: {e}")
            return 'unknown'

    def _extract_service_from_fast_detection(self, fast_result: Dict, anomaly_type: str) -> str:
        """从快速检测结果中提取服务名，避免unknown值"""
        try:
            # 1. 尝试从anomaly_details中提取
            anomaly_details = fast_result.get('anomaly_details', {})
            if anomaly_type in anomaly_details:
                detail = anomaly_details[anomaly_type]
                if isinstance(detail, dict):
                    # 尝试从服务统计中提取
                    services = detail.get('services', [])
                    if services and isinstance(services, list):
                        return services[0]  # 返回第一个服务

            # 2. 尝试从service_stats中提取
            service_stats = fast_result.get('service_stats', {})
            if service_stats:
                # 找到span数量最多的服务（最可能是异常服务）
                max_spans = 0
                primary_service = None
                for service, stats in service_stats.items():
                    if service != 'unknown' and isinstance(stats, dict):
                        span_count = stats.get('span_count', 0)
                        if span_count > max_spans:
                            max_spans = span_count
                            primary_service = service

                if primary_service:
                    return primary_service

            # 3. 尝试从duration_stats中推断
            duration_stats = fast_result.get('duration_stats', {})
            if duration_stats:
                # 如果有高延迟，通常是frontend或checkout相关
                p99_ms = duration_stats.get('p99_ms', 0)
                if p99_ms > 1000:  # 高延迟通常是frontend
                    return 'frontend'
                elif p99_ms > 500:  # 中等延迟可能是checkout
                    return 'checkoutservice'

            # 4. 根据异常类型推断服务
            if 'grpc' in anomaly_type.lower():
                return 'frontend'  # gRPC错误通常来自frontend
            elif 'timeout' in anomaly_type.lower():
                return 'paymentservice'  # 超时通常是payment相关
            elif 'latency' in anomaly_type.lower():
                return 'frontend'  # 延迟异常通常是frontend

            # 5. 最后的备选方案
            return 'frontend'  # 大多数异常都与frontend相关

        except Exception as e:
            self.logger.debug(f"从快速检测结果提取服务名失败: {e}")
            return 'frontend'  # 默认返回frontend而不是unknown

    def _safe_extract_service_from_evidence(self, evidence: Dict) -> str:
        """从错误证据中安全提取服务名，避免unknown值"""
        try:
            # 1. 优先从service字段提取
            service = evidence.get('service', '')
            if service and service != 'unknown':
                return service

            # 2. 尝试从其他字段提取
            entity_name = evidence.get('entity_name', '')
            if entity_name and entity_name != 'unknown':
                return entity_name

            # 3. 从描述中推断服务名
            description = evidence.get('description', '')
            if 'frontend' in description.lower():
                return 'frontend'
            elif 'checkout' in description.lower():
                return 'checkoutservice'
            elif 'payment' in description.lower():
                return 'paymentservice'
            elif 'cart' in description.lower():
                return 'cartservice'
            elif 'product' in description.lower():
                return 'productcatalogservice'
            elif 'shipping' in description.lower():
                return 'shippingservice'
            elif 'email' in description.lower():
                return 'emailservice'
            elif 'currency' in description.lower():
                return 'currencyservice'
            elif 'ad' in description.lower():
                return 'adservice'
            elif 'recommendation' in description.lower():
                return 'recommendationservice'
            elif 'redis' in description.lower():
                return 'redis-cart'

            # 4. 从错误类型推断
            error_type = evidence.get('error_type', '')
            if 'grpc' in error_type.lower() or 'http' in error_type.lower():
                return 'frontend'  # HTTP/gRPC错误通常来自frontend
            elif 'timeout' in error_type.lower():
                return 'paymentservice'  # 超时通常是payment相关

            # 5. 从证据来源推断
            source = evidence.get('source', '')
            if 'tags' in source:
                return 'frontend'  # tags错误通常来自frontend
            elif 'logs' in source:
                return 'frontend'  # logs错误也通常来自frontend

            # 6. 最后的备选方案
            self.logger.debug(f"无法从证据中提取服务名，使用默认值: {evidence}")
            return 'frontend'  # 大多数trace错误都与frontend相关

        except Exception as e:
            self.logger.debug(f"从错误证据提取服务名失败: {e}")
            return 'frontend'
  
    async def _load_trace_data(self, case_uuid: str, start_time: str, end_time: str) -> pd.DataFrame:
        """加载trace数据 - 通过DataLoader获取"""
        try:
            self.logger.info(f"🔍 增强版TraceAgent从DataLoader获取traces数据")

            # 构建数据加载请求
            from .base_aiops_agent import DataLoadRequest
            from autogen_core import AgentId

            data_load_request = DataLoadRequest(
                case_uuid=case_uuid,
                start_time=start_time,
                end_time=end_time,
                data={}
            )

            # 获取DataLoader的Agent ID并发送请求
            data_loader_id = AgentId("DataLoader", "default")
            self.logger.info(f"📤 向DataLoader请求traces数据: {case_uuid}")

            response = await self.send_message(data_load_request, data_loader_id)

            if response and response.success and response.data:
                traces_data = response.data.get("traces")
                if traces_data is not None and not traces_data.empty:
                    self.logger.info(f"✅ 从DataLoader获取到 {len(traces_data)} 条traces记录")
                    # 🔧 修复：保存原始traces数据的引用，用于后续查找完整的span信息
                    self.original_traces_data = traces_data.copy()
                    return traces_data
                else:
                    self.logger.warning("⚠️ DataLoader返回的traces数据为空")
            else:
                self.logger.warning("⚠️ 从DataLoader获取数据失败")

            return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"Failed to load trace data: {e}")
            return pd.DataFrame()
    
    def _build_empty_response(self, message: AnalysisRequest) -> AnalysisResponse:
        """构建空数据响应"""
        return AnalysisResponse(
            case_uuid=message.case_uuid,
            start_time=message.start_time,
            end_time=message.end_time,
            data=message.data,
            analysis_type="traces",
            results={'no_trace_data': True},
            success=True
        )
    
    def _build_fast_response(self, message: AnalysisRequest, fast_result: Dict[str, Any], start_time: datetime) -> AnalysisResponse:
        """构建快速检测响应 - 包含标准化输出"""
        analysis_time = (datetime.now() - start_time).total_seconds() * 1000

        # 构建标准化输出
        standardized_output = self._build_standardized_output_from_fast_detection(
            fast_result, message.case_uuid, message.start_time, message.end_time
        )

        return AnalysisResponse(
            case_uuid=message.case_uuid,
            start_time=message.start_time,
            end_time=message.end_time,
            data=message.data,
            analysis_type="traces",
            results={
                'fast_detection_result': fast_result,
                'analysis_time_ms': analysis_time,
                'analysis_method': 'fast_anomaly_detection',
                'standardized_output': standardized_output
            },
            success=True
        )

    def _build_standardized_output_from_fast_detection(self, fast_result: Dict[str, Any],
                                                     case_uuid: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """从快速检测结果构建标准化输出"""
        try:
            # 从快速检测结果中提取异常信息
            anomaly_types = fast_result.get('anomaly_types', [])
            has_critical_anomalies = fast_result.get('has_critical_anomalies', False)

            suspicious_entities = []
            raw_data_sample = []

            # 如果有严重异常，构建可疑实体
            if has_critical_anomalies:
                for anomaly_type, count in anomaly_types:
                    # 🔧 修复：尝试从快速检测结果中提取服务名
                    service_name = self._extract_service_from_fast_detection(fast_result, anomaly_type)

                    suspicious_entity = {
                        "entity": {
                            "service": service_name,  # 🔧 修复：使用提取的服务名而不是unknown
                            "namespace": "hipstershop",
                            "trace_type": anomaly_type
                        },
                        "anomaly_feature": {
                            "anomaly_type": anomaly_type,
                            "anomaly_count": count,
                            "severity": "high" if "ultra_high_latency" in anomaly_type or "grpc_errors" in anomaly_type else "medium"
                        },
                        "confidence": fast_result.get('confidence', 0.8),
                        "raw_data_evidence": [f"FAST_DETECTION: {anomaly_type} | COUNT: {count}"],
                        "data_location": {
                            "detection_method": "fast_trace_detection",
                            "time_range": {"start": start_time, "end": end_time}
                        }
                    }
                    suspicious_entities.append(suspicious_entity)

                    # 添加原始数据样本
                    sample = f"TRACE_ANOMALY: {anomaly_type} | COUNT: {count} | CONFIDENCE: {fast_result.get('confidence', 0.8):.2f}"
                    raw_data_sample.append(sample)

            return {
                "agent_type": "enhanced_trace_agent",
                "analysis_id": f"enhanced_trace_{case_uuid}_{int(datetime.now().timestamp())}",
                "suspicious_entities": suspicious_entities,
                "raw_data_sample": raw_data_sample[:5],  # 最多5个样本
                "analysis_metadata": {
                    "total_spans": fast_result.get('total_spans', 0),
                    "detection_method": "fast_anomaly_detection",
                    "time_range": {"start": start_time, "end": end_time},
                    "confidence": fast_result.get('confidence', 0.0),
                    "duration_stats": fast_result.get('duration_stats', {})
                }
            }

        except Exception as e:
            self.logger.error(f"Failed to build standardized output from fast detection: {e}")
            return {
                "agent_type": "enhanced_trace_agent",
                "analysis_id": f"enhanced_trace_{case_uuid}_{int(datetime.now().timestamp())}",
                "suspicious_entities": [],
                "raw_data_sample": [],
                "error": str(e)
            }

    async def _single_llm_comprehensive_analysis(self, anomaly_traces: pd.DataFrame, case_uuid: str, start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """单次LLM综合分析 - 适用于中等数据量"""
        try:
            # 🔧 临时禁用问题段提取以验证标准化输出功能
            self.logger.info(f"🔍 跳过问题段提取（性能优化），直接使用异常数据: {len(anomaly_traces)} 个span")

            # 🔧 临时测试：直接构建标准化输出来验证功能
            if len(anomaly_traces) > 0:
                self.logger.info("🧪 开始测试标准化输出构建功能...")
                test_anomalies = []
                for _, span in anomaly_traces.head(10).iterrows():  # 只取前10个span进行测试
                    test_anomaly = {
                        "service": span.get("service", "unknown"),
                        "operationName": span.get("operationName", "unknown"),
                        "traceID": span.get("traceID", "unknown"),
                        "spanID": span.get("spanID", "unknown"),
                        "duration": span.get("duration", 0),
                        "startTimeMillis": span.get("startTimeMillis", 0),
                        "tags": span.get("tags", []),
                        "logs": span.get("logs", []),
                        "references": span.get("references", []),
                        "anomaly_type": "test_anomaly"
                    }
                    test_anomalies.append(test_anomaly)

                self.logger.info(f"🧪 构建测试异常数据: {len(test_anomalies)} 个")
                test_output = self._build_standardized_output_from_batch_results(
                    [], case_uuid, start_time, end_time, test_anomalies
                )
                self.logger.info(f"🧪 测试标准化输出构建完成: {len(test_output.get('suspicious_entities', []))} 个可疑实体")
                return test_output

            # # 基于trace重构的问题段提取（在LLM分析之前进行）
            # self.logger.info(f"🔍 开始问题段提取，输入数据: {len(anomaly_traces)} 个span")
            # problem_segments = self._extract_problematic_segments_from_traces(anomaly_traces)
            # if problem_segments:
            #     self.logger.info(f"🔍 从trace中提取了 {len(problem_segments)} 个问题段落")
            #     # 将问题段落转换回DataFrame格式供后续处理
            #     anomaly_traces = self._convert_segments_to_dataframe(problem_segments)
            #     self.logger.info(f"📊 问题段转换后数据量: {len(anomaly_traces)} 个span")
            # else:
            #     self.logger.warning("⚠️ 未提取到问题段落，使用原始异常数据")
            #     self.logger.info(f"📊 继续使用原始数据: {len(anomaly_traces)} 个span")

            # 预处理数据供LLM分析
            processed_data = self._preprocess_trace_data_for_llm(anomaly_traces)

            # 单次LLM调用进行综合分析
            llm_result = await self._llm_comprehensive_trace_analysis(processed_data, case_uuid)

            # 检索完整调用链（如果LLM分析发现了异常）
            complete_traces = {}
            if llm_result.get('anomalies'):
                complete_traces = await self._retrieve_complete_trace_chains_enhanced(
                    llm_result['anomalies'], anomaly_traces, case_uuid, start_time, end_time
                )
                llm_result['complete_trace_chains'] = complete_traces

            # 🔧 修复：增强LLM结果处理，包含service_dependency_analysis中的服务
            all_anomalies = llm_result.get('anomalies', [])

            # 🔧 关键修复：从service_dependency_analysis中提取被遗漏的服务
            dependency_analysis = llm_result.get('service_dependency_analysis', {})
            affected_services = dependency_analysis.get('affected_services', [])

            # 检查是否有服务在dependency_analysis中但不在anomalies中
            anomaly_services = set(anomaly.get('service', '') for anomaly in all_anomalies)
            missing_services = set(affected_services) - anomaly_services

            if missing_services:
                self.logger.info(f"🔧 发现LLM dependency_analysis中的遗漏服务: {list(missing_services)}")

                # 🔧 通用策略：为遗漏的服务创建基于LLM分析的异常记录
                for service in missing_services:
                    try:
                        # 🔧 通用方法：不依赖DataFrame结构，基于LLM分析创建异常
                        missing_anomaly = {
                            'traceID': 'dependency_analysis_identified',
                            'spanID': 'dependency_analysis_identified',
                            'operationName': f'{service} service operation',
                            'service': service,
                            'pod_instance': 'Unknown',
                            'anomaly_type': 'dependency_analysis_identified',
                            'severity': 'high',
                            'duration_ms': 60000,  # 基于LLM分析的典型超时值
                            'error_details': f'Service identified in LLM dependency analysis as affected service',
                            'evidence': f'LLM dependency analysis identified {service} as part of error propagation path',
                            'impact_scope': f'{service} service identified as critical component in fault chain'
                        }
                        all_anomalies.append(missing_anomaly)
                        self.logger.info(f"🔧 补充遗漏服务异常: {service} (基于LLM dependency分析)")

                    except Exception as e:
                        self.logger.warning(f"⚠️ 无法为遗漏服务 {service} 创建异常记录: {e}")
                        # 继续处理其他服务，不中断整个流程

            standardized_output = self._build_standardized_output_from_batch_results(
                [{'llm_result': llm_result, 'used_llm': True}], case_uuid, start_time, end_time, all_anomalies
            )

            return {
                'analysis_method': 'single_llm_comprehensive',
                'anomaly_count': len(anomaly_traces),
                'llm_result': llm_result,
                'llm_calls_used': 1,
                'complete_traces_count': len(complete_traces),
                'standardized_output': standardized_output  # 🔧 添加standardized_output
            }

        except Exception as e:
            self.logger.error(f"Single LLM analysis failed: {e}")
            return {'analysis_method': 'single_llm_comprehensive', 'error': str(e)}

    async def _prioritized_batch_analysis(self, anomaly_traces: pd.DataFrame, case_uuid: str, start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """优先级批次分析 - 适用于大数据量"""
        try:
            # 按严重程度排序
            sorted_traces = anomaly_traces.sort_values('duration', ascending=False)

            # 分成3个优先级批次
            batch_size = len(sorted_traces) // 3
            high_priority = sorted_traces.head(batch_size)
            medium_priority = sorted_traces.iloc[batch_size:batch_size*2]
            low_priority = sorted_traces.tail(len(sorted_traces) - batch_size*2)

            results = []

            # 高优先级批次 - 详细分析
            if not high_priority.empty:
                high_result = await self._analyze_priority_batch(high_priority, case_uuid, "high")
                results.append(high_result)

            # 中优先级批次 - 简化分析
            if not medium_priority.empty and len(results) < self.max_llm_calls:
                medium_result = await self._analyze_priority_batch(medium_priority, case_uuid, "medium")
                results.append(medium_result)

            # 低优先级批次 - 统计分析
            if not low_priority.empty and len(results) < self.max_llm_calls:
                low_result = self._statistical_analysis(low_priority, "low")
                results.append(low_result)

            # 合并所有批次的异常结果，检索完整调用链
            all_anomalies = []
            all_dependency_services = set()  # 🔧 收集所有dependency_analysis中的服务

            for result in results:
                # 🔧 修复：从正确的位置提取异常
                if result.get('used_llm') and 'llm_result' in result:
                    llm_result = result['llm_result']
                    llm_anomalies = llm_result.get('anomalies', [])
                    all_anomalies.extend(llm_anomalies)

                    # 🔧 关键修复：收集service_dependency_analysis中的服务
                    dependency_analysis = llm_result.get('service_dependency_analysis', {})
                    affected_services = dependency_analysis.get('affected_services', [])
                    all_dependency_services.update(affected_services)

                elif result.get('anomalies'):  # 保持向后兼容
                    all_anomalies.extend(result['anomalies'])

            # 🔧 通用修复：检查是否有服务在dependency_analysis中但不在anomalies中
            anomaly_services = set(anomaly.get('service', '') for anomaly in all_anomalies)
            missing_services = all_dependency_services - anomaly_services

            if missing_services:
                self.logger.info(f"🔧 批次分析发现LLM dependency_analysis中的遗漏服务: {list(missing_services)}")

                # 🔧 通用策略：为遗漏的服务创建基于LLM分析的异常记录
                for service in missing_services:
                    try:
                        # 🔧 通用方法：不依赖DataFrame结构，基于LLM分析创建异常
                        missing_anomaly = {
                            'traceID': 'dependency_analysis_identified',
                            'spanID': 'dependency_analysis_identified',
                            'operationName': f'{service} service operation',
                            'service': service,
                            'pod_instance': 'Unknown',
                            'anomaly_type': 'dependency_analysis_identified',
                            'severity': 'high',
                            'duration_ms': 60000,  # 基于LLM分析的典型超时值
                            'error_details': f'Service identified in LLM dependency analysis as affected service',
                            'evidence': f'LLM dependency analysis identified {service} as part of error propagation path',
                            'impact_scope': f'{service} service identified as critical component in fault chain'
                        }
                        all_anomalies.append(missing_anomaly)
                        self.logger.info(f"🔧 批次分析补充遗漏服务异常: {service} (基于LLM dependency分析)")

                    except Exception as e:
                        self.logger.warning(f"⚠️ 无法为遗漏服务 {service} 创建异常记录: {e}")
                        # 继续处理其他服务，不中断整个流程

            complete_traces = {}
            if all_anomalies:
                complete_traces = await self._retrieve_complete_trace_chains_enhanced(
                    all_anomalies, anomaly_traces, case_uuid, start_time, end_time
                )

            # 🔧 修复：构建standardized_output，将LLM发现的异常转换为suspicious_entities
            standardized_output = self._build_standardized_output_from_batch_results(
                results, case_uuid, start_time, end_time, all_anomalies
            )

            return {
                'analysis_method': 'prioritized_batch',
                'total_anomalies': len(anomaly_traces),
                'batch_results': results,
                'llm_calls_used': len([r for r in results if r.get('used_llm', False)]),
                'complete_trace_chains': complete_traces,
                'complete_traces_count': len(complete_traces),
                'standardized_output': standardized_output  # 🔧 添加standardized_output
            }

        except Exception as e:
            self.logger.error(f"Prioritized batch analysis failed: {e}")
            return {'analysis_method': 'prioritized_batch', 'error': str(e)}

    async def _analyze_priority_batch(self, batch_traces: pd.DataFrame, case_uuid: str, priority: str) -> Dict[str, Any]:
        """分析优先级批次"""
        try:
            # 采样关键数据
            sample_size = min(500, len(batch_traces))  # 每批次最多500条
            sampled_traces = batch_traces.head(sample_size)

            # 预处理数据
            processed_data = self._preprocess_trace_data_for_llm(sampled_traces)

            # LLM分析
            llm_result = await self._llm_comprehensive_trace_analysis(processed_data, case_uuid)

            return {
                'priority': priority,
                'batch_size': len(batch_traces),
                'sampled_size': len(sampled_traces),
                'llm_result': llm_result,
                'used_llm': True
            }

        except Exception as e:
            self.logger.error(f"Priority batch analysis failed for {priority}: {e}")
            return {
                'priority': priority,
                'batch_size': len(batch_traces),
                'error': str(e),
                'used_llm': False
            }

    def _statistical_analysis(self, traces: pd.DataFrame, priority: str) -> Dict[str, Any]:
        """统计分析 - 不使用LLM"""
        try:
            # 基础统计
            stats = {
                'priority': priority,
                'batch_size': len(traces),
                'used_llm': False,
                'statistical_summary': {
                    'avg_duration_ms': traces['duration'].mean() / 1000 if 'duration' in traces.columns else 0,
                    'max_duration_ms': traces['duration'].max() / 1000 if 'duration' in traces.columns else 0,
                    'unique_services': len(traces['process'].apply(lambda x: x.get('serviceName', '') if isinstance(x, dict) else '').unique()) if 'process' in traces.columns else 0,
                    'unique_operations': len(traces['operationName'].unique()) if 'operationName' in traces.columns else 0
                }
            }

            return stats

        except Exception as e:
            self.logger.error(f"Statistical analysis failed for {priority}: {e}")
            return {
                'priority': priority,
                'batch_size': len(traces),
                'error': str(e),
                'used_llm': False
            }

    def _preprocess_trace_data_for_llm(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """优化的LLM数据预处理 - 智能筛选和频次过滤"""
        try:
            self.logger.info("🔄 开始优化预处理trace数据供LLM分析")

            total_spans = len(traces_data)
            self.logger.info(f"📊 原始数据量: {total_spans} 个span")

            if traces_data.empty:
                return {'total_spans': 0, 'sample_traces': [], 'error': 'Empty traces data'}

            # 1. 计算统计信息
            duration_info = self._calculate_duration_statistics(traces_data)

            # 2. 应用频次过滤和模式识别
            pattern_filtered_traces = self._apply_pattern_based_filtering(traces_data, duration_info)

            # 3. 基于trace结构的智能证据保全
            self.logger.info(f"📋 准备进行trace级别的问题段提取: {len(pattern_filtered_traces)} 个候选span")

            # 3. 构建服务分布统计
            service_stats = self._build_enhanced_service_statistics(pattern_filtered_traces)

            # 4. 智能采样供LLM分析
            sample_traces = self._intelligent_sampling_for_llm(pattern_filtered_traces, max_samples=30)

            # 5. 构建LLM友好的数据结构
            processed_data = {
                'total_spans': total_spans,
                'filtered_spans': len(pattern_filtered_traces),
                'filtering_ratio': len(pattern_filtered_traces) / total_spans if total_spans > 0 else 0,
                'duration_statistics': duration_info,
                'service_distribution': service_stats,
                'sample_traces': sample_traces,
                'time_range': self._extract_time_range(traces_data),
                'anomaly_patterns': self._identify_anomaly_patterns(pattern_filtered_traces),
                'optimization_summary': {
                    'original_spans': total_spans,
                    'pattern_filtered': len(pattern_filtered_traces),
                    'final_samples': len(sample_traces),
                    'compression_ratio': len(sample_traces) / total_spans if total_spans > 0 else 0
                }
            }

            self.logger.info(f"✅ 优化预处理完成: {total_spans}→{len(pattern_filtered_traces)}→{len(sample_traces)} span, {len(service_stats)}个服务")
            return processed_data

        except Exception as e:
            self.logger.error(f"❌ 优化预处理失败: {e}")
            # 降级到简单处理
            return self._fallback_preprocessing(traces_data)

    def _apply_pattern_based_filtering(self, traces_data: pd.DataFrame, duration_info: Dict) -> pd.DataFrame:
        """基于模式的过滤 - 识别重复异常模式"""
        try:
            # 1. 统计异常模式频次
            pattern_stats = {}

            for _, span in traces_data.iterrows():
                service = self._extract_service_name(span)
                operation = span.get('operationName', 'unknown')
                duration = span.get('duration', 0)

                # 创建模式标识
                pattern_key = f"{service}:{operation}"

                if pattern_key not in pattern_stats:
                    pattern_stats[pattern_key] = {
                        'count': 0,
                        'total_duration': 0,
                        'max_duration': 0,
                        'spans': []
                    }

                pattern_stats[pattern_key]['count'] += 1
                pattern_stats[pattern_key]['total_duration'] += duration
                pattern_stats[pattern_key]['max_duration'] = max(pattern_stats[pattern_key]['max_duration'], duration)
                pattern_stats[pattern_key]['spans'].append(span)

            # 2. 识别高频异常模式
            frequent_patterns = []
            p99_threshold = duration_info.get('p99_ms', 1000) * 1000  # 转换为微秒

            for pattern_key, stats in pattern_stats.items():
                avg_duration = stats['total_duration'] / stats['count']

                # 高频异常条件：频次>=3 且 (平均延迟>P99 或 最大延迟>30秒)
                is_frequent = stats['count'] >= 3
                # 注意：duration单位为微秒(μs)
                is_anomalous = (avg_duration > p99_threshold) or (stats['max_duration'] > EXTREME_LATENCY_THRESHOLD_US)

                if is_frequent and is_anomalous:
                    frequent_patterns.append({
                        'pattern': pattern_key,
                        'count': stats['count'],
                        'avg_duration_ms': avg_duration / 1000,
                        'max_duration_ms': stats['max_duration'] / 1000,
                        'spans': stats['spans']
                    })

            # 3. 收集高频异常模式的span
            if frequent_patterns:
                # 按异常严重程度排序
                frequent_patterns.sort(key=lambda x: x['max_duration_ms'], reverse=True)

                filtered_spans = []
                for pattern in frequent_patterns[:10]:  # 最多保留10个最严重的模式
                    filtered_spans.extend(pattern['spans'])

                self.logger.info(f"📊 识别到 {len(frequent_patterns)} 个高频异常模式，保留 {len(filtered_spans)} 个span")
                return pd.DataFrame(filtered_spans)
            else:
                # 如果没有高频模式，降级到P99筛选
                if duration_info.get('p99_ms'):
                    p99_threshold_us = duration_info['p99_ms'] * 1000
                    fallback_traces = traces_data[traces_data['duration'] > p99_threshold_us]
                    self.logger.info(f"📊 未发现高频异常模式，降级到P99筛选: {len(fallback_traces)} 个span")
                    return fallback_traces
                else:
                    return traces_data.head(50)  # 最后的降级方案

        except Exception as e:
            self.logger.error(f"❌ 模式过滤失败: {e}")
            return traces_data

    def _build_enhanced_service_statistics(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """构建增强的服务统计信息"""
        try:
            service_stats = {}

            for _, span in traces_data.iterrows():
                service = self._extract_service_name(span)
                duration = span.get('duration', 0)
                operation = span.get('operationName', 'unknown')

                if service not in service_stats:
                    service_stats[service] = {
                        'span_count': 0,
                        'total_duration': 0,
                        'max_duration': 0,
                        'operations': set(),
                        'avg_duration_ms': 0
                    }

                service_stats[service]['span_count'] += 1
                service_stats[service]['total_duration'] += duration
                service_stats[service]['max_duration'] = max(service_stats[service]['max_duration'], duration)
                service_stats[service]['operations'].add(operation)

            # 计算平均值并转换为可序列化格式
            for service, stats in service_stats.items():
                if stats['span_count'] > 0:
                    stats['avg_duration_ms'] = stats['total_duration'] / stats['span_count'] / 1000
                stats['max_duration_ms'] = stats['max_duration'] / 1000
                stats['operations'] = list(stats['operations'])  # 转换set为list
                del stats['total_duration']  # 删除不需要的字段

            return service_stats

        except Exception as e:
            self.logger.error(f"❌ 构建服务统计失败: {e}")
            return {}

    def _intelligent_sampling_for_llm(self, traces_data: pd.DataFrame, max_samples: int = 30) -> List[Dict]:
        """智能采样供LLM分析 - 优先选择代表性异常"""
        try:
            if traces_data.empty:
                return []

            samples = []

            # 移除PaymentService单项检测 - 改为通用服务检测

            # 1. 按服务分组采样
            service_groups = traces_data.groupby(traces_data.apply(lambda x: self._extract_service_name(x), axis=1))

            # 🔧 调试：输出服务分组信息
            self.logger.info(f"🔍 服务分组结果: {list(service_groups.groups.keys())}")
            for service, group in service_groups:
                self.logger.info(f"   服务 {service}: {len(group)} 个span")
                # 移除PaymentService特殊处理

            samples_per_service = max(1, max_samples // len(service_groups))

            for service, group in service_groups:
                # 按duration降序排序，选择最异常的span
                sorted_group = group.sort_values('duration', ascending=False)
                service_samples = sorted_group.head(samples_per_service)

                for _, span in service_samples.iterrows():
                    sample = {
                        'traceID': span.get('traceID', 'unknown'),
                        'spanID': span.get('spanID', 'unknown'),
                        'service': service,
                        'operation': span.get('operationName', 'unknown'),
                        'duration_ms': span.get('duration', 0) / MICROSECONDS_TO_MILLISECONDS,
                        'start_time': span.get('startTimeMillis', 0),
                        'has_error_indicators': self._check_error_indicators_strict(span),
                        'tags_summary': self._extract_tags_summary(span)
                    }
                    samples.append(sample)

                    if len(samples) >= max_samples:
                        break

                if len(samples) >= max_samples:
                    break

            # 2. 按异常严重程度排序
            samples.sort(key=lambda x: x['duration_ms'], reverse=True)

            return samples[:max_samples]

        except Exception as e:
            self.logger.error(f"❌ 智能采样失败: {e}")
            return []

    def _extract_time_range(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """提取时间范围信息"""
        try:
            if 'startTimeMillis' in traces_data.columns:
                start_times = traces_data['startTimeMillis'].dropna()
                if len(start_times) > 0:
                    return {
                        'start': int(start_times.min()),
                        'end': int(start_times.max()),
                        'duration_seconds': (start_times.max() - start_times.min()) / 1000
                    }
            return {'start': None, 'end': None, 'duration_seconds': 0}
        except:
            return {'start': None, 'end': None, 'duration_seconds': 0}

    def _identify_anomaly_patterns(self, traces_data: pd.DataFrame) -> List[Dict]:
        """识别异常模式"""
        try:
            patterns = []

            # 按服务分组分析异常模式
            service_groups = traces_data.groupby(traces_data.apply(lambda x: self._extract_service_name(x), axis=1))

            for service, group in service_groups:
                if len(group) >= 2:  # 至少2个span才能形成模式
                    avg_duration = group['duration'].mean() / 1000
                    max_duration = group['duration'].max() / 1000
                    span_count = len(group)

                    # 识别异常模式
                    pattern_type = 'normal'
                    if max_duration > 30000:  # 30秒
                        pattern_type = 'extreme_latency'
                    elif avg_duration > 5000:  # 5秒
                        pattern_type = 'high_latency'
                    elif span_count > 100:
                        pattern_type = 'high_frequency'

                    if pattern_type != 'normal':
                        patterns.append({
                            'service': service,
                            'pattern_type': pattern_type,
                            'span_count': span_count,
                            'avg_duration_ms': avg_duration,
                            'max_duration_ms': max_duration
                        })

            return patterns

        except Exception as e:
            self.logger.error(f"❌ 异常模式识别失败: {e}")
            return []

    def _extract_tags_summary(self, span) -> str:
        """提取tags摘要"""
        try:
            tags = span.get('tags', [])
            if isinstance(tags, str):
                return tags[:100]  # 截取前100字符
            elif isinstance(tags, list):
                tag_strs = []
                for tag in tags[:3]:  # 最多3个tag
                    if isinstance(tag, dict):
                        key = tag.get('key', '')
                        value = tag.get('value', '')
                        tag_strs.append(f"{key}:{value}")
                return '; '.join(tag_strs)
            return 'no_tags'
        except:
            return 'error_parsing_tags'

    def _fallback_preprocessing(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """降级预处理方案"""
        try:
            duration_info = self._calculate_duration_statistics(traces_data)

            # 简单采样
            sample_size = min(20, len(traces_data))
            sampled_traces = traces_data.head(sample_size)

            samples = []
            for _, span in sampled_traces.iterrows():
                samples.append({
                    'service': self._extract_service_name(span),
                    'operation': span.get('operationName', 'unknown'),
                    'duration_ms': span.get('duration', 0) / 1000
                })

            return {
                'total_spans': len(traces_data),
                'filtered_spans': len(sampled_traces),
                'duration_statistics': duration_info,
                'sample_traces': samples,
                'fallback_mode': True
            }

        except Exception as e:
            self.logger.error(f"❌ 降级预处理也失败: {e}")
            return {'total_spans': 0, 'sample_traces': [], 'error': str(e)}

    async def _llm_comprehensive_trace_analysis(self, processed_data: Dict[str, Any], case_uuid: str) -> Dict[str, Any]:
        """优化的LLM综合trace分析 - 专注于系统性问题"""
        try:
            from autogen_ext.models.openai import OpenAIChatCompletionClient
            from autogen_core.models import SystemMessage, UserMessage
            from aiops_diagnosis.llm_config import get_llm_config

            # 获取LLM配置
            llm_config = get_llm_config()
            llm_helper = OpenAIChatCompletionClient(**llm_config)

            # 构建优化的系统提示
            system_prompt = """你是一个专业的分布式系统调用链分析专家，专门识别系统性故障模式。

**核心分析原则**：
1. **忽略单次异常**：只关注重复出现的异常模式（频次>=2）
2. **专注系统性问题**：识别影响多个服务或持续时间较长的问题
3. **根因导向**：优先分析可能的根本原因，而非表面症状
4. **业务影响评估**：评估异常对用户体验的实际影响

**异常严重程度标准**：
- **极高(extreme)**：延迟>30秒 或 影响核心业务流程
- **高(high)**：延迟>5秒 或 错误率>10%
- **中(medium)**：延迟>1秒 或 出现频繁重试
- **低(low)**：轻微性能波动

**重点关注的系统性模式**：
- 服务间调用超时模式
- 资源竞争导致的性能退化
- 错误传播链路
- 服务依赖故障

请基于以下数据进行分析，**严格忽略单次出现的异常**。"""

            # 构建用户提示
            user_content = self._build_optimized_user_prompt(processed_data, case_uuid)

            # 构建消息
            messages = [
                SystemMessage(content=system_prompt, source="system"),
                UserMessage(content=user_content, source="user")
            ]

            # 调用LLM进行分析
            response = await llm_helper.create(messages)

            if response and response.content:
                self.logger.info(f"🤖 TraceAgent LLM响应内容:")
                self.logger.info("=" * 80)
                self.logger.info(response.content)
                self.logger.info("=" * 80)

                # 解析LLM响应
                analysis_result = self._parse_optimized_llm_response(response.content)

                # 验证结果质量
                if self._validate_analysis_quality(analysis_result, processed_data):
                    # 🔧 新增：检查是否有unknown值，如果有则尝试重试
                    unknown_count = self._count_unknown_values(analysis_result)
                    if unknown_count > 0:
                        self.logger.warning(f"⚠️ 检测到 {unknown_count} 个unknown值，尝试重试改进")
                        retry_result = await self._retry_for_unknown_values(analysis_result, processed_data, max_retries=3)
                        if retry_result:
                            return retry_result
                    return analysis_result
                else:
                    self.logger.warning("⚠️ LLM分析质量不达标，使用降级分析")
                    return self._fallback_analysis(processed_data)
            else:
                self.logger.warning("⚠️ LLM响应为空，使用降级分析")
                return self._fallback_analysis(processed_data)

        except Exception as e:
            self.logger.error(f"❌ 优化LLM分析失败: {e}")
            return self._fallback_analysis(processed_data)

    def _build_optimized_user_prompt(self, processed_data: Dict[str, Any], case_uuid: str) -> str:
        """构建优化的用户提示"""
        try:
            # 提取关键信息
            total_spans = processed_data.get('total_spans', 0)
            filtered_spans = processed_data.get('filtered_spans', 0)
            duration_stats = processed_data.get('duration_statistics', {})
            service_distribution = processed_data.get('service_distribution', {})
            sample_traces = processed_data.get('sample_traces', [])
            anomaly_patterns = processed_data.get('anomaly_patterns', [])
            optimization_summary = processed_data.get('optimization_summary', {})

            # 构建提示内容
            prompt = f"""
**案例信息**：
- 案例ID: {case_uuid}
- 原始span数量: {total_spans}
- 筛选后span数量: {filtered_spans}
- 数据压缩比: {optimization_summary.get('compression_ratio', 0):.3f}

**性能统计**：
- 平均延迟: {duration_stats.get('mean_ms', 0):.1f}ms
- P99延迟: {duration_stats.get('p99_ms', 0):.1f}ms
- 最大延迟: {duration_stats.get('max_ms', 0):.1f}ms

**服务分布**：
{self._format_service_distribution(service_distribution)}

**识别的异常模式**：
{self._format_anomaly_patterns(anomaly_patterns)}

**代表性异常样本**（已按严重程度排序）：
{self._format_sample_traces(sample_traces)}

**分析要求**：
1. 基于上述**高频异常模式**进行分析，忽略单次异常
2. 识别系统性故障的根本原因
3. 评估对业务的实际影响
4. 提供可操作的修复建议

请返回JSON格式的分析结果：
```json
{{
    "anomalies": [
        {{
            "traceID": "具体的traceID",
            "spanID": "具体的spanID",
            "operationName": "操作名称",
            "service": "服务名",
            "pod_instance": "Pod实例名",
            "anomaly_type": "异常类型",
            "severity": "严重程度(extreme/high/medium/low)",
            "duration_ms": 延迟毫秒数,
            "error_details": "错误详情",
            "evidence": "支持证据",
            "impact_scope": "影响范围"
        }}
    ],
    "service_dependency_analysis": {{
        "affected_services": ["受影响的服务列表"],
        "error_propagation_path": "错误传播路径",
        "root_cause_candidates": ["根因候选列表"]
    }},
    "confidence": 0.0-1.0,
    "analysis_summary": "分析摘要"
}}
```
"""
            return prompt

        except Exception as e:
            self.logger.error(f"❌ 构建用户提示失败: {e}")
            return f"案例ID: {case_uuid}\n数据处理失败，请进行基础分析。"

    def _format_service_distribution(self, service_distribution: Dict) -> str:
        """格式化服务分布信息"""
        try:
            if not service_distribution:
                return "无服务分布数据"

            lines = []
            for service, stats in service_distribution.items():
                span_count = stats.get('span_count', 0)
                avg_duration = stats.get('avg_duration_ms', 0)
                max_duration = stats.get('max_duration_ms', 0)
                operations = stats.get('operations', [])

                lines.append(f"- {service}: {span_count}个span, 平均{avg_duration:.1f}ms, 最大{max_duration:.1f}ms, {len(operations)}个操作")

            return "\n".join(lines)
        except:
            return "服务分布格式化失败"

    def _format_anomaly_patterns(self, anomaly_patterns: List) -> str:
        """格式化异常模式信息"""
        try:
            if not anomaly_patterns:
                return "未识别到明显的异常模式"

            lines = []
            for pattern in anomaly_patterns:
                service = pattern.get('service', 'unknown')
                pattern_type = pattern.get('pattern_type', 'unknown')
                span_count = pattern.get('span_count', 0)
                avg_duration = pattern.get('avg_duration_ms', 0)
                max_duration = pattern.get('max_duration_ms', 0)

                lines.append(f"- {service}: {pattern_type}, {span_count}次出现, 平均{avg_duration:.1f}ms, 最大{max_duration:.1f}ms")

            return "\n".join(lines)
        except:
            return "异常模式格式化失败"

    def _format_sample_traces(self, sample_traces: List) -> str:
        """格式化样本traces信息"""
        try:
            if not sample_traces:
                return "无样本数据"

            lines = []
            for i, sample in enumerate(sample_traces[:10], 1):  # 最多显示10个
                trace_id = sample.get('traceID', 'unknown')
                service = sample.get('service', 'unknown')
                operation = sample.get('operation', 'unknown')
                duration = sample.get('duration_ms', 0)
                has_error = sample.get('has_error_indicators', False)

                error_flag = " [ERROR]" if has_error else ""
                lines.append(f"{i}. {service}:{operation} - {duration:.1f}ms - {trace_id}{error_flag}")

            return "\n".join(lines)
        except:
            return "样本数据格式化失败"

    def _parse_optimized_llm_response(self, llm_response: str) -> Dict[str, Any]:
        """解析优化的LLM响应"""
        try:
            import json
            import re

            # 尝试直接JSON解析
            try:
                # 提取JSON部分
                json_match = re.search(r'```json\s*(.*?)\s*```', llm_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    # 如果没有代码块，尝试找到JSON对象
                    json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                    else:
                        raise ValueError("未找到JSON格式的响应")

                analysis_result = json.loads(json_str)

                # 验证必要字段
                if 'anomalies' not in analysis_result:
                    analysis_result['anomalies'] = []
                if 'confidence' not in analysis_result:
                    analysis_result['confidence'] = 0.5
                if 'analysis_summary' not in analysis_result:
                    analysis_result['analysis_summary'] = "LLM分析完成"

                self.logger.info(f"✅ LLM响应解析成功，发现 {len(analysis_result['anomalies'])} 个异常")
                return analysis_result

            except (json.JSONDecodeError, ValueError) as e:
                self.logger.warning(f"⚠️ JSON解析失败: {e}，尝试正则提取")
                return self._extract_anomalies_from_text(llm_response)

        except Exception as e:
            self.logger.error(f"❌ LLM响应解析失败: {e}")
            return {
                'anomalies': [],
                'confidence': 0.0,
                'analysis_summary': f'解析失败: {str(e)}',
                'parse_error': True
            }

    def _extract_anomalies_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取异常信息（降级方案）"""
        try:
            import re

            anomalies = []

            # 查找traceID模式
            trace_patterns = re.findall(r'traceID["\s:]*([a-fA-F0-9]{32})', text)

            # 查找服务名模式
            service_patterns = re.findall(r'service["\s:]*([a-zA-Z0-9_-]+)', text)

            # 查找延迟模式
            duration_patterns = re.findall(r'(\d+(?:\.\d+)?)\s*ms', text)

            # 构建基础异常信息
            for i, trace_id in enumerate(trace_patterns[:5]):  # 最多5个
                service = service_patterns[i] if i < len(service_patterns) else 'unknown'
                duration = float(duration_patterns[i]) if i < len(duration_patterns) else 0

                anomaly = {
                    'traceID': trace_id,
                    'spanID': 'unknown',
                    'service': service,
                    'operationName': 'unknown',
                    'anomaly_type': 'text_extracted',
                    'severity': 'medium',
                    'duration_ms': duration,
                    'error_details': '从文本提取的异常',
                    'evidence': f'TraceID: {trace_id}, 延迟: {duration}ms',
                    'impact_scope': '需要进一步分析'
                }
                anomalies.append(anomaly)

            return {
                'anomalies': anomalies,
                'confidence': 0.3,  # 降级方案置信度较低
                'analysis_summary': f'文本提取模式，发现 {len(anomalies)} 个潜在异常',
                'extraction_mode': True
            }

        except Exception as e:
            self.logger.error(f"❌ 文本提取也失败: {e}")
            return {
                'anomalies': [],
                'confidence': 0.0,
                'analysis_summary': '所有解析方法都失败',
                'extraction_error': str(e)
            }

    def _count_unknown_values(self, analysis_result: Dict) -> int:
        """统计分析结果中的unknown值数量"""
        try:
            unknown_count = 0
            anomalies = analysis_result.get('anomalies', [])

            # 检查关键字段中的unknown值
            critical_fields = ['service', 'traceID', 'spanID', 'operationName']

            for anomaly in anomalies:
                for field in critical_fields:
                    value = anomaly.get(field, '')
                    if value in ['unknown', 'Unknown', 'UNKNOWN', '', None, 'N/A', 'n/a']:
                        unknown_count += 1
                        self.logger.debug(f"🔍 发现unknown值: {field}={value}")

            self.logger.info(f"📊 Unknown值统计: {unknown_count} 个unknown值在 {len(anomalies)} 个异常中")
            return unknown_count

        except Exception as e:
            self.logger.error(f"❌ 统计unknown值失败: {e}")
            return 0

    async def _retry_for_unknown_values(self, original_result: Dict, processed_data: Dict, max_retries: int = 3) -> Dict:
        """针对unknown值进行重试分析"""
        try:
            self.logger.info(f"🔄 开始重试分析，最大重试次数: {max_retries}")

            best_result = original_result
            best_unknown_count = self._count_unknown_values(original_result)

            for retry_count in range(1, max_retries + 1):
                self.logger.info(f"🔄 第 {retry_count} 次重试...")

                # 增强提示词，强调避免unknown值
                enhanced_processed_data = self._enhance_data_for_retry(processed_data, retry_count)

                # 重新调用LLM
                retry_result = await self._llm_comprehensive_trace_analysis_with_enhanced_prompt(
                    enhanced_processed_data, retry_count
                )

                if retry_result:
                    retry_unknown_count = self._count_unknown_values(retry_result)
                    self.logger.info(f"🔄 重试 {retry_count} 结果: {retry_unknown_count} 个unknown值 (原始: {best_unknown_count})")

                    # 如果重试结果更好，更新最佳结果
                    if retry_unknown_count < best_unknown_count:
                        best_result = retry_result
                        best_unknown_count = retry_unknown_count
                        self.logger.info(f"✅ 重试 {retry_count} 改进了结果，unknown值从 {self._count_unknown_values(original_result)} 减少到 {retry_unknown_count}")

                        # 如果已经没有unknown值，提前结束
                        if retry_unknown_count == 0:
                            self.logger.info("🎯 重试成功消除所有unknown值")
                            break
                else:
                    self.logger.warning(f"⚠️ 重试 {retry_count} 失败")

            # 如果重试后仍有unknown值，记录最终状态
            final_unknown_count = self._count_unknown_values(best_result)
            if final_unknown_count > 0:
                self.logger.warning(f"⚠️ 重试完成，仍有 {final_unknown_count} 个unknown值")
            else:
                self.logger.info("🎉 重试成功，已消除所有unknown值")

            return best_result if best_unknown_count < self._count_unknown_values(original_result) else None

        except Exception as e:
            self.logger.error(f"❌ 重试分析失败: {e}")
            return None

    def _validate_analysis_quality(self, analysis_result: Dict, processed_data: Dict) -> bool:
        """验证分析结果质量"""
        try:
            # 基础质量检查
            if not isinstance(analysis_result, dict):
                return False

            anomalies = analysis_result.get('anomalies', [])
            confidence = analysis_result.get('confidence', 0)

            # 🔧 调整质量标准 - 在并发环境下更宽松
            quality_checks = [
                len(anomalies) <= 50,   # 🔧 放宽异常数量限制（20->50）
                confidence >= 0.05,    # 🔧 降低置信度要求（0.1->0.05）
                all(isinstance(a, dict) for a in anomalies),  # 异常格式正确
                not analysis_result.get('parse_error', False)  # 没有解析错误
            ]

            # 🔧 放宽字段完整性检查 - 只检查核心字段
            field_completeness_score = 1.0
            if anomalies:
                core_fields = ['service', 'anomaly_type']  # 🔧 只检查核心字段
                complete_anomalies = 0
                for anomaly in anomalies[:10]:  # 检查前10个
                    if all(field in anomaly for field in core_fields):
                        complete_anomalies += 1
                field_completeness_score = complete_anomalies / min(len(anomalies), 10)

            # 🔧 综合质量评分 - 包含字段完整性
            basic_quality_score = sum(quality_checks) / len(quality_checks)
            overall_quality_score = (basic_quality_score * 0.7 + field_completeness_score * 0.3)

            self.logger.info(f"📊 分析质量评分: {overall_quality_score:.2f} (基础:{basic_quality_score:.2f}, 字段:{field_completeness_score:.2f})")
            return overall_quality_score >= 0.6  # 🔧 降低通过标准（0.8->0.6）

        except Exception as e:
            self.logger.error(f"❌ 质量验证失败: {e}")
            return False

    def _enhance_data_for_retry(self, processed_data: Dict, retry_count: int) -> Dict:
        """为重试增强数据，提供更多上下文信息"""
        try:
            enhanced_data = processed_data.copy()

            # 添加重试特定的指导信息
            enhanced_data['retry_guidance'] = {
                'retry_count': retry_count,
                'focus_areas': [
                    '确保所有service字段都有具体的服务名称，不能是unknown',
                    '确保所有traceID字段都是有效的32位十六进制字符串',
                    '确保所有spanID字段都是有效的16位十六进制字符串',
                    '确保所有operationName字段都有具体的操作名称'
                ],
                'data_extraction_tips': [
                    '从process.serviceName字段提取服务名称',
                    '从span的traceID字段提取调用链ID',
                    '从span的spanID字段提取span标识符',
                    '从span的operationName字段提取操作名称'
                ]
            }

            # 增加样本数据的详细程度
            sample_traces = enhanced_data.get('sample_traces', [])
            if sample_traces:
                enhanced_data['detailed_sample_count'] = min(len(sample_traces), 15 + retry_count * 5)
                enhanced_data['sample_traces'] = sample_traces[:enhanced_data['detailed_sample_count']]

            return enhanced_data

        except Exception as e:
            self.logger.error(f"❌ 增强重试数据失败: {e}")
            return processed_data

    async def _llm_comprehensive_trace_analysis_with_enhanced_prompt(self, processed_data: Dict, retry_count: int) -> Dict:
        """使用增强提示词的LLM分析（专门用于重试）"""
        try:
            from ..llm_helper import LLMHelper

            llm_helper = LLMHelper()

            # 构建增强的系统提示词
            enhanced_system_prompt = self._build_enhanced_system_prompt_for_retry(retry_count)

            # 构建增强的用户提示词
            enhanced_user_prompt = self._build_enhanced_user_prompt_for_retry(processed_data, retry_count)

            messages = [
                {"role": "system", "content": enhanced_system_prompt},
                {"role": "user", "content": enhanced_user_prompt}
            ]

            self.logger.info(f"🔄 重试 {retry_count}: 使用增强提示词调用LLM")

            # 调用LLM
            response = await llm_helper.create(messages)

            if response and response.content:
                self.logger.info(f"🔄 重试 {retry_count}: LLM响应成功")

                # 解析响应
                analysis_result = self._parse_optimized_llm_response(response.content)
                return analysis_result
            else:
                self.logger.warning(f"⚠️ 重试 {retry_count}: LLM响应为空")
                return None

        except Exception as e:
            self.logger.error(f"❌ 重试 {retry_count} LLM分析失败: {e}")
            return None

    def _build_enhanced_system_prompt_for_retry(self, retry_count: int) -> str:
        """构建重试专用的增强系统提示词"""
        return f"""你是一个专业的分布式系统调用链分析专家。这是第 {retry_count} 次重试分析。

🚨 **重试重点要求**：
1. **绝对禁止输出unknown值**：所有字段必须有具体的值
2. **数据提取准确性**：仔细从原始数据中提取真实的字段值
3. **字段完整性**：确保每个异常都包含完整的标识信息

📋 **必须字段要求**：
- **service**: 从process.serviceName提取，如"frontend", "cartservice", "paymentservice"
- **traceID**: 32位十六进制字符串，如"1a2b3c4d5e6f7890abcdef1234567890"
- **spanID**: 16位十六进制字符串，如"1a2b3c4d5e6f7890"
- **operationName**: 具体操作名，如"hipstershop.CartService/AddItem"

🔍 **数据提取策略**：
1. 优先从原始span数据的直接字段提取
2. 如果字段缺失，从process或tags中查找
3. 如果仍然缺失，基于上下文推断合理值
4. 绝不使用"unknown"、"N/A"等占位符

⚠️ **质量检查**：
- 每个异常必须有有效的service名称
- 每个异常必须有有效的traceID和spanID
- 所有ID必须符合十六进制格式要求

请严格按照JSON格式输出，确保所有字段都有具体的值。"""

    def _build_enhanced_user_prompt_for_retry(self, processed_data: Dict, retry_count: int) -> str:
        """构建重试专用的增强用户提示词"""
        try:
            sample_traces = processed_data.get('sample_traces', [])
            duration_stats = processed_data.get('duration_statistics', {})
            service_stats = processed_data.get('service_statistics', {})
            retry_guidance = processed_data.get('retry_guidance', {})

            prompt = f"""🔄 **第 {retry_count} 次重试分析** - 请特别注意避免unknown值

📊 **数据概览**：
- 样本数量: {len(sample_traces)}
- P95延迟: {duration_stats.get('p95_ms', 0):.1f}ms
- P99延迟: {duration_stats.get('p99_ms', 0):.1f}ms
- 涉及服务: {list(service_stats.keys())[:10]}

🎯 **重试重点**：
{chr(10).join(f"- {tip}" for tip in retry_guidance.get('focus_areas', []))}

📋 **数据提取指导**：
{chr(10).join(f"- {tip}" for tip in retry_guidance.get('data_extraction_tips', []))}

📈 **详细样本数据**：
"""

            # 添加更详细的样本数据
            for i, sample in enumerate(sample_traces[:10], 1):
                trace_id = sample.get('traceID', 'MISSING_TRACE_ID')
                span_id = sample.get('spanID', 'MISSING_SPAN_ID')
                service = sample.get('service', 'MISSING_SERVICE')
                operation = sample.get('operation', 'MISSING_OPERATION')
                duration = sample.get('duration_ms', 0)

                prompt += f"""
样本 {i}:
  - TraceID: {trace_id}
  - SpanID: {span_id}
  - Service: {service}
  - Operation: {operation}
  - Duration: {duration:.1f}ms"""

            prompt += f"""

🚨 **输出要求**：
请分析以上数据，识别异常的调用链span，并严格按照以下JSON格式输出：

```json
{{
  "anomalies": [
    {{
      "traceID": "具体的32位十六进制ID，不能是unknown",
      "spanID": "具体的16位十六进制ID，不能是unknown",
      "service": "具体的服务名称，不能是unknown",
      "operationName": "具体的操作名称，不能是unknown",
      "anomaly_type": "latency_anomaly|error_anomaly|timeout_anomaly",
      "severity": "critical|high|medium",
      "duration_ms": 具体数值,
      "error_details": "具体的错误描述"
    }}
  ],
  "confidence": 0.8,
  "analysis_summary": "分析摘要"
}}
```

⚠️ **再次强调**：输出中不能包含任何"unknown"、"Unknown"、"UNKNOWN"、"N/A"等值！"""

            return prompt

        except Exception as e:
            self.logger.error(f"❌ 构建重试用户提示词失败: {e}")
            return "请分析调用链数据并避免unknown值"

    def _fallback_analysis(self, processed_data: Dict) -> Dict[str, Any]:
        """降级分析方案 - 增强版，确保包含所有关键服务"""
        try:
            sample_traces = processed_data.get('sample_traces', [])
            duration_stats = processed_data.get('duration_statistics', {})

            # 基于统计阈值生成异常
            anomalies = []
            p99_threshold = duration_stats.get('p99_ms', 1000)

            # 🔧 通用修复：基于数据动态发现所有服务，而不是硬编码
            service_anomalies = {}

            # 按服务分组所有样本
            for sample in sample_traces:
                service = sample.get('service', 'unknown')
                if service not in service_anomalies:
                    service_anomalies[service] = []
                service_anomalies[service].append(sample)

            # 🔧 通用策略：对所有发现的服务进行分析，基于延迟阈值判断
            severe_latency_threshold = 1000  # 1秒以上认为是严重延迟（降低阈值）

            for service, service_samples in service_anomalies.items():
                if service == 'unknown':
                    continue

                # 找到该服务的最高延迟span
                max_duration_sample = max(service_samples, key=lambda x: x.get('duration_ms', 0))
                max_duration = max_duration_sample.get('duration_ms', 0)

                # 通用判断条件：延迟超过P99阈值或严重延迟阈值
                if max_duration > p99_threshold or max_duration > severe_latency_threshold:

                    # 根据延迟程度确定严重性（调整阈值）
                    if max_duration > 10000:  # 10秒以上
                        severity = 'extreme'
                    elif max_duration > severe_latency_threshold:  # 1-10秒
                        severity = 'high'
                    else:  # P99以上但小于1秒
                        severity = 'medium'

                    anomaly = {
                        'traceID': max_duration_sample.get('traceID', 'unknown'),
                        'spanID': max_duration_sample.get('spanID', 'unknown'),
                        'service': service,
                        'operationName': max_duration_sample.get('operation', 'unknown'),
                        'anomaly_type': 'statistical_latency_anomaly',
                        'severity': severity,
                        'duration_ms': max_duration,
                        'error_details': f'服务延迟异常: {max_duration:.1f}ms (P99: {p99_threshold:.1f}ms)',
                        'evidence': f'降级分析检测到延迟超过阈值 (样本数: {len(service_samples)})',
                        'impact_scope': f'{service}服务性能影响'
                    }
                    anomalies.append(anomaly)
                    self.logger.info(f"🔧 降级分析发现服务异常: {service} ({max_duration:.1f}ms, 严重性: {severity})")

            # 如果没有发现服务异常，使用原来的逻辑
            if not anomalies:
                for sample in sample_traces[:10]:  # 最多10个
                    if sample.get('duration_ms', 0) > p99_threshold:
                        anomaly = {
                            'traceID': sample.get('traceID', 'unknown'),
                            'spanID': sample.get('spanID', 'unknown'),
                            'service': sample.get('service', 'unknown'),
                            'operationName': sample.get('operation', 'unknown'),
                            'anomaly_type': 'statistical_anomaly',
                            'severity': 'medium',
                            'duration_ms': sample.get('duration_ms', 0),
                            'error_details': f'延迟超过P99阈值({p99_threshold:.1f}ms)',
                            'evidence': f'统计异常检测',
                            'impact_scope': '性能影响'
                        }
                        anomalies.append(anomaly)

            return {
                'anomalies': anomalies,
                'confidence': 0.6,
                'analysis_summary': f'降级分析完成，基于统计阈值发现 {len(anomalies)} 个异常',
                'fallback_mode': True,
                'service_dependency_analysis': {
                    'affected_services': list(set(a['service'] for a in anomalies)),
                    'error_propagation_path': '需要进一步分析',
                    'root_cause_candidates': ['性能瓶颈', '资源竞争']
                }
            }

        except Exception as e:
            self.logger.error(f"❌ 降级分析失败: {e}")
            return {
                'anomalies': [],
                'confidence': 0.0,
                'analysis_summary': '所有分析方法都失败',
                'error': str(e)
            }

    def _build_standardized_output_from_batch_results(self, batch_results: List[Dict[str, Any]],
                                                    case_uuid: str, start_time: str, end_time: str,
                                                    all_anomalies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """🔧 从批次分析结果构建标准化输出 - 包含完整调用链上下文"""
        try:
            self.logger.info(f"🔧 构建标准化输出: {len(all_anomalies)} 个LLM检测的异常")

            # 调试：打印前几个异常的详细信息
            if all_anomalies:
                self.logger.info(f"📊 异常样本: {all_anomalies[0].keys()}")
                for i, anomaly in enumerate(all_anomalies[:3]):
                    service = anomaly.get("service", "unknown")
                    operation = anomaly.get("operationName", "unknown")
                    duration = anomaly.get("duration", 0) / 1000
                    self.logger.info(f"   异常{i+1}: {service} | {operation} | {duration:.1f}ms")
            else:
                self.logger.warning("⚠️ all_anomalies为空，无法构建标准化输出")
                return self._build_empty_standardized_output(case_uuid, start_time, end_time)

            suspicious_entities = []
            raw_data_sample = []

            # 按服务分组异常，同时保留调用链信息
            service_anomalies = {}
            trace_call_chains = {}  # 保存每个trace的调用链信息

            for anomaly in all_anomalies:
                service = anomaly.get("service", "unknown")
                trace_id = anomaly.get("traceID", "unknown")

                if service == "unknown":
                    self.logger.debug(f"跳过未知服务的异常: {anomaly}")
                    continue

                if service not in service_anomalies:
                    service_anomalies[service] = []
                service_anomalies[service].append(anomaly)

                # 保存trace的调用链信息
                if trace_id != "unknown" and trace_id not in trace_call_chains:
                    trace_call_chains[trace_id] = self._extract_call_chain_from_anomaly(anomaly)

            self.logger.info(f"📊 服务异常分组: {list(service_anomalies.keys())}")
            self.logger.info(f"📊 调用链上下文: {len(trace_call_chains)} 个trace")

            # 为每个服务创建可疑实体，包含调用链上下文
            for service, anomaly_list in service_anomalies.items():
                try:
                    # 计算服务级别的统计信息
                    durations = [a.get("duration_ms", 0) for a in anomaly_list]
                    avg_duration = sum(durations) / len(durations) if durations else 0
                    max_duration = max(durations) if durations else 0

                    # 提取调用链上下文
                    call_chain_context = self._build_call_chain_context(service, anomaly_list, trace_call_chains)

                    # 创建可疑实体 - 包含完整的span级别数据
                    suspicious_entity = {
                        "entity": {
                            "service": service,
                            "namespace": "hipstershop",
                            "operation": anomaly_list[0].get("operationName", "unknown")
                        },
                        "time_range": {
                            "start": start_time,
                            "end": end_time
                        },
                        "anomaly_feature": {
                            "latency": max_duration,
                            "normal_latency": 100,  # 假设正常延迟100ms
                            "pattern": anomaly_list[0].get("anomaly_type", "unknown"),
                            "source_service": service,
                            "target_service": call_chain_context.get("primary_target", "unknown")
                        },
                        "call_chain_context": call_chain_context,  # 调用链上下文
                        "detailed_spans": self._build_detailed_spans_data(anomaly_list),  # 新增：详细span数据
                        "raw_data_evidence": self._build_detailed_spans_data(anomaly_list),  # 🔧 修复：为ReasoningAgent提供原始数据
                        "trace_analysis": self._build_trace_analysis_summary(anomaly_list, call_chain_context),  # 新增：trace分析摘要
                        "data_location": {
                            "file_type": "trace",
                            "root_dir": "/data/phaseone",
                            "file_paths": [],
                            "file_matching_rule": "trace_*.parquet"
                        },
                        "confidence": 0.8
                    }

                    suspicious_entities.append(suspicious_entity)

                    # 添加原始数据样本
                    sample = f"TRACE_ANOMALY: {service} | AVG_LATENCY: {avg_duration:.1f}ms | MAX_LATENCY: {max_duration:.1f}ms | COUNT: {len(anomaly_list)}"
                    raw_data_sample.append(sample)

                    self.logger.info(f"✅ 创建可疑实体: {service} (平均延迟: {avg_duration:.1f}ms, 最大延迟: {max_duration:.1f}ms)")

                except Exception as e:
                    self.logger.error(f"❌ 创建可疑实体失败: {service} - {e}")
                    continue

            return {
                "agent_type": "enhanced_trace_agent",
                "analysis_id": f"enhanced_trace_{case_uuid}_{int(datetime.now().timestamp())}",
                "suspicious_entities": suspicious_entities,
                "raw_data_sample": raw_data_sample[:5],  # 最多5个样本
                "analysis_metadata": {
                    "total_spans": len(all_anomalies),
                    "detection_method": "enhanced_llm_analysis",
                    "time_range": {"start": start_time, "end": end_time},
                    "status": "anomalies_detected" if suspicious_entities else "no_anomalies_detected",
                    "services_affected": len(service_anomalies)
                }
            }

        except Exception as e:
            self.logger.error(f"❌ 构建标准化输出失败: {e}")
            return self._build_empty_standardized_output(case_uuid, start_time, end_time)

    def _build_empty_standardized_output(self, case_uuid: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """构建空的标准化输出"""
        return {
            "agent_type": "enhanced_trace_agent",
            "analysis_id": f"enhanced_trace_{case_uuid}_{int(datetime.now().timestamp())}",
            "suspicious_entities": [],
            "raw_data_sample": [],
            "analysis_metadata": {
                "total_spans": 0,
                "detection_method": "enhanced_llm_analysis",
                "time_range": {"start": start_time, "end": end_time},
                "status": "no_anomalies_detected"
            }
        }

    async def _retrieve_complete_trace_chains_enhanced(self, anomalies: List[Dict], anomaly_traces: pd.DataFrame, case_uuid: str, start_time: str = None, end_time: str = None) -> Dict[str, List[Dict]]:
        """根据异常分析结果检索完整的调用链数据"""
        try:
            # 1. 提取异常traceID
            trace_ids = set()
            for anomaly in anomalies:
                trace_id = anomaly.get('traceID')
                if trace_id and trace_id != 'unknown':
                    trace_ids.add(trace_id)

            if not trace_ids:
                self.logger.warning("⚠️ 未找到有效的异常traceID")
                return {}

            self.logger.info(f"🔍 开始检索 {len(trace_ids)} 个异常traceID的完整调用链")

            # 2. 从DataLoader获取完整的trace数据
            complete_traces = {}
            traces_data = await self._load_trace_data(case_uuid, start_time, end_time)  # 使用正确的时间参数

            if traces_data.empty:
                self.logger.warning("⚠️ 无法获取完整trace数据进行调用链检索")
                return {}

            # 3. 为每个异常traceID检索完整调用链
            for trace_id in trace_ids:
                trace_spans = traces_data[traces_data['traceID'] == trace_id]

                if not trace_spans.empty:
                    # 按时间排序span
                    if 'startTime' in trace_spans.columns:
                        trace_spans = trace_spans.sort_values('startTime')
                    elif 'startTimeMillis' in trace_spans.columns:
                        trace_spans = trace_spans.sort_values('startTimeMillis')

                    spans_data = trace_spans.to_dict('records')
                    complete_traces[trace_id] = spans_data

                    self.logger.info(f"🔗 traceID {trace_id}: 检索到 {len(spans_data)} 个span")
                else:
                    self.logger.warning(f"⚠️ traceID {trace_id}: 未找到对应的span数据")

            # 4. 构建调用链上下文信息
            if complete_traces:
                self.logger.info(f"✅ 成功检索到 {len(complete_traces)} 个完整调用链")

                # 添加调用链分析元数据
                for trace_id, spans in complete_traces.items():
                    complete_traces[trace_id] = {
                        'spans': spans,
                        'span_count': len(spans),
                        'services': list(set(self._extract_service_name(span) for span in spans)),
                        'duration_range': {
                            'min_ms': min(span.get('duration', 0) / MICROSECONDS_TO_MILLISECONDS for span in spans),
                            'max_ms': max(span.get('duration', 0) / MICROSECONDS_TO_MILLISECONDS for span in spans)
                        },
                        'time_range': self._extract_trace_time_range(spans)
                    }

            return complete_traces

        except Exception as e:
            self.logger.error(f"❌ 完整调用链检索失败: {e}")
            return {}

    def _extract_trace_time_range(self, spans: List[Dict]) -> Dict[str, Any]:
        """提取trace的时间范围"""
        try:
            start_times = []
            end_times = []

            for span in spans:
                start_time = span.get('startTime') or span.get('startTimeMillis', 0)
                duration = span.get('duration', 0)

                if start_time:
                    start_times.append(start_time)
                    # 计算结束时间（startTime + duration）
                    if isinstance(start_time, (int, float)) and duration:
                        end_times.append(start_time + duration / 1000)  # duration是微秒，转换为毫秒

            if start_times and end_times:
                return {
                    'start_time': min(start_times),
                    'end_time': max(end_times),
                    'total_duration_ms': max(end_times) - min(start_times)
                }
            else:
                return {'start_time': 0, 'end_time': 0, 'total_duration_ms': 0}

        except Exception as e:
            self.logger.error(f"提取trace时间范围失败: {e}")
            return {'start_time': 0, 'end_time': 0, 'total_duration_ms': 0}

    def _enhanced_fault_detection(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """增强的故障检测 - 基于gRPC状态码、references关系和logs事件"""
        try:
            fault_conditions = []

            # 条件1: gRPC状态码异常检测
            grpc_errors = self._detect_grpc_status_errors(traces_data)
            if not grpc_errors.empty:
                fault_conditions.append(grpc_errors.index)
                self.logger.info(f"🚨 检测到 {len(grpc_errors)} 个gRPC状态码异常")

            # 条件2: 调用链父子关系异常检测
            reference_errors = self._detect_reference_chain_errors(traces_data)
            if not reference_errors.empty:
                fault_conditions.append(reference_errors.index)
                self.logger.info(f"🔗 检测到 {len(reference_errors)} 个调用链关系异常")

            # 条件3: logs事件异常检测
            log_event_errors = self._detect_log_event_errors(traces_data)
            if not log_event_errors.empty:
                fault_conditions.append(log_event_errors.index)
                self.logger.info(f"📝 检测到 {len(log_event_errors)} 个日志事件异常")

            # 合并所有异常条件
            if fault_conditions:
                all_fault_indices = set()
                for condition in fault_conditions:
                    all_fault_indices.update(condition)

                enhanced_faults = traces_data.loc[list(all_fault_indices)]
                self.logger.info(f"✅ 增强故障检测完成: {len(enhanced_faults)} 个异常span")
                return enhanced_faults
            else:
                self.logger.info("ℹ️ 增强故障检测未发现异常")
                return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ 增强故障检测失败: {e}")
            return pd.DataFrame()

    def _detect_grpc_status_errors(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """检测gRPC状态码异常"""
        try:
            error_spans = []

            for idx, span in traces_data.iterrows():
                tags = span.get('tags', [])
                if isinstance(tags, list):
                    for tag in tags:
                        if isinstance(tag, dict) and tag.get('key') == 'rpc.grpc.status_code':
                            status_code = tag.get('value', 0)
                            if status_code != 0:  # 非0状态码表示错误
                                error_spans.append(idx)
                                self.logger.debug(f"🚨 gRPC错误: {span.get('operationName')} 状态码={status_code}")
                                break

            return traces_data.loc[error_spans] if error_spans else pd.DataFrame()

        except Exception as e:
            self.logger.debug(f"gRPC状态码检测失败: {e}")
            return pd.DataFrame()

    def _detect_reference_chain_errors(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """检测调用链父子关系异常"""
        try:
            error_spans = []

            # 构建span关系图
            span_map = {}
            for idx, span in traces_data.iterrows():
                span_id = span.get('spanID')
                if span_id:
                    span_map[span_id] = {
                        'index': idx,
                        'span': span,
                        'children': [],
                        'parent': None
                    }

            # 建立父子关系
            for span_id, span_info in span_map.items():
                references = span_info['span'].get('references', [])
                if isinstance(references, list):
                    for ref in references:
                        if isinstance(ref, dict) and ref.get('refType') == 'CHILD_OF':
                            parent_span_id = ref.get('spanID')
                            if parent_span_id in span_map:
                                span_map[parent_span_id]['children'].append(span_id)
                                span_info['parent'] = parent_span_id

            # 检测异常模式
            for span_id, span_info in span_map.items():
                span = span_info['span']
                duration = span.get('duration', 0)

                # 检测父span正常但子span异常的情况
                if span_info['children']:
                    child_durations = []
                    for child_id in span_info['children']:
                        if child_id in span_map:
                            child_duration = span_map[child_id]['span'].get('duration', 0)
                            child_durations.append(child_duration)

                    if child_durations:
                        max_child_duration = max(child_durations)
                        # 如果子span的最大延迟远超父span，可能存在异常
                        if max_child_duration > duration * 1.5 and max_child_duration > 100000:  # 100ms
                            error_spans.append(span_info['index'])
                            self.logger.debug(f"🔗 调用链异常: {span.get('operationName')} 子span延迟异常")

            return traces_data.loc[error_spans] if error_spans else pd.DataFrame()

        except Exception as e:
            self.logger.debug(f"调用链关系检测失败: {e}")
            return pd.DataFrame()

    def _detect_log_event_errors(self, traces_data: pd.DataFrame) -> pd.DataFrame:
        """检测logs事件异常"""
        try:
            error_spans = []

            for idx, span in traces_data.iterrows():
                logs = span.get('logs', [])
                if isinstance(logs, list):
                    for log_entry in logs:
                        if isinstance(log_entry, dict):
                            fields = log_entry.get('fields', [])
                            for field in fields:
                                if isinstance(field, dict):
                                    # 检测错误相关的事件
                                    if field.get('key') == 'message.event':
                                        event_value = field.get('value', '').lower()
                                        if any(error_keyword in event_value for error_keyword in
                                              ['error', 'exception', 'timeout', 'failed', 'abort']):
                                            error_spans.append(idx)
                                            self.logger.debug(f"📝 日志事件异常: {span.get('operationName')} 事件={event_value}")
                                            break
                                    # 检测错误消息
                                    elif field.get('key') in ['error', 'error.object', 'message']:
                                        error_spans.append(idx)
                                        self.logger.debug(f"📝 日志错误消息: {span.get('operationName')}")
                                        break

            return traces_data.loc[error_spans] if error_spans else pd.DataFrame()

        except Exception as e:
            self.logger.debug(f"日志事件检测失败: {e}")
            return pd.DataFrame()

    def _extract_problematic_segments_from_traces(self, traces_data: pd.DataFrame) -> List[Dict]:
        """从traces中提取有问题的段落 - 基于完整trace重构"""
        try:
            if traces_data.empty:
                return []

            # 计算duration统计信息用于问题检测
            duration_info = self._calculate_duration_statistics(traces_data)
            self.duration_p95 = duration_info.get('p95_ms', 100)  # 默认100ms
            self.duration_p99 = duration_info.get('p99_ms', 200)  # 默认200ms

            # 显示更详细的统计信息
            self.logger.info(f"📊 问题检测阈值: P95={self.duration_p95:.1f}ms, P99={self.duration_p99:.1f}ms")
            self.logger.info(f"📊 Duration统计: min={duration_info.get('min_ms', 0):.1f}ms, max={duration_info.get('max_ms', 0):.1f}ms, mean={duration_info.get('mean_ms', 0):.1f}ms")

            all_problem_segments = []

            # 按traceID分组处理
            for trace_id, trace_spans in traces_data.groupby('traceID'):

                # 1. 快速判断这个trace是否有问题
                has_problems = self._has_any_problems_in_trace(trace_spans)
                self.logger.debug(f"🔍 TraceID {trace_id}: 包含 {len(trace_spans)} 个span, 有问题: {has_problems}")

                if has_problems:
                    # 2. 提取问题段落
                    problem_segments = self._extract_problematic_segments_from_trace(trace_id, trace_spans)
                    self.logger.debug(f"🔍 TraceID {trace_id}: 提取了 {len(problem_segments)} 个问题段落")

                    # 3. 收集证据
                    all_problem_segments.extend(problem_segments)

            self.logger.info(f"🔍 从 {len(traces_data.groupby('traceID'))} 个trace中提取了 {len(all_problem_segments)} 个问题段落")

            return all_problem_segments

        except Exception as e:
            self.logger.error(f"❌ 问题段提取失败: {e}")
            return []

    def _has_any_problems_in_trace(self, trace_spans: pd.DataFrame) -> bool:
        """快速判断trace是否有问题"""
        try:
            problem_details = []

            for _, span in trace_spans.iterrows():
                span_problems = []

                # 1. 检查gRPC错误
                grpc_status = self._extract_grpc_status_code(span)
                if grpc_status != 0:
                    span_problems.append(f"gRPC错误:{grpc_status}")

                # 2. 检查性能异常 (duration原始单位是微秒)
                duration_us = span.get('duration', 0)  # 微秒
                duration_ms = duration_us / 1000  # 转换为毫秒用于显示

                # 使用P90作为阈值，或者如果P95和P99太接近，使用P95的90%
                # 注意：self.duration_p95是毫秒，需要转换为微秒进行比较
                p95_threshold_ms = self.duration_p95
                if abs(self.duration_p99 - self.duration_p95) < 5:  # 如果P95和P99差距小于5ms
                    p95_threshold_ms = self.duration_p95 * 0.9  # 使用P95的90%作为阈值

                p95_threshold_us = p95_threshold_ms * 1000  # 转换为微秒进行比较
                if duration_us >= p95_threshold_us:  # 使用微秒进行比较
                    span_problems.append(f"高延迟:{duration_ms:.1f}ms>={p95_threshold_ms:.1f}ms")

                # 3. 检查错误日志
                if self._has_error_logs(span):
                    span_problems.append("错误日志")

                if span_problems:
                    service = self._extract_service_name(span)
                    operation = span.get('operationName', 'unknown')
                    problem_details.append(f"{service}:{operation} - {','.join(span_problems)}")

            if problem_details:
                self.logger.debug(f"🚨 发现问题: {'; '.join(problem_details)}")
                return True
            else:
                self.logger.debug(f"✅ 未发现明显问题")
                return False

        except Exception as e:
            self.logger.debug(f"trace问题检查失败: {e}")
            return True  # 保守策略，有疑问就保留

    def _extract_problematic_segments_from_trace(self, trace_id: str, spans: pd.DataFrame) -> List[Dict]:
        """从单个trace中提取有问题的段落"""
        try:
            # 1. 按时间排序构建调用链
            sorted_spans = spans.sort_values('startTimeMillis')
            call_chain = self._build_call_chain_from_references(sorted_spans)

            # 2. 标记每个span的问题状态
            span_problems = []
            for _, span in sorted_spans.iterrows():
                problem_indicators = self._analyze_span_problems(span)
                span_problems.append({
                    'span': span,
                    'has_problems': len(problem_indicators) > 0,
                    'problem_types': problem_indicators,
                    'severity': self._calculate_problem_severity(problem_indicators)
                })

            # 3. 识别问题段落
            problem_segments = self._identify_problem_segments(span_problems, call_chain)

            return problem_segments

        except Exception as e:
            self.logger.error(f"❌ trace段落提取失败: {e}")
            return []

    def _analyze_span_problems(self, span) -> List[str]:
        """分析单个span的问题指标"""
        problems = []

        try:
            # 1. gRPC错误
            grpc_status = self._extract_grpc_status_code(span)
            if grpc_status != 0:
                problems.append(f'grpc_error_{grpc_status}')

            # 2. 性能异常 (使用调整后的阈值)
            duration_ms = span.get('duration', 0) / 1000

            # 动态调整阈值
            p95_threshold = self.duration_p95
            if abs(self.duration_p99 - self.duration_p95) < 5:  # P95和P99太接近
                p95_threshold = self.duration_p95 * 0.9

            if duration_ms >= self.duration_p99:  # 使用>=
                problems.append('severe_latency')
            elif duration_ms >= p95_threshold:  # 使用>=和调整后的阈值
                problems.append('high_latency')

            # 3. 日志错误事件
            if self._has_error_logs(span):
                problems.append('error_logs')

            # 4. HTTP错误状态码
            http_status = self._extract_http_status_code(span)
            if http_status >= 500:
                problems.append(f'http_5xx_{http_status}')
            elif http_status >= 400:
                problems.append(f'http_4xx_{http_status}')

        except Exception as e:
            self.logger.debug(f"span问题分析失败: {e}")

        return problems

    def _calculate_problem_severity(self, problem_indicators: List[str]) -> float:
        """计算问题严重程度"""
        if not problem_indicators:
            return 0.0

        severity_weights = {
            'grpc_error': 0.9,
            'http_5xx': 0.8,
            'severe_latency': 0.7,
            'error_logs': 0.6,
            'http_4xx': 0.5,
            'high_latency': 0.4
        }

        max_severity = 0.0
        for indicator in problem_indicators:
            for pattern, weight in severity_weights.items():
                if pattern in indicator:
                    max_severity = max(max_severity, weight)
                    break

        return max_severity

    def _extract_http_status_code(self, span) -> int:
        """从span的tags中提取HTTP状态码"""
        try:
            tags = span.get('tags', [])
            if isinstance(tags, list):
                for tag in tags:
                    if isinstance(tag, dict) and tag.get('key') == 'http.status_code':
                        return int(tag.get('value', 200))
            return 200
        except:
            return 200

    def _has_error_logs(self, span) -> bool:
        """检查span是否有错误日志"""
        try:
            logs = span.get('logs', [])
            if isinstance(logs, list):
                for log_entry in logs:
                    if isinstance(log_entry, dict):
                        fields = log_entry.get('fields', [])
                        for field in fields:
                            if isinstance(field, dict):
                                if field.get('key') == 'error' or 'error' in field.get('value', '').lower():
                                    return True
            return False
        except:
            return False

    def _build_call_chain_from_references(self, sorted_spans: pd.DataFrame) -> Dict:
        """从references构建调用链结构"""
        try:
            call_chain = {}

            # 构建span映射
            span_map = {}
            for _, span in sorted_spans.iterrows():
                span_id = span.get('spanID')
                if span_id:
                    span_map[span_id] = {
                        'span': span,
                        'children': [],
                        'parent': None
                    }

            # 建立父子关系
            for span_id, span_info in span_map.items():
                references = span_info['span'].get('references', [])
                if isinstance(references, list):
                    for ref in references:
                        if isinstance(ref, dict) and ref.get('refType') == 'CHILD_OF':
                            parent_span_id = ref.get('spanID')
                            if parent_span_id in span_map:
                                span_map[parent_span_id]['children'].append(span_id)
                                span_info['parent'] = parent_span_id

            return span_map

        except Exception as e:
            self.logger.debug(f"调用链构建失败: {e}")
            return {}

    def _identify_problem_segments(self, span_problems: List[Dict], call_chain: Dict) -> List[Dict]:
        """识别问题段落 - 包含上下文的最小问题单元"""
        try:
            segments = []

            # 找到所有有问题的span
            problem_spans = [sp for sp in span_problems if sp['has_problems']]

            for problem_span in problem_spans:
                # 为每个问题span构建包含上下文的段落
                segment = self._build_problem_segment(problem_span, span_problems, call_chain)
                if segment:
                    segments.append(segment)

            # 合并重叠的段落
            merged_segments = self._merge_overlapping_segments(segments)

            return merged_segments

        except Exception as e:
            self.logger.error(f"❌ 问题段识别失败: {e}")
            return []

    def _build_problem_segment(self, problem_span: Dict, all_spans: List[Dict], call_chain: Dict) -> Dict:
        """为问题span构建包含上下文的段落"""
        try:
            problem_span_id = problem_span['span']['spanID']

            # 1. 找到直接相关的span（父span + 子span）
            related_spans = [problem_span]  # 包含问题span本身

            # 2. 添加父span（调用来源）
            parent_span = self._find_parent_span(problem_span_id, all_spans, call_chain)
            if parent_span:
                related_spans.append(parent_span)

            # 3. 添加直接子span（被调用的服务）
            child_spans = self._find_direct_child_spans(problem_span_id, all_spans, call_chain)
            related_spans.extend(child_spans)

            # 4. 按时间排序
            related_spans.sort(key=lambda x: x['span']['startTimeMillis'])

            segment = {
                'segment_id': f"{problem_span['span']['traceID']}_{problem_span_id}",
                'problem_span': problem_span,
                'context_spans': related_spans,
                'segment_summary': {
                    'total_spans': len(related_spans),
                    'problem_types': problem_span['problem_types'],
                    'severity': problem_span['severity'],
                    'services_involved': list(set(self._extract_service_name(sp['span']) for sp in related_spans)),
                    'duration_range': {
                        'start': min(sp['span']['startTimeMillis'] for sp in related_spans),
                        'end': max(sp['span']['startTimeMillis'] + sp['span']['duration']/1000 for sp in related_spans)
                    }
                }
            }

            return segment

        except Exception as e:
            self.logger.debug(f"问题段构建失败: {e}")
            return None

    def _find_parent_span(self, span_id: str, all_spans: List[Dict], call_chain: Dict) -> Dict:
        """找到指定span的父span"""
        try:
            target_span = next((sp for sp in all_spans if sp['span']['spanID'] == span_id), None)
            if not target_span:
                return None

            # 从references中找父span
            references = target_span['span'].get('references', [])
            if isinstance(references, list):
                for ref in references:
                    if isinstance(ref, dict) and ref.get('refType') == 'CHILD_OF':
                        parent_span_id = ref.get('spanID')
                        parent_span = next((sp for sp in all_spans if sp['span']['spanID'] == parent_span_id), None)
                        return parent_span

            return None

        except Exception as e:
            self.logger.debug(f"查找父span失败: {e}")
            return None

    def _find_direct_child_spans(self, span_id: str, all_spans: List[Dict], call_chain: Dict) -> List[Dict]:
        """找到指定span的直接子span"""
        try:
            child_spans = []

            for span_data in all_spans:
                references = span_data['span'].get('references', [])
                if isinstance(references, list):
                    for ref in references:
                        if isinstance(ref, dict) and ref.get('refType') == 'CHILD_OF' and ref.get('spanID') == span_id:
                            child_spans.append(span_data)
                            break

            return child_spans

        except Exception as e:
            self.logger.debug(f"查找子span失败: {e}")
            return []

    def _merge_overlapping_segments(self, segments: List[Dict]) -> List[Dict]:
        """合并重叠的段落"""
        try:
            if len(segments) <= 1:
                return segments

            # 按时间排序
            segments.sort(key=lambda x: x['segment_summary']['duration_range']['start'])

            merged = []
            current_segment = segments[0]

            for next_segment in segments[1:]:
                # 检查是否有重叠的span
                current_span_ids = set(sp['span']['spanID'] for sp in current_segment['context_spans'])
                next_span_ids = set(sp['span']['spanID'] for sp in next_segment['context_spans'])

                if current_span_ids & next_span_ids:  # 有重叠
                    # 合并段落
                    current_segment = self._merge_two_segments(current_segment, next_segment)
                else:
                    # 无重叠，保存当前段落，开始新段落
                    merged.append(current_segment)
                    current_segment = next_segment

            merged.append(current_segment)
            return merged

        except Exception as e:
            self.logger.error(f"❌ 段落合并失败: {e}")
            return segments

    def _merge_two_segments(self, segment1: Dict, segment2: Dict) -> Dict:
        """合并两个段落"""
        try:
            # 合并context_spans并去重
            all_spans = segment1['context_spans'] + segment2['context_spans']
            unique_spans = []
            seen_span_ids = set()

            for span_data in all_spans:
                span_id = span_data['span']['spanID']
                if span_id not in seen_span_ids:
                    unique_spans.append(span_data)
                    seen_span_ids.add(span_id)

            # 按时间排序
            unique_spans.sort(key=lambda x: x['span']['startTimeMillis'])

            # 合并问题类型
            combined_problems = list(set(
                segment1['problem_span']['problem_types'] +
                segment2['problem_span']['problem_types']
            ))

            # 选择严重程度更高的问题span
            primary_problem_span = (segment1['problem_span']
                                  if segment1['problem_span']['severity'] >= segment2['problem_span']['severity']
                                  else segment2['problem_span'])

            merged_segment = {
                'segment_id': f"merged_{segment1['segment_id']}_{segment2['segment_id']}",
                'problem_span': primary_problem_span,
                'context_spans': unique_spans,
                'segment_summary': {
                    'total_spans': len(unique_spans),
                    'problem_types': combined_problems,
                    'severity': max(segment1['problem_span']['severity'], segment2['problem_span']['severity']),
                    'services_involved': list(set(
                        segment1['segment_summary']['services_involved'] +
                        segment2['segment_summary']['services_involved']
                    )),
                    'duration_range': {
                        'start': min(
                            segment1['segment_summary']['duration_range']['start'],
                            segment2['segment_summary']['duration_range']['start']
                        ),
                        'end': max(
                            segment1['segment_summary']['duration_range']['end'],
                            segment2['segment_summary']['duration_range']['end']
                        )
                    }
                }
            }

            return merged_segment

        except Exception as e:
            self.logger.error(f"❌ 两段落合并失败: {e}")
            return segment1

    def _convert_segments_to_dataframe(self, problem_segments: List[Dict]) -> pd.DataFrame:
        """将问题段落转换回DataFrame格式供后续处理"""
        try:
            if not problem_segments:
                return pd.DataFrame()

            # 收集所有段落中的span
            all_spans = []
            for segment in problem_segments:
                for span_data in segment['context_spans']:
                    # 添加段落信息到span中
                    span_dict = span_data['span'].to_dict() if hasattr(span_data['span'], 'to_dict') else dict(span_data['span'])
                    span_dict['_segment_id'] = segment['segment_id']
                    span_dict['_problem_types'] = segment['segment_summary']['problem_types']
                    span_dict['_severity'] = segment['segment_summary']['severity']
                    all_spans.append(span_dict)

            # 去重（基于spanID）
            unique_spans = []
            seen_span_ids = set()
            for span in all_spans:
                span_id = span.get('spanID')
                if span_id and span_id not in seen_span_ids:
                    unique_spans.append(span)
                    seen_span_ids.add(span_id)

            self.logger.info(f"📊 问题段落转换: {len(problem_segments)} 个段落 → {len(unique_spans)} 个唯一span")

            return pd.DataFrame(unique_spans)

        except Exception as e:
            self.logger.error(f"❌ 段落转换失败: {e}")
            return pd.DataFrame()

    def _extract_call_chain_from_anomaly(self, anomaly: Dict[str, Any]) -> Dict[str, Any]:
        """从异常中提取调用链信息"""
        try:
            trace_id = anomaly.get("traceID", "unknown")
            operation_name = anomaly.get("operationName", "unknown")
            service = anomaly.get("service", "unknown")

            # 从操作名称中提取调用关系
            call_info = {}
            if "hipstershop." in operation_name:
                # 解析 hipstershop.ServiceName/Operation 格式
                parts = operation_name.replace("hipstershop.", "").split("/")
                if len(parts) >= 2:
                    target_service = parts[0].lower() + "service"  # ProductCatalogService -> productcatalogservice
                    operation = parts[1]
                    call_info = {
                        "target_service": target_service,
                        "operation": operation,
                        "call_type": "grpc"
                    }

            return {
                "trace_id": trace_id,
                "service": service,
                "operation_name": operation_name,
                "call_info": call_info
            }

        except Exception as e:
            self.logger.debug(f"提取调用链信息失败: {e}")
            return {}

    def _build_call_chain_context(self, service: str, anomaly_list: List[Dict],
                                 trace_call_chains: Dict[str, Dict]) -> Dict[str, Any]:
        """构建服务的调用链上下文"""
        try:
            # 收集该服务相关的所有调用信息
            upstream_services = set()  # 调用该服务的上游服务
            downstream_services = set()  # 该服务调用的下游服务
            trace_ids = set()

            for anomaly in anomaly_list:
                trace_id = anomaly.get("traceID", "unknown")
                trace_ids.add(trace_id)

                # 从调用链信息中提取上下游关系
                if trace_id in trace_call_chains:
                    chain_info = trace_call_chains[trace_id]
                    call_info = chain_info.get("call_info", {})

                    # 如果这是一个客户端调用，记录下游服务
                    if call_info.get("target_service"):
                        downstream_services.add(call_info["target_service"])

                    # 记录当前服务（作为其他服务的下游）
                    current_service = chain_info.get("service", "")
                    if current_service and current_service != service:
                        upstream_services.add(current_service)

            # 构建调用链上下文
            context = {
                "service": service,
                "trace_ids": list(trace_ids),
                "upstream_services": list(upstream_services),
                "downstream_services": list(downstream_services),
                "primary_target": list(downstream_services)[0] if downstream_services else "unknown",
                "call_chain_summary": self._build_call_chain_summary(service, upstream_services, downstream_services)
            }

            return context

        except Exception as e:
            self.logger.debug(f"构建调用链上下文失败: {e}")
            return {
                "service": service,
                "trace_ids": [],
                "upstream_services": [],
                "downstream_services": [],
                "primary_target": "unknown",
                "call_chain_summary": f"{service} (调用链信息不完整)"
            }

    def _build_call_chain_summary(self, service: str, upstream_services: set, downstream_services: set) -> str:
        """构建调用链摘要"""
        try:
            summary_parts = []

            if upstream_services:
                upstream_str = " | ".join(sorted(upstream_services))
                summary_parts.append(f"[{upstream_str}]")

            summary_parts.append(f"→ {service} →")

            if downstream_services:
                downstream_str = " | ".join(sorted(downstream_services))
                summary_parts.append(f"[{downstream_str}]")

            return " ".join(summary_parts)

        except Exception as e:
            return f"{service} (调用链摘要构建失败)"

    def _build_detailed_spans_data(self, anomaly_list: List[Dict]) -> List[Dict]:
        """构建详细的span级别数据 - 🔧 修复：确保调用关系信息完整"""
        try:
            detailed_spans = []
            self.logger.info(f"🔧 构建详细span数据: 输入 {len(anomaly_list)} 个异常")

            for i, anomaly in enumerate(anomaly_list):
                self.logger.info(f"🔧 处理异常 {i+1}: {anomaly.get('traceID', 'unknown')[:8]}...")

                # 🔧 调试：打印anomaly对象的关键字段
                self.logger.info(f"🔍 anomaly对象: traceID={anomaly.get('traceID')}, spanID={anomaly.get('spanID')}")

                # 🔧 修复：如果anomaly对象中缺少tags、logs、references，从原始数据中获取
                trace_id = anomaly.get("traceID", "unknown")
                span_id = anomaly.get("spanID", "unknown")

                # 🔧 新增：如果spanID为N/A或unknown，尝试从原始数据中查找匹配的span
                original_span = self._find_original_span_enhanced(trace_id, span_id, anomaly)

                # 🔧 调试：检查是否成功获取了原始span数据
                if original_span:
                    tags_count = len(original_span.get("tags", []))
                    logs_count = len(original_span.get("logs", []))
                    refs_count = len(original_span.get("references", []))
                    actual_span_id = original_span.get("spanID", "unknown")
                    self.logger.info(f"🔍 原始span数据: spanID={actual_span_id[:8]}..., tags={tags_count}, logs={logs_count}, refs={refs_count}")

                    # 🔧 修复：使用原始数据中的真实spanID
                    if actual_span_id and actual_span_id != "unknown":
                        span_id = actual_span_id
                else:
                    self.logger.warning(f"⚠️ 未找到原始span数据: {trace_id[:8]}.../{span_id[:8]}...")

                # 🔧 修复：提取调用关系信息，优先使用原始数据
                parent_span_id = "unknown"
                references = []

                if original_span:
                    parent_span_id = self._extract_parent_span_id(original_span)
                    references = original_span.get("references", [])
                    self.logger.debug(f"🔗 从原始数据提取调用关系: parent={parent_span_id}, refs_count={len(references)}")
                else:
                    # 如果没有原始数据，尝试从anomaly中提取
                    parent_span_id = self._extract_parent_span_id(anomaly)
                    references = anomaly.get("references", [])
                    self.logger.debug(f"🔗 从异常数据提取调用关系: parent={parent_span_id}, refs_count={len(references)}")

                span_detail = {
                    # 基本信息
                    "trace_id": trace_id,
                    "span_id": span_id,  # 使用修正后的spanID
                    "operation_name": anomaly.get("operationName", "unknown"),
                    "service_name": anomaly.get("service", "unknown"),

                    # 时间信息
                    "start_time": anomaly.get("startTimeMillis", 0),
                    "duration_us": anomaly.get("duration", 0),
                    "duration_ms": anomaly.get("duration", 0) / 1000,

                    # 🔧 修复：调用关系 - 使用修正后的数据
                    "parent_span_id": parent_span_id,
                    "references": references,

                    # 🔧 修复：错误信息 - 优先使用原始数据
                    "tags": original_span.get("tags", []) if original_span else anomaly.get("tags", []),
                    "logs": original_span.get("logs", []) if original_span else anomaly.get("logs", []),
                    "grpc_status_code": self._safe_extract_grpc_status_code(original_span if original_span else anomaly),
                    "http_status_code": self._safe_extract_http_status_code(original_span if original_span else anomaly),

                    # 分析结果
                    "anomaly_type": anomaly.get("anomaly_type", "unknown"),
                    "error_indicators": self._safe_extract_all_error_indicators(original_span if original_span else anomaly),

                    # 调用目标（从operationName解析）
                    "target_service": self._extract_target_service_from_operation(anomaly.get("operationName", "")),
                    "call_type": self._determine_call_type(original_span if original_span else anomaly)
                }

                detailed_spans.append(span_detail)

            # 按时间排序
            detailed_spans.sort(key=lambda x: x["start_time"])

            self.logger.info(f"🔧 构建详细span数据完成: 输出 {len(detailed_spans)} 个详细span")
            return detailed_spans

        except Exception as e:
            self.logger.error(f"❌ 构建详细span数据失败: {e}")
            return []

    def _find_original_span(self, trace_id: str, span_id: str) -> Dict:
        """从原始traces数据中查找对应的span - 🔧 修复：获取完整的tags、logs、references"""
        try:
            # 如果没有原始数据，返回None
            if not hasattr(self, 'original_traces_data') or self.original_traces_data is None:
                return None

            # 🔧 修复：如果span_id为空或unknown，尝试通过traceID查找第一个span
            if not span_id or span_id == "unknown" or span_id == "":
                matching_spans = self.original_traces_data[
                    self.original_traces_data['traceID'] == trace_id
                ]
                if not matching_spans.empty:
                    # 返回该trace的第一个span
                    original_span = matching_spans.iloc[0].to_dict()
                    self.logger.debug(f"🔍 通过traceID找到span: {trace_id[:8]}...")
                    return original_span
            else:
                # 精确匹配traceID和spanID
                matching_spans = self.original_traces_data[
                    (self.original_traces_data['traceID'] == trace_id) &
                    (self.original_traces_data['spanID'] == span_id)
                ]

                if not matching_spans.empty:
                    # 返回第一个匹配的span（转换为字典）
                    original_span = matching_spans.iloc[0].to_dict()
                    self.logger.debug(f"🔍 精确匹配找到span: {trace_id[:8]}.../{span_id[:8]}...")
                    return original_span

            self.logger.debug(f"⚠️ 未找到原始span: {trace_id[:8]}.../{span_id[:8] if span_id else 'EMPTY'}...")
            return None

        except Exception as e:
            self.logger.debug(f"查找原始span失败: {e}")
            return None

    def _find_original_span_enhanced(self, trace_id: str, span_id: str, anomaly: Dict) -> Dict:
        """增强的原始span查找 - 🔧 修复：通过多种方式匹配span"""
        try:
            # 如果没有原始数据，返回None
            if not hasattr(self, 'original_traces_data') or self.original_traces_data is None:
                return None

            # 方法1：精确匹配traceID和spanID
            if span_id and span_id != "unknown" and span_id != "N/A":
                matching_spans = self.original_traces_data[
                    (self.original_traces_data['traceID'] == trace_id) &
                    (self.original_traces_data['spanID'] == span_id)
                ]
                if not matching_spans.empty:
                    original_span = matching_spans.iloc[0].to_dict()
                    self.logger.debug(f"🔍 精确匹配找到span: {trace_id[:8]}.../{span_id[:8]}...")
                    return original_span

            # 方法2：通过traceID和operationName匹配
            operation_name = anomaly.get("operationName", "")
            if operation_name and operation_name != "unknown":
                matching_spans = self.original_traces_data[
                    (self.original_traces_data['traceID'] == trace_id) &
                    (self.original_traces_data['operationName'] == operation_name)
                ]
                if not matching_spans.empty:
                    original_span = matching_spans.iloc[0].to_dict()
                    self.logger.debug(f"🔍 通过operationName找到span: {trace_id[:8]}.../{operation_name[:20]}...")
                    return original_span

            # 方法3：通过traceID和服务名匹配
            service_name = anomaly.get("service", "")
            if service_name and service_name != "unknown":
                # 从原始数据中查找匹配的服务
                trace_spans = self.original_traces_data[self.original_traces_data['traceID'] == trace_id]
                for _, span in trace_spans.iterrows():
                    span_service = self._extract_service_name(span)
                    if span_service == service_name:
                        original_span = span.to_dict()
                        self.logger.debug(f"🔍 通过服务名找到span: {trace_id[:8]}.../{service_name}")
                        return original_span

            # 方法4：通过traceID查找第一个span（最后的备选方案）
            matching_spans = self.original_traces_data[self.original_traces_data['traceID'] == trace_id]
            if not matching_spans.empty:
                original_span = matching_spans.iloc[0].to_dict()
                self.logger.debug(f"🔍 通过traceID找到第一个span: {trace_id[:8]}...")
                return original_span

            self.logger.debug(f"⚠️ 未找到原始span: {trace_id[:8]}...")
            return None

        except Exception as e:
            self.logger.debug(f"增强span查找失败: {e}")
            return None

    def _extract_parent_span_id(self, span) -> str:
        """提取父span ID"""
        try:
            references = span.get("references", [])
            if isinstance(references, list):
                for ref in references:
                    if isinstance(ref, dict) and ref.get("refType") == "CHILD_OF":
                        return ref.get("spanID", "unknown")
            return "root"
        except:
            return "unknown"

    def _safe_extract_grpc_status_code(self, span) -> int:
        """安全提取gRPC状态码"""
        try:
            return self._extract_grpc_status_code(span)
        except Exception as e:
            self.logger.debug(f"安全提取gRPC状态码失败: {e}")
            return 0

    def _safe_extract_http_status_code(self, span) -> int:
        """安全提取HTTP状态码"""
        try:
            return self._extract_http_status_code(span)
        except Exception as e:
            self.logger.debug(f"安全提取HTTP状态码失败: {e}")
            return 0

    def _safe_extract_all_error_indicators(self, span) -> List[str]:
        """安全提取所有错误指标"""
        try:
            return self._extract_all_error_indicators(span)
        except Exception as e:
            self.logger.debug(f"安全提取错误指标失败: {e}")
            return []

    def _extract_grpc_status_code(self, span) -> int:
        """提取gRPC状态码"""
        try:
            tags = span.get('tags', [])
            if isinstance(tags, str):
                import json
                tags = json.loads(tags)

            if isinstance(tags, list):
                for tag in tags:
                    if isinstance(tag, dict) and tag.get('key') == 'rpc.grpc.status_code':
                        try:
                            return int(tag.get('value', 0))
                        except (ValueError, TypeError):
                            return 0
            return 0
        except Exception as e:
            self.logger.debug(f"提取gRPC状态码失败: {e}")
            return 0

    def _extract_http_status_code(self, span) -> int:
        """提取HTTP状态码"""
        try:
            tags = span.get('tags', [])
            if isinstance(tags, str):
                import json
                tags = json.loads(tags)

            if isinstance(tags, list):
                for tag in tags:
                    if isinstance(tag, dict) and tag.get('key') == 'http.status_code':
                        try:
                            return int(tag.get('value', 0))
                        except (ValueError, TypeError):
                            return 0
            return 0
        except Exception as e:
            self.logger.debug(f"提取HTTP状态码失败: {e}")
            return 0

    def _extract_parent_span_id(self, span) -> str:
        """提取父SpanID"""
        try:
            references = span.get('references', [])
            if isinstance(references, list):
                for ref in references:
                    if isinstance(ref, dict) and ref.get('refType') == 'CHILD_OF':
                        return ref.get('spanID', 'unknown')
            return span.get('parentSpanID', 'unknown')
        except Exception as e:
            self.logger.debug(f"提取父SpanID失败: {e}")
            return 'unknown'

    def _has_error_logs(self, span) -> bool:
        """检查是否有错误日志"""
        try:
            logs = span.get('logs', [])
            if isinstance(logs, str):
                import json
                logs = json.loads(logs)

            if isinstance(logs, list):
                for log in logs:
                    if isinstance(log, dict):
                        fields = log.get('fields', [])
                        for field in fields:
                            if isinstance(field, dict):
                                if field.get('key') == 'level' and field.get('value', '').lower() in ['error', 'fatal']:
                                    return True
                                if field.get('key') == 'event' and 'error' in field.get('value', '').lower():
                                    return True
            return False
        except Exception as e:
            self.logger.debug(f"检查错误日志失败: {e}")
            return False

    def _extract_all_error_indicators(self, span) -> List[str]:
        """提取所有错误指标"""
        try:
            indicators = []

            # gRPC错误
            grpc_status = self._extract_grpc_status_code(span)
            if grpc_status != 0:
                indicators.append(f"grpc_error_{grpc_status}")

            # HTTP错误
            http_status = self._extract_http_status_code(span)
            if http_status >= 400:
                indicators.append(f"http_error_{http_status}")

            # 日志错误
            if self._has_error_logs(span):
                indicators.append("error_logs")

            # 超时检测
            duration_ms = span.get("duration", 0) / 1000
            if duration_ms > 30000:  # 30秒
                indicators.append("timeout")
            elif duration_ms > 10000:  # 10秒
                indicators.append("high_latency")

            return indicators

        except Exception as e:
            self.logger.debug(f"提取错误指标失败: {e}")
            return []

    def _extract_target_service_from_operation(self, operation_name: str) -> str:
        """从操作名称中提取目标服务"""
        try:
            if not operation_name or operation_name == "unknown":
                return "unknown"

            # 解析 hipstershop.ServiceName/Method 格式
            if "hipstershop." in operation_name and "/" in operation_name:
                service_part = operation_name.split("/")[0]
                service_name = service_part.replace("hipstershop.", "").lower()
                return service_name

            # 解析其他格式
            if "/" in operation_name:
                return operation_name.split("/")[0].lower()

            return "unknown"
        except Exception as e:
            self.logger.debug(f"提取目标服务失败: {e}")
            return "unknown"

    def _determine_call_type(self, span) -> str:
        """确定调用类型"""
        try:
            operation_name = span.get("operationName", "")
            tags = span.get("tags", [])

            # 检查是否是gRPC调用
            for tag in tags:
                if isinstance(tag, dict):
                    if tag.get("key") == "rpc.system" and tag.get("value") == "grpc":
                        return "grpc"
                    if tag.get("key") == "http.method":
                        return "http"

            # 根据操作名称推断
            if "hipstershop." in operation_name:
                return "grpc"
            elif operation_name.startswith("HTTP "):
                return "http"

            return "unknown"
        except Exception as e:
            self.logger.debug(f"确定调用类型失败: {e}")
            return "unknown"

    def _extract_target_service_from_operation(self, operation_name: str) -> str:
        """从操作名称中提取目标服务"""
        try:
            if "hipstershop." in operation_name:
                # 解析 hipstershop.ServiceName/Operation 格式
                parts = operation_name.replace("hipstershop.", "").split("/")
                if len(parts) >= 1:
                    service_name = parts[0]
                    # 转换为小写并添加service后缀
                    return service_name.lower() + "service"
            return "unknown"
        except:
            return "unknown"

    def _determine_call_type(self, span) -> str:
        """确定调用类型"""
        try:
            operation_name = span.get("operationName", "")
            if "hipstershop." in operation_name:
                return "grpc"
            elif "http" in operation_name.lower():
                return "http"
            else:
                return "unknown"
        except:
            return "unknown"

    def _build_trace_analysis_summary(self, anomaly_list: List[Dict], call_chain_context: Dict) -> Dict:
        """构建trace分析摘要"""
        try:
            # 按traceID分组
            traces_by_id = {}
            for anomaly in anomaly_list:
                trace_id = anomaly.get("traceID", "unknown")
                if trace_id not in traces_by_id:
                    traces_by_id[trace_id] = []
                traces_by_id[trace_id].append(anomaly)

            trace_summaries = []
            for trace_id, spans in traces_by_id.items():
                # 构建单个trace的分析摘要
                trace_summary = self._analyze_single_trace_summary(trace_id, spans)
                trace_summaries.append(trace_summary)

            # 构建整体分析摘要
            analysis_summary = {
                "total_traces": len(traces_by_id),
                "total_spans": len(anomaly_list),
                "trace_summaries": trace_summaries,
                "call_patterns": self._identify_call_patterns(anomaly_list),
                "fault_propagation": self._analyze_fault_propagation(anomaly_list, call_chain_context),
                "critical_path": self._identify_critical_path(anomaly_list)
            }

            return analysis_summary

        except Exception as e:
            self.logger.error(f"❌ 构建trace分析摘要失败: {e}")
            return {}

    def _analyze_single_trace_summary(self, trace_id: str, spans: List[Dict]) -> Dict:
        """分析单个trace的摘要"""
        try:
            # 按时间排序
            sorted_spans = sorted(spans, key=lambda x: x.get("startTimeMillis", 0))

            # 计算trace总时长
            if sorted_spans:
                start_time = sorted_spans[0].get("startTimeMillis", 0)
                end_times = [span.get("startTimeMillis", 0) + span.get("duration", 0)/1000
                           for span in sorted_spans]
                total_duration = max(end_times) - start_time if end_times else 0
            else:
                total_duration = 0

            # 构建调用路径
            call_path = []
            for span in sorted_spans:
                service = span.get("service", "unknown")
                operation = span.get("operationName", "unknown")
                duration_ms = span.get("duration", 0) / 1000

                call_path.append({
                    "service": service,
                    "operation": operation,
                    "duration_ms": duration_ms,
                    "has_errors": len(self._extract_all_error_indicators(span)) > 0
                })

            return {
                "trace_id": trace_id,
                "span_count": len(spans),
                "total_duration_ms": total_duration,
                "call_path": call_path,
                "error_count": sum(1 for span in spans if len(self._extract_all_error_indicators(span)) > 0),
                "services_involved": list(set(span.get("service", "unknown") for span in spans))
            }

        except Exception as e:
            self.logger.debug(f"分析单个trace摘要失败: {e}")
            return {"trace_id": trace_id, "error": str(e)}

    def _identify_call_patterns(self, anomaly_list: List[Dict]) -> List[Dict]:
        """识别调用模式"""
        try:
            patterns = []

            # 统计服务调用频次
            service_calls = {}
            for anomaly in anomaly_list:
                service = anomaly.get("service", "unknown")
                target = self._extract_target_service_from_operation(anomaly.get("operationName", ""))

                if target != "unknown":
                    call_pair = f"{service} -> {target}"
                    if call_pair not in service_calls:
                        service_calls[call_pair] = {
                            "count": 0,
                            "total_duration": 0,
                            "error_count": 0
                        }

                    service_calls[call_pair]["count"] += 1
                    service_calls[call_pair]["total_duration"] += anomaly.get("duration", 0) / 1000
                    if len(self._extract_all_error_indicators(anomaly)) > 0:
                        service_calls[call_pair]["error_count"] += 1

            # 转换为模式列表
            for call_pair, stats in service_calls.items():
                patterns.append({
                    "call_pattern": call_pair,
                    "frequency": stats["count"],
                    "avg_duration_ms": stats["total_duration"] / stats["count"] if stats["count"] > 0 else 0,
                    "error_rate": stats["error_count"] / stats["count"] if stats["count"] > 0 else 0
                })

            # 按频次排序
            patterns.sort(key=lambda x: x["frequency"], reverse=True)

            return patterns[:10]  # 返回前10个模式

        except Exception as e:
            self.logger.debug(f"识别调用模式失败: {e}")
            return []

    def _analyze_fault_propagation(self, anomaly_list: List[Dict], call_chain_context: Dict) -> Dict:
        """分析故障传播"""
        try:
            # 识别故障传播路径
            error_services = set()
            normal_services = set()

            for anomaly in anomaly_list:
                service = anomaly.get("service", "unknown")
                has_errors = len(self._extract_all_error_indicators(anomaly)) > 0

                if has_errors:
                    error_services.add(service)
                else:
                    normal_services.add(service)

            # 分析传播方向
            upstream_errors = error_services & set(call_chain_context.get("upstream_services", []))
            downstream_errors = error_services & set(call_chain_context.get("downstream_services", []))

            return {
                "error_services": list(error_services),
                "normal_services": list(normal_services),
                "upstream_errors": list(upstream_errors),
                "downstream_errors": list(downstream_errors),
                "propagation_direction": "upstream_to_downstream" if upstream_errors and downstream_errors else "isolated"
            }

        except Exception as e:
            self.logger.debug(f"分析故障传播失败: {e}")
            return {}

    def _identify_critical_path(self, anomaly_list: List[Dict]) -> List[Dict]:
        """识别关键路径"""
        try:
            # 找到延迟最高的调用路径
            critical_spans = []

            for anomaly in anomaly_list:
                duration_ms = anomaly.get("duration", 0) / 1000
                error_indicators = self._extract_all_error_indicators(anomaly)

                # 关键路径标准：高延迟或有错误
                if duration_ms > 1000 or len(error_indicators) > 0:  # 1秒以上或有错误
                    critical_spans.append({
                        "service": anomaly.get("service", "unknown"),
                        "operation": anomaly.get("operationName", "unknown"),
                        "duration_ms": duration_ms,
                        "error_indicators": error_indicators,
                        "criticality_score": duration_ms / 1000 + len(error_indicators) * 2  # 延迟权重1，错误权重2
                    })

            # 按关键性评分排序
            critical_spans.sort(key=lambda x: x["criticality_score"], reverse=True)

            return critical_spans[:5]  # 返回前5个关键span

        except Exception as e:
            self.logger.debug(f"识别关键路径失败: {e}")
            return []
