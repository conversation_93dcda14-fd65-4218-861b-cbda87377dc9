.docusaurus/
node_modules/
# Project
/.vs
# Visual Studio 2015/2017 cache/options directory
.vs/

.vscode

# Log files
*.log

# Python virtualenv
.venv*

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
aiops-env/

# AIOps specific files
*.json.bak
*_results.json
*_output.json
error_log.json
debug_*.log
performance_*.log

# Data directories (if containing sensitive data)
# data/
# logs/
# outputs/

# LLM API keys and secrets
.env.local
.env.production
secrets.yaml
api_keys.txt

# Temporary files
temp/
tmp/
*.tmp
*.temp

# IDE and editor files
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

logs

.idea/*
.DS_Store

output/
*.pkl

# local config files
*.config.local
OAI_CONFIG_LIST
key_openai.txt
key_aoai.txt
base_aoai.txt
wolfram.txt

# DB on disk for Teachability
tmp/
test/my_tmp/*

# Storage for the AgentEval output
test/test_files/agenteval-in-out/out/

# local cache or coding foler
local_cache/
coding/

# Files created by tests
*tmp_code_*
test/agentchat/test_agent_scripts/*

# test cache
.cache_test
.db
local_cache


notebook/result.png
samples/apps/autogen-studio/autogenstudio/models/test/

notebook/coding

# dotnet artifacts
artifacts

# project data
registry.json