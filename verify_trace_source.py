#!/usr/bin/env python3
"""
验证trace源数据：检查TraceID和延迟信息的准确性
"""

import json
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append('/home/<USER>/aiopsagent/autogen-0.4.4')

from aiops_diagnosis.autogen_agents.data_loader_agent import AutoGenDataLoaderAgent

def verify_trace_source():
    """验证trace源数据"""
    print("🔍 开始验证trace源数据...")

    # 初始化DataLoaderAgent
    data_loader = AutoGenDataLoaderAgent()
    
    # 获取案例数据
    case_id = "ad13b37a-146"
    print(f"📋 案例ID: {case_id}")
    
    try:
        # 获取traces数据
        print("📤 请求traces数据...")
        traces_data = data_loader.get_traces_data(case_id)
        
        if not traces_data:
            print("❌ 没有获取到traces数据")
            return
            
        print(f"✅ 获取到 {len(traces_data)} 条traces记录")
        
        # 查找特定的TraceID
        target_trace_ids = [
            "e4e35ee0c4303cdfdf64f368e50a9492",
            "5b3dff6f2fafbacadb979116e6564e55"
        ]
        
        found_traces = {}
        
        for i, trace in enumerate(traces_data):
            if isinstance(trace, dict):
                trace_id = trace.get('traceID', '')
                if trace_id in target_trace_ids:
                    found_traces[trace_id] = trace
                    print(f"\n🎯 找到目标TraceID: {trace_id}")
                    print(f"   索引: {i}")
                    print(f"   SpanID: {trace.get('spanID', 'unknown')}")
                    print(f"   操作名: {trace.get('operationName', 'unknown')}")
                    print(f"   持续时间: {trace.get('duration', 0)} 微秒")
                    print(f"   持续时间(ms): {trace.get('duration', 0) / 1000:.1f} ms")
                    print(f"   开始时间: {trace.get('startTime', 0)}")
                    
                    # 检查process信息
                    process = trace.get('process', {})
                    print(f"   服务名: {process.get('serviceName', 'unknown')}")
                    print(f"   主机名: {process.get('hostname', 'unknown')}")
                    
                    # 检查tags
                    tags = trace.get('tags', [])
                    print(f"   Tags数量: {len(tags)}")
                    error_tags = []
                    for tag in tags:
                        if isinstance(tag, dict):
                            key = tag.get('key', '')
                            value = tag.get('value', '')
                            if ('error' in key.lower() or 'status' in key.lower() or 'http' in key.lower()):
                                error_tags.append(f"{key}: {value}")
                    
                    if error_tags:
                        print("   🚨 错误相关Tags:")
                        for error_tag in error_tags:
                            print(f"     - {error_tag}")
                    
                    print("   " + "="*50)
        
        if not found_traces:
            print("❌ 没有找到目标TraceID")
            # 显示前几个trace的TraceID作为参考
            print("\n📋 前10个trace的TraceID:")
            for i, trace in enumerate(traces_data[:10]):
                if isinstance(trace, dict):
                    trace_id = trace.get('traceID', 'unknown')
                    duration = trace.get('duration', 0)
                    service = trace.get('process', {}).get('serviceName', 'unknown')
                    print(f"   {i+1}. {trace_id} - {service} - {duration/1000:.1f}ms")
        else:
            print(f"\n✅ 成功找到 {len(found_traces)} 个目标TraceID")
            
            # 验证数据一致性
            print("\n🔍 验证数据一致性...")
            for trace_id, trace in found_traces.items():
                print(f"\n📊 TraceID: {trace_id}")
                
                # 验证延迟计算
                duration_us = trace.get('duration', 0)
                duration_ms = duration_us / 1000 if duration_us else 0
                print(f"   原始持续时间: {duration_us} 微秒")
                print(f"   转换后延迟: {duration_ms:.1f} ms")
                
                # 验证时间戳
                start_time = trace.get('startTime', 0)
                print(f"   开始时间戳: {start_time}")
                
                if start_time:
                    try:
                        import datetime
                        timestamp_seconds = start_time / 1000000  # 微秒转秒
                        dt = datetime.datetime.fromtimestamp(timestamp_seconds)
                        formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                        print(f"   格式化时间: {formatted_time}")
                    except:
                        print(f"   时间格式化失败")
                
                # 验证服务信息
                process = trace.get('process', {})
                service_name = process.get('serviceName', 'unknown')
                hostname = process.get('hostname', 'unknown')
                print(f"   服务名: {service_name}")
                print(f"   主机名: {hostname}")
                
                # 验证错误信息
                tags = trace.get('tags', [])
                error_info = []
                for tag in tags:
                    if isinstance(tag, dict):
                        key = tag.get('key', '')
                        value = tag.get('value', '')
                        if key == 'otel.status_code' and value != '0':
                            error_info.append(f"OpenTelemetry错误: {value}")
                        elif key == 'http.status_code' and str(value).isdigit() and int(value) >= 400:
                            error_info.append(f"HTTP错误: {value}")
                        elif key == 'error' and str(value).lower() == 'true':
                            error_info.append("通用错误标志")
                
                if error_info:
                    print("   🚨 检测到的错误:")
                    for error in error_info:
                        print(f"     - {error}")
                else:
                    print("   ✅ 未检测到错误标志")
        
    except Exception as e:
        print(f"❌ 验证trace数据时发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_trace_source()
