#!/usr/bin/env python3
"""
调试trace数据结构，查看unknown值的来源
"""
import pandas as pd
import json
from pathlib import Path
import sys

def analyze_trace_data_structure():
    """分析trace数据结构，查找unknown值的来源"""
    
    # 查找最新的trace文件
    data_root = Path("/data/phaseone_data")
    trace_dirs = []
    
    # 查找最近几天的数据
    for date_dir in sorted(data_root.glob("2025-*"), reverse=True)[:3]:
        trace_dir = date_dir / "trace-parquet"
        if trace_dir.exists():
            trace_files = list(trace_dir.glob("*.parquet"))
            if trace_files:
                trace_dirs.append((date_dir.name, trace_files[:2]))  # 只取前2个文件
    
    if not trace_dirs:
        print("❌ 未找到trace数据文件")
        return
    
    print("🔍 **Trace数据结构分析**")
    print("=" * 60)
    
    for date, files in trace_dirs[:1]:  # 只分析最新的一天
        print(f"\n📅 分析日期: {date}")
        
        for file_path in files[:1]:  # 只分析第一个文件
            print(f"\n📁 文件: {file_path.name}")
            
            try:
                # 读取parquet文件
                df = pd.read_parquet(file_path)
                print(f"📊 总记录数: {len(df)}")
                print(f"📊 列数: {len(df.columns)}")
                
                # 显示列名
                print(f"\n📋 列名: {list(df.columns)}")
                
                # 分析前几条记录
                print(f"\n🔍 **前3条记录分析**:")
                for i in range(min(3, len(df))):
                    record = df.iloc[i]
                    print(f"\n--- 记录 {i+1} ---")
                    
                    # 检查关键字段
                    trace_id = record.get('traceID', 'MISSING')
                    span_id = record.get('spanID', 'MISSING')
                    operation_name = record.get('operationName', 'MISSING')
                    
                    print(f"traceID: {trace_id}")
                    print(f"spanID: {span_id}")
                    print(f"operationName: {operation_name}")
                    
                    # 分析process字段
                    process = record.get('process', 'MISSING')
                    print(f"process类型: {type(process)}")
                    
                    if isinstance(process, dict):
                        service_name = process.get('serviceName', 'MISSING')
                        hostname = process.get('hostname', 'MISSING')
                        print(f"process.serviceName: {service_name}")
                        print(f"process.hostname: {hostname}")
                        
                        # 检查tags
                        tags = process.get('tags', [])
                        print(f"process.tags数量: {len(tags) if isinstance(tags, list) else 'NOT_LIST'}")
                        if isinstance(tags, list) and tags:
                            print(f"前3个tags: {tags[:3]}")
                    elif isinstance(process, str):
                        print(f"process字符串长度: {len(process)}")
                        try:
                            process_dict = json.loads(process)
                            service_name = process_dict.get('serviceName', 'MISSING')
                            print(f"解析后serviceName: {service_name}")
                        except:
                            print("process字符串无法解析为JSON")
                    else:
                        print(f"process内容: {process}")
                
                # 统计字段缺失情况
                print(f"\n📊 **字段缺失统计**:")
                
                missing_stats = {}
                key_fields = ['traceID', 'spanID', 'operationName', 'process']
                
                for field in key_fields:
                    if field in df.columns:
                        missing_count = df[field].isna().sum()
                        empty_count = (df[field] == '').sum() if df[field].dtype == 'object' else 0
                        missing_stats[field] = {
                            'missing': missing_count,
                            'empty': empty_count,
                            'total': len(df)
                        }
                    else:
                        missing_stats[field] = {'missing': len(df), 'empty': 0, 'total': len(df)}
                
                for field, stats in missing_stats.items():
                    missing_rate = (stats['missing'] + stats['empty']) / stats['total'] * 100
                    print(f"{field}: 缺失{stats['missing']}条, 空值{stats['empty']}条, 缺失率{missing_rate:.1f}%")
                
                # 分析process字段中的serviceName
                if 'process' in df.columns:
                    print(f"\n🔍 **Process字段serviceName分析**:")
                    
                    service_names = []
                    unknown_count = 0
                    error_count = 0
                    
                    for _, row in df.head(100).iterrows():  # 只分析前100条
                        try:
                            process = row.get('process', {})
                            if isinstance(process, dict):
                                service_name = process.get('serviceName', 'unknown')
                            elif isinstance(process, str):
                                try:
                                    process_dict = json.loads(process)
                                    service_name = process_dict.get('serviceName', 'unknown')
                                except:
                                    service_name = 'unknown'
                                    error_count += 1
                            else:
                                service_name = 'unknown'
                            
                            service_names.append(service_name)
                            if service_name == 'unknown':
                                unknown_count += 1
                                
                        except Exception as e:
                            error_count += 1
                            service_names.append('unknown')
                    
                    # 统计服务名分布
                    from collections import Counter
                    service_counter = Counter(service_names)
                    
                    print(f"前100条记录中:")
                    print(f"- unknown服务名: {unknown_count}条")
                    print(f"- 解析错误: {error_count}条")
                    print(f"- 服务名分布: {dict(service_counter.most_common(10))}")
                
                break  # 只分析第一个文件
            
            except Exception as e:
                print(f"❌ 读取文件失败: {e}")
        
        break  # 只分析第一天

if __name__ == "__main__":
    analyze_trace_data_structure()
