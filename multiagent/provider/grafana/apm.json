{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "service", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 0, "y": 1}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "error{object_type=~\"service\",object_id=~\"$service\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "error", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 5, "y": 1}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "server_error{object_type=~\"service\",object_id=~\"$service\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "server_error", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 9, "y": 1}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "client_error{object_type=~\"service\",object_id=~\"$service\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "client_error", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 14, "y": 1}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "timeout{object_type=~\"service\",object_id=~\"$service\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "timeout", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 19, "y": 1}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "request{object_type=~\"service\",object_id=~\"$service\"} - response{object_type=~\"service\",object_id=~\"$service\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "response - request", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 8}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "rrt{object_type=~\"service\",object_id=~\"$service\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "rrt", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 9, "y": 8}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "rrt_max{object_type=~\"service\",object_id=~\"$service\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "rrt", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 8}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "rrt_max{object_type=~\"service\",object_id=~\"$service\"} / rrt{object_type=~\"service\",object_id=~\"$service\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "rrt_max/rrt", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 4, "panels": [], "title": "pod", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 0, "y": 16}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "error{object_type=~\"pod\",object_id=~\"$pod\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "error", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 5, "y": 16}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "client_error{object_type=~\"pod\",object_id=~\"$pod\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "client_error", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 10, "y": 16}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "server_error{object_type=~\"pod\",object_id=~\"$pod\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "server_error", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 14, "y": 16}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "timeout{object_type=~\"pod\",object_id=~\"$pod\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "timeout", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 19, "y": 16}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "request{object_type=~\"pod\",object_id=~\"$pod\"} - response{object_type=~\"pod\",object_id=~\"$pod\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "request-response", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 23}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "rrt{object_type='pod',object_id=~\"$pod\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "rrt", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 9, "y": 23}, "id": 21, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "rrt_max{object_type=~\"pod\",object_id=~\"$pod\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "rrt_max", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 23}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "editorMode": "code", "expr": "rrt_max{object_type=~\"pod\",object_id=~\"$pod\"} / rrt{object_type=~\"pod\",object_id=~\"$pod\"}", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "rrt_max / rrt", "type": "timeseries"}], "refresh": false, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "definition": "label_values(error,object_type)", "hide": 0, "includeAll": true, "label": "类型", "multi": true, "name": "type", "options": [], "query": {"query": "label_values(error,object_type)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "definition": "label_values(error{object_type=~\"$type\"},object_id)", "hide": 0, "includeAll": true, "label": "服务", "multi": true, "name": "service", "options": [], "query": {"query": "label_values(error{object_type=~\"$type\"},object_id)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["adservice-0", "adservice-1", "adservice-2", "cartservice-0", "cartservice-1", "cartservice-2", "checkoutservice-2", "currencyservice-0", "currencyservice-1", "currencyservice-2", "emailservice-0", "emailservice-1", "emailservice-2", "frontend-0", "frontend-1", "frontend-2", "paymentservice-0", "paymentservice-1", "paymentservice-2", "productcatalogservice-0", "productcatalogservice-1", "productcatalogservice-2", "recommendationservice-0", "redis-cart-0", "shippingservice-0", "shippingservice-1", "shippingservice-2"], "value": ["adservice-0", "adservice-1", "adservice-2", "cartservice-0", "cartservice-1", "cartservice-2", "checkoutservice-2", "currencyservice-0", "currencyservice-1", "currencyservice-2", "emailservice-0", "emailservice-1", "emailservice-2", "frontend-0", "frontend-1", "frontend-2", "paymentservice-0", "paymentservice-1", "paymentservice-2", "productcatalogservice-0", "productcatalogservice-1", "productcatalogservice-2", "recommendationservice-0", "redis-cart-0", "shippingservice-0", "shippingservice-1", "shippingservice-2"]}, "datasource": {"type": "prometheus", "uid": "ed7b31dd-c024-4b55-8a7a-4a25eb8d2cad"}, "definition": "label_values(error{object_type=\"pod\"},object_id)", "hide": 0, "includeAll": true, "label": "pod", "multi": true, "name": "pod", "options": [], "query": {"query": "label_values(error{object_type=\"pod\"},object_id)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "2025-06-16T23:10:00.000Z", "to": "2025-06-16T23:26:00.000Z"}, "timepicker": {}, "timezone": "", "title": "apm", "uid": "c5a12485-f9b1-48aa-944a-5fe59c2411bf", "version": 8, "weekStart": ""}