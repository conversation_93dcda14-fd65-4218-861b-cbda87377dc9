## 工具tools目录

### 用途说明：

提供给不同的智能体使用的工具。

#### 日志模块
- check_log： 提取关键日志数据

#### 指标模块
- check_metric_pod_restart： 提供pod重调度数据                           
- check_metric_tidb_pd： 提供tidb_pd的指标
- check_metric_tidb_tikv： 提供tidb_kv的指标
- check_metric_tidb_tidb： 提供tidb_tidb的指标
- check_metric_infra： 提供基础指标，不进行区分
- check_metric_infra_node： 细化指标，只提供node指标
- check_metric_infra_pod： 细化指标，只提供pod指标
- check_metric_apm： 提供apm指标，不进行区分
- check_metric_apm_service_history： 提供apm service的基线比对数据
- check_metric_apm_pod_history： 提供apm pod的基线比对数据
- check_node_cpu_rate： 细化指标，提供节点的cpu数据
- check_node_filesystem_usage_rate： 细化指标，提供节点的文件使用率指标
- check_metric_pod_process_abnormal： 细化指标，提供pod的进程异常指标
- check_node_io_util： 细化指标，提供节点的IO使用率指标
- check_pod_network_drop_to_zero： 细化指标，提供pod网络掉零的指标

#### tidb模块
- check_tidb_interrupt： 提供tidb的中断情况数据
- check_tidb_interrupt： 提供tikv的中断情况数据
- check_tidb_interrupt： 提供pd的中断情况数据

#### trace模块
- check_trace: 提供trace数据
- check_trace_duration: 提供trace duration数据
