from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core.tools import FunctionTool
from tools.time_analyze import time_analyzer
from tools.check_log import check_log
from tools.check_trace import check_trace
from tools.check_metric import check_metric_apm,check_metric_infra_node,\
    check_metric_infra_pod,check_metric_tidb_tikv,check_metric_tidb_tidb,check_metric_tidb_pd, \
    check_metric_pod_restart,check_node_cpu_rate,check_node_filesystem_usage_rate, \
    check_metric_pod_process_abnormal,check_metric_apm_pod_history,check_metric_apm_service_history,\
    check_node_io_util,check_pod_network_drop_to_zero
from tools.check_tidb import check_tidb_interrupt,check_tikv_interrupt,check_pd_interrupt
from autogen_agentchat.conditions import TextMentionTermination
from tools.check_trace_duration import check_trace_duration

time_analyzer_tool = FunctionTool(
    time_analyzer, 
    description="用于根据输入，识别出其中的开始时间和结束时间。"
    )

check_log_tool = FunctionTool(
    check_log, 
    description="用于根据故障的时间，检测时间范围内的异常日志，支持定义故障时间和过滤字段。"
)

check_trace_tool = FunctionTool(
    check_trace, 
    description="用于根据故障的时间，检测时间范围内的异常调用链信息，支持定义故障时间。"
)

check_metric_tidb_tool = FunctionTool(
    check_metric_tidb_tidb,
    description="用于输出在故障时间内，tidb组件指标数据，已经对应p9X的数据，支持定义p9x级别。"
)

check_metric_tikv_tool = FunctionTool(
    check_metric_tidb_tikv,
    description="用于输出在故障时间内，tikv的指标数据，已经对应p9X的数据，支持定义p9x级别。"
)

check_metric_pd_tool = FunctionTool(
    check_metric_tidb_pd,
    description="用于输出在故障时间内，pd的指标数据，已经对应p9X的数据，支持定义p9x级别。"
)

check_metric_infra_node_tool = FunctionTool(
    check_metric_infra_node,
    description="用于输出在故障时间内，node的基础指标数据，已经对应p9X的数据，支持定义p9x级别。"
)

check_metric_infra_pod_tool = FunctionTool(
    check_metric_infra_pod,
    description="用于输出在故障时间内，pod的指标数据，已经对应p9X的数据，支持定义p9x级别。"
)

check_metric_apm_tool = FunctionTool(
    check_metric_apm,
    description="用于输出在故障时间内，apm的指标数据，包括service和pod，已经对应p9X的数据，支持定义p9x级别。"
)

check_metric_pod_restart_tool = FunctionTool(
    check_metric_pod_restart,
    description="用于输出在故障时间内，pod的重启次数。"
)

check_tidb_interrupt_tool = FunctionTool(
    check_tidb_interrupt,
    description="用于输出在故障时间内，tidb是否存在监控断点"
)

check_tikv_interrupt_tool = FunctionTool(
    check_tikv_interrupt,
    description="用于输出在故障时间内，tikv是否存在监控断点"
)

check_pd_interrupt_tool = FunctionTool(
    check_pd_interrupt,
    description="用于输出在故障时间内，pd是否存在监控断点"
)

check_node_io_util_tool = FunctionTool(
    check_node_io_util,
    description="用于输出在故障时间内，node是否存在IO高使用率的情况"
)

check_trace_duration_tool = FunctionTool(
    check_trace_duration,
    description="用于输出在故障时间内，trace是否存在span持续时间长的情况"
)

check_node_cpu_rate_tool = FunctionTool(
    check_node_cpu_rate,
    description="用于输出在故障时间内,node的CPU使用率峰值"
)

check_node_filesystem_usage_rate_tool = FunctionTool(
    check_node_filesystem_usage_rate,
    description="用于输出在故障时间内,node的磁盘使用率峰值"
)

check_metric_pod_process_abnormal_tool = FunctionTool(
    check_metric_pod_process_abnormal,
    description="用于输出在故障时间内,pod的process num峰值"
)

check_metric_apm_pod_history_tool = FunctionTool(
    check_metric_apm_pod_history,
    description="用于输出在故障时间内,pod的apm数据与历史基线数据的关系"
)

check_metric_apm_service_history_tool = FunctionTool(
    check_metric_apm_service_history,
    description="用于输出在故障时间内,service的apm数据与历史基线数据的关系"
)

check_pod_network_drop_to_zero_tool = FunctionTool(
    check_pod_network_drop_to_zero,
    description="用于输出在故障时间内,pod的网络是否存在从有数值到掉0的情况"
)

import asyncio
import json
from autogen_agentchat.ui import Console

import re

def extract_json_from_content(content):
    """
    从智能体的message.content 中提取 JSON 字符串（去除 markdown 代码块等），并解析为 dict
    """
    if not isinstance(content, str):
        return None
    # 匹配 ```json ... ``` 或 ``` ... ``` 或直接是 json
    match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", content)
    if match:
        json_str = match.group(1)
    else:
        json_str = content
    try:
        return json.loads(json_str)
    except Exception as e:
        print("JSON解析失败，原始内容：", json_str)
        return None
            
async def run_and_collect(task:str):

    # Define a model client. You can use other model client that implements
    # the `ChatCompletionClient` interface.
    model_client = OpenAIChatCompletionClient(
        model="Pro/deepseek-ai/DeepSeek-R1",  # 替换为硅基流动提供的模型名称
        temperature=0.1,
        api_key="sk-qhukemetcffylfxgsmuckxbcenyvyuoudilowlvrveaixbrj",    # 替换为你的硅基流动 API 密钥
        base_url="https://api.siliconflow.cn/v1", # 替换为硅基流动的 API 端点 URL
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown"
        }
    )

    info_agent =  AssistantAgent(
        name="info_assistant",
        description="信息提取助手",
        model_client=model_client,
        tools=[],
        system_message="""你是一个信息提取助手，需要从输入的字符串内提取需要的信息。
        包括：开始时间，结束时间，uuid
        **举例**
        {
        "Anomaly Description": "A fault occurred from 2025-06-06T12:03:18Z to 2025-06-06T12:26:18Z. Please identify the root cause.",
        "uuid": "342d4820-100"
        }
        提取的开始时间："2025-06-06T12:03:18Z"
        提取的结束时间："2025-06-06T12:26:18Z"
        提取的uuid："342d4820-100",使用正则表达式提取方法是： [a-f0-9]{8}-[a-f0-9]{2,3}，
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )

    log_agent = AssistantAgent(
        name="log_assistant",
        description="日志故障检测助手",
        model_client=model_client,
        tools=[check_log_tool],
        system_message="""
        你是一个日志检测助手，目前你正在检查hisptershop的日志。需要根据开始时间，结束时间，检查时间范围内的日志是否有异常,完成以下任务：
        1. 输出结构化数据，包括故障组件，观察现象，故障原因
        2. 若未发现异常，明确返回"未检测到异常日志"
        3. 如果是frontend日志，需要检查message内出现的k8_pod名称，通常是以下选择：
            all_service = [
            "adservice",
            "cartservice",
            "currencyservice",
            "productcatalogservice",
            "checkoutservice",
            "recommendationservice",
            "shippingservice",
            "emailservice",
            "paymentservice",
            "tidb-pd",
            "tidb-tidb",
            "tidb-tikv",
            "cart-redis"
            ],
            all_pod = [
                "adservice-0",
                "adservice-1",
                "adservice-2",
                "cartservice-0",
                "cartservice-1",
                "cartservice-2",
                "currencyservice-0",
                "currencyservice-1",
                "currencyservice-2",
                "productcatalogservice-0",
                "productcatalogservice-1",
                "productcatalogservice-2",
                "checkoutservice-0",
                "checkoutservice-1",
                "checkoutservice-2",
                "recommendationservice-0",
                "recommendationservice-1",
                "recommendationservice-2",
                "shippingservice-0",
                "shippingservice-1",
                "shippingservice-2",
                "emailservice-0",
                "emailservice-1",
                "emailservice-2",
                "paymentservice-0",
                "paymentservice-1",
                "paymentservice-2",
                "tidb-pd-0",
                "tidb-tidb-0",
                "tidb-tikv-0"
            ]

        4. 输出的结果格式参考：
        [
            {
                "component": servicename,
                "reason": "the possible reason"
            },
        ...
        ]
        
        5. 根因组件可能时service，node或者pod。

        ## 举例1：
        {'k8_namespace': 'hipstershop', '@timestamp': '2025-06-07t05:27:27.313z', 'agent_name': 'filebeat-filebeat-z7ss4', 'k8_pod': 'frontend-0', 'message': '{"error":"could not retrieve product: rpc error: code = unavailable desc = transport is closing","http.req.id":"f6fe11f0-3bfd-4108-89ba-967d39cd487f","http.req.method":"post","http.req.path":"/cart","message":"request error","session":"f9a46b32-7496-4f57-9772-c0cb93cc9d47","severity":"error","timestamp":"2025-06-07t05:27:27.311594826z"}', 'k8_node_name': 'aiops-k8s-03'}
        该日志为frontend-0，frontend相关的服务，不会是根因，需要查看日志，排查导致frontend服务报错的后端服务
        该日志指向productcatalogservice
        
        输出的原因为：

        [
            {
                "component": "productcatalogservice",
                "reason": "service rpc error",
                "next step": "check the  productcatalogservice pod or node"
            },
        ...
        ]
        
        ## 举例2：
        {'k8_namespace': 'hipstershop', '@timestamp': '2025-06-07t05:17:01.569z', 'agent_name': 'filebeat-filebeat-hdb2p', 'k8_pod': 'frontend-1', 'message': '{"error":"failed to get product recommendations: failed to get recommended product info (#ls4psxunum): rpc error: code = canceled desc = context canceled","http.req.id":"f8b2e403-d9c1-4737-912a-565ca98b5e3c","http.req.method":"get","http.req.path":"/cart","message":"request error","session":"92ea8f60-31ef-439f-b80d-4594c6c832b4","severity":"error","timestamp":"2025-06-07t05:17:01.568894527z"}', 'k8_node_name': 'aiops-k8s-07'}
        该日志为frontend-0，frontend相关的服务，不会是根因，需要查看日志，排查导致frontend服务报错的后端服务
        该日志指向recommendationservice
        
        输出的原因为：

        [
            {
                "component": "recommendationservice",
                "reason": "service rpc error",
                "next step": "check the recommendationservice pod or node"
            },
        ...
        ]

        ## 注意:
        1. 根因不会是k8s-master1，k8s-master2、k8s-master3
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )
    
    trace_agent = AssistantAgent(
        name="trace_assistant",
        description="trace故障检测助手",
        model_client=model_client,
        tools=[check_trace_tool],
        system_message="""你是一个调用链检测助手，需要根据开始时间，结束时间，检查时间范围内的trace是否有异常,完成以下任务：
        1. 输出结构化数据，包括故障组件，观察现象，故障原因
        2. 若未发现异常，明确返回"未检测到异常trace信息"
        3. 通常front作为client，不会是根因，需要查看front访问的目标端
        4. 输出的格式：
        [
            {
                "component": "productcatalogservice",
                "reason": "grpc status code 4, DeadlineExceeded",
                "next step": "check the  productcatalogservice pod or node"
            },
            ...
        ]   
        5. 组件是service，或者pod，需要判断component是服务还是pod，举例： 如果只有adservice-0报错，通常是pod问题，问题组件时adservice-0，如果同时adservice-0，adservice-1，adservice-2报错，通常是service报错，问题组件时adservice
            all_service = [
            "adservice",
            "cartservice",
            "currencyservice",
            "productcatalogservice",
            "checkoutservice",
            "recommendationservice",
            "shippingservice",
            "emailservice",
            "paymentservice",
            "tidb-pd",
            "tidb-tidb",
            "tidb-tikv",
            "cart-redis"
            ],
            all_pod = [
                "adservice-0",
                "adservice-1",
                "adservice-2",
                "cartservice-0",
                "cartservice-1",
                "cartservice-2",
                "currencyservice-0",
                "currencyservice-1",
                "currencyservice-2",
                "productcatalogservice-0",
                "productcatalogservice-1",
                "productcatalogservice-2",
                "checkoutservice-0",
                "checkoutservice-1",
                "checkoutservice-2",
                "recommendationservice-0",
                "recommendationservice-1",
                "recommendationservice-2",
                "shippingservice-0",
                "shippingservice-1",
                "shippingservice-2",
                "emailservice-0",
                "emailservice-1",
                "emailservice-2",
                "paymentservice-0",
                "paymentservice-1",
                "paymentservice-2",
                "tidb-pd-0",
                "tidb-tidb-0",
                "tidb-tikv-0"
            ]

        ## 举例1：
        {'key': 'frontend -> adservice', 'status_code': '4', 'message': 'context deadline exceeded', 'traceId': None, 'spanId': None}
        1. 异常识别：当接收到上述格式的数据时，模型应能立即识别出这是一条异常记录。
        2. 状态码分析：
           核心功能：特别关注 "status_code" 字段。模型应能理解 4 代表 "context deadline exceeded"。请指导模型将此状态码与超时（Timeout）或上下文取消（Context Cancelled）这类问题关联起来。
           通用分析：模型需要能够解析其他常见的 gRPC 状态码（例如 2 代表 UNKNOWN，14 代表 UNAVAILABLE 等），并给出相应的解释。
        3. 根因推测：根据 "key" 和 "message"，模型应能推测出异常的直接原因。例如，对于 "key": "frontend -> adservice" 和 "message": "context deadline exceeded"，模型应能推断出：
           frontend 服务在调用 adservice 服务时发生了超时。
           可能的原因是 adservice 服务响应过慢或不可用。
        4. 输出：
        [
            {
                "component": "adservice",
                "reason": "adservice unavailable",
                "next step": "check the adservice pod or node"
            },   
        ]

        ## 注意:
        1. 根因不会是k8s-master1，k8s-master2、k8s-master3

        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )
    
    metric_tidb_agent = AssistantAgent(
        name="metric_tidb_assistant",
        description="tidb故障检测助手",
        model_client=model_client,
        tools=[check_metric_tidb_tool,check_metric_tikv_tool,check_metric_pd_tool, \
               check_tidb_interrupt_tool,check_tikv_interrupt_tool,check_pd_interrupt_tool
                ],
        system_message="""你是一个tidb的检测助手，需要根据开始时间，结束时间，检查时间范围内的tidb的组件是否有异常，完成以下任务：
        1. 输出结构化数据，包括故障组件，组件包括tidb、pd、tikv，观察现象，故障原因
        2. 若未发现异常，明确返回"未检测到异常tidb、pd、tikv信息"
        3. 检查tidb，使用`check_metric_tidb_tool`和`check_tidb_interrupt_tool`,check_tidb_interrupt_tool默认使用300s进行判断
        4. 检查tikv，使用`check_metric_tikv_tool`和`check_tikv_interrupt_tool`，check_tikv_interrupt_tool默认使用300s进行判断
        5. 检查pd，使用`check_metric_pd_tool`和`check_pd_interrupt_tool`，check_pd_interrupt_tool默认使用300s进行判断
        6. check_tidb_interrupt_tool、check_tikv_interrupt_tool和check_pd_interrupt_tool如果interrupt_count大于1，说明tidb-tidb，tidb-tikv，tidb-pd组件异常
        - 正常现象：
        [{"object": "tidb_tidb", "interrupt_count": 0}]  
        说明tidb_tidb在故障期间没有发生重启
        - 异常现象：
        [{"object": "tidb_tidb", "interrupt_count": 1}]  
        说明tidb_tidb在故障期间发生重启
        输出如下：
        {
            "component": "tidb_tidb",
            "reason": "tidb_tidb failure"
        }
        
        ## tidb检查规则：
        1. server_is_up 小于1，tidb服务故障，可以结合uptime进行验证
        2. transaction_retry是事务请求次数，failed_query_ops是失败请求数，大于0，说明请求出现报错
        3. memory_usage，cpu_usage反映了tidb的使用情况
        4. tidb_tidb的interrupt大于1，故障原因： tidb_tidb failure

        ## tikv检查规则：
        1. io_util的单位是%,最大值是1，反应tikv的磁盘繁忙，需要关注io_util是否存在1的情况，如果存在，说明某段时间内IO的使用率繁忙
        2. cpu_usage和memory_usage反应了tikv的计算使用情况
        3. server_is_up小于1，tikv服务出现异常
        4. store_size/capacity_size反应了存储使用率
        5. tidb_tikv的interrupt大于1，故障原因： tidb_tidb failure
        
        ## pd检查规则：
        1. store_down_count，store_unhealth_count，store_low_space_count，store_slow_count，abnormal_region_count大于0，pd异常
        2. storage_used_ratio大于90，pd存储容量异常
        3. cpu_usage，memory_usage反映了pd的计算使用情况
        4. tidb_pd的interrupt大于1，故障原因： tidb_tidb failure

        ## 已知可以忽略的问题：
        1、 'component': 'pd'，miss-peer-region-count>0，这个问题可以忽略。
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )
    
    metric_infra_node_agent = AssistantAgent(
        name="metric_infra_node_assistant",
        description="infra node故障检测助手",
        model_client=model_client,
        tools=[check_metric_infra_node_tool,check_node_cpu_rate_tool,check_node_filesystem_usage_rate_tool,check_node_io_util_tool],
        system_message="""你是一个infra node检测助手，需要根据开始时间，结束时间，检查时间范围内的node是否有异常，完成以下任务
        1. 输出结构化数据，包括故障组件，观察现象，故障原因
        2. 若未发现异常，明确返回"未检测到异常node信息"
        3. 数值提取的是float类型的数字
        4. 问题组件必须在以下列表内
            all_node = [
                "aiops-k8s-01",
                "aiops-k8s-02",
                "aiops-k8s-03",
                "aiops-k8s-04",
                "aiops-k8s-05",
                "aiops-k8s-06",
                "aiops-k8s-07",
                "aiops-k8s-08"
            ]
        5. 调用工具`check_node_cpu_rate_tool`，检查node的CPU峰值，如果超过90，故障原因是cpu usage overhead
        6. 调用工具`check_node_filesystem_usage_rate_tool`,检查node的磁盘峰值，如果超过90，故障原因是filesystem usage overhead
        7. 调用工具`check_node_io_util_tool`,需要输入参数，默认检查的是0.9，如果超过0.9，说明磁盘的IO繁忙率高，故障原因是disk IO overhead
           返回说明：
           ["node节点aiops-k8s-03磁盘使用率超过90%，可能有影响的pod包括：['adservice-0', 'cartservice-2', 'frontend-0', 'paymentservice-1', 'recommendationservice-0']"] 
           此时aiops-k8s-03的峰值默认超过0.9，可能影响的pod包括：'adservice-0', 'cartservice-2', 'frontend-0', 'paymentservice-1', 'recommendationservice-0'
       
        **故障判断方法**
        1. node的node_cpu_usage_rate，node_memory_usage_rate，node_filesystem_usage_rate单位都是%，可以通过阈值判断
        2. node_cpu_usage_rate超过80，判断有故障，故障原因：CPU high overload
        3. node_memory_usage_rate超过90，判断有故障，故障原因：Memory high overload
        4. node_filesystem_usage_rate需要按照device进行分类，超过90, 判断有严重故障，故障原因：Filesystem High Usage
        5. 严格按照提取的数字进行判断，如node_cpu_usage_rate为90，大于80，判断有故障
        6. 如果最大值出现峰值，如1，100，需要重点关注，出现故障的概率极高
        7. 同时存在CPU，内存或者磁盘故障时，磁盘使用率故障的情况更严重
        
        ## 正常情况
        {'entity_type': 'node', 'entity_name': 'node_cpu_usage_rate', 'component': array(['aiops-k8s-08'], dtype=object), 'unit': '%', 'kpi': 'node_cpu_usage_rate', 'field': 'node_cpu_usage_rate', 'labels': {'instance': '223.193.36.130', 'kpi_name': 'CPU使用率', 'kubernetes_node': 'aiops-k8s-08', 'object_type': 'node'}, 'mean': 29.03714285714286, 'p95': 33.06, 'start_time': '2025-06-05T23:10:13Z', 'end_time': '2025-06-05T23:24:13Z'} 
        组件：aiops-k8s-08
        指标：node_cpu_usage_rate
        平均值：29.03714285714286
        p95：33.06

        aiops-k8s-08的cpu使用率29.03，单位是%，没有超过80%，正常
        
        ## 异常情况
        {'entity_type': 'node', 'entity_name': 'node_cpu_usage_rate', 'component': array(['aiops-k8s-08'], dtype=object), 'unit': '%', 'kpi': 'node_cpu_usage_rate', 'field': 'node_cpu_usage_rate', 'labels': {'instance': '223.193.36.130', 'kpi_name': 'CPU使用率', 'kubernetes_node': 'aiops-k8s-08', 'object_type': 'node'}, 'mean': 90.87, 'p95': 33.06, 'start_time': '2025-06-05T23:10:13Z', 'end_time': '2025-06-05T23:24:13Z'} 
        组件：aiops-k8s-08
        指标：node_cpu_usage_rate
        平均值：90.87
        p95：33.06

        aiops-k8s-08的cpu使用率90.87，单位是%，超过80，CPU high overload
        
        ## 注意:
        1. 根因不会是k8s-master1，k8s-master2、k8s-master3

        **输出格式**
        [{
            component: aiops-k8s-08
            problem: 'CPU high overload'
        }]
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )
    
    metric_infra_pod_agent = AssistantAgent(
        name="metric_infra_pod_assistant",
        description="infra pod故障检测助手",
        model_client=model_client,
        tools=[check_metric_infra_pod_tool,\
               check_metric_pod_restart_tool,\
               check_metric_pod_process_abnormal_tool,\
               check_pod_network_drop_to_zero_tool 
               ],
        system_message="""你是一个infra pod检测助手，需要根据开始时间，结束时间，检查时间范围内的pod是否有异常，完成以下任务
        1. 输出结构化数据，包括故障组件，观察现象，故障原因
        2. 若未发现异常，明确返回"未检测到异常pod信息"
        3. 数值提取的是float类型的数字
        4. 使用工具`check_metric_infra_pod_tool`，获取pod的指标数据。
        5. 使用工具`check_metric_pod_restart_tool`,获取pod是否发生重启。
        6. 使用工具`check_metric_pod_process_abnormal_tool`，获取pod是否有异常进程。
        7. 使用工具`check_pod_network_drop_to_zero_tool`，支持自定义时间，在时间范围内，流量持续为0的异常情况。
       
        ## 使用工具： check_metric_infra_pod_tool
        **故障判断方法**
        1. pod的pod_processes不为1，通常认为pod发生异常
        2. pod_cpu_usage表示pod的CPU使用量，单位是core
        3. pod_memory_working_set_bytes表示pod的内存使用大小，单位是bytes
        4. 严格按照提取的数字进行判断
        5. 如果最大值出现1或者100，需要重点关注，出现故障的概率极高
        
        ### 正常情况
        {'entity_type': 'pod', 'entity_name': 'pod_network_transmit_packets', 'component': array(['shippingservice-2'], dtype=object), 'unit': 'packets/s', 'kpi': 'pod_network_transmit_packets', 'field': 'pod_network_transmit_packets', 'labels': {'instance': 'aiops-k8s-08', 'kpi_name': '传输数据包的累积计数', 'namespace': 'hipstershop', 'pod': 'shippingservice-2', 'object_type': 'pod'}, 'mean': 0.7021428571428572, 'p95': 7.866999999999999, 'start_time': '2025-06-05T23:10:13Z', 'end_time': '2025-06-05T23:24:13Z'}
        组件：shippingservice-2
        指标：pod_network_transmit_packets
        平均值：0.7021428571428572
        p95：7.866999999999999

        shippingservice-2的传输数据包的累积计数是0.7，单位是packets/s，没有超过p95，正常
        
        ### 异常情况
        {'entity_type': 'pod', 'entity_name': 'pod_network_transmit_packets', 'component': array(['shippingservice-2'], dtype=object), 'unit': 'packets/s', 'kpi': 'pod_network_transmit_packets', 'field': 'pod_network_transmit_packets', 'labels': {'instance': 'aiops-k8s-08', 'kpi_name': '传输数据包的累积计数', 'namespace': 'hipstershop', 'pod': 'shippingservice-2', 'object_type': 'pod'}, 'mean': 9.7021428571428572, 'p95': 7.866999999999999, 'start_time': '2025-06-05T23:10:13Z', 'end_time': '2025-06-05T23:24:13Z'}
        组件：shippingservice-2
        指标：pod_network_transmit_packets
        平均值：9.7021428571428572
        p95：7.866999999999999

        shippingservice-2的传输数据包的累积计数是9.7，单位是packets/s，没有超过p95，正常

        ## 使用工具： check_metric_pod_restart_tool
        1. 如果instance_count的值大于2，说明pod切换过worker节点，发生过重启

        ### 正常情况：
        {"pod":"adservice-0","instance_count":1}
        说明adservice-0没有发生切换

        ### 异常情况：
        {"pod":"frontend-2","instance_count":2}
        说明frontend-2发生切换，pod kill

        **输出格式**
        {
            component: shippingservice-2
            problem: 'network transmit packets overload'
        }

        ## 使用工具： check_metric_pod_process_abnormal_tool
        1. 如果pod_processes_avg的值大于2，说明pod进程大于2，故障原因pod failure

        ### 正常情况：
        {'pod': 'shippingservice-0', 'pod_processes_avg': 1}
        说明shippingservice-0运行正常

        ### 异常情况：
        {'pod': 'currencyservice-0', 'pod_processes_avg': 8}
        {'pod': 'currencyservice-1', 'pod_processes_avg': 8}
        {'pod': 'currencyservice-2', 'pod_processes_avg': 8}
        {'pod': 'redis-cart-0', 'pod_processes_avg': 2}
        同时存在currencyservice-0，currencyservice-1,currencyservice-2，redis-cart-0指标异常。
        但是currencyservice指标远高于2，切同时currencyservice异常，判断发生服务故障，故障组件为currencyservice

        ## 使用工具： check_pod_network_drop_to_zero_tool

        ### 正常情况：
        返回数据为： []，说明没有pod存在网络流量掉0的情况

        ### 异常情况：
        [{'pod': 'cartservice-1', 'zero_periods': [{'start': '2025-06-12T01:12:00', 'end': '2025-06-12T01:29:00', 'duration': 1020.0}], 'zero_interrupt_count': 1}]
        pod实例cartservice-1，在2025-06-12T01:12:00到2025-06-12T01:29:00时间范围内，流量变为0，持续时间1020s。该现象的可能原因是network limit，网络存在限流的情况。
        
        ### 注意:
        1. 根因不会是k8s-master1，k8s-master2、k8s-master3

        **输出格式**
        {
            component: currencyservice
            problem: 'pod process failure'
        }
        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )

    metric_apm_agent = AssistantAgent(
        name="metric_apm_assistant",
        description="apm故障检测助手",
        model_client=model_client,
        tools=[check_metric_apm_pod_history_tool,check_metric_apm_service_history_tool,check_trace_duration_tool],
        system_message="""你是一个apm检测助手，需要根据开始时间，结束时间，检查时间范围内的apm是否有异常，完成以下任务
        1. 输出结构化数据，包括故障组件，观察现象，故障原因
        2. 若未发现异常，明确返回"未检测到异常apm信息"
        3. frontendservice如果出现apm指标异常，通常不是根因，是因为后端服务异常导致的frontend服务异常
        4. 各apm指标：
            {"key": "client_error_ratio", "unit": "%"}，大于10，判断异常比例较高，需要重点排查
            {"key": "error_ratio", "unit": "%"}，大于10，判断异常比例较高，需要重点排查
            {"key": "server_error_ratio", "unit": "%"},大于10，判断异常比例较高，需要重点排查
            {"key": "timeout", "unit": "count"},平均值大于p95，判断超时较多，需要重点排查
            {"key": "rrt", "unit": "ms"},平均值大于100，判断延时较高，需要重点排查
            {"key": "rrt_max", "unit": "ms"}
        5. 使用`check_trace_duration_tool`，结合apm指标，判断延时是否有大的情况，一般超过1000ms，说明接口调用比较慢。如果多个组件同时存在延时增大的情况，需要关注底层情况。
        6. 使用`check_metric_apm_pod_history_tool`，分析service的运行数据和历史峰值的关系。
           使用说明：check_metric_apm_service_history_tool返回举例：
           {'entity_type': 'apmservice', 'entity_name': 'emailservice', 'component': None, 'unit': 'ms', 'kpi': 'rrt', 'field': None, 'labels': None, 'mean': np.float64(21.634825714285718), 'p95': 2.421153, 'max': 31.7404, 'min': 6.818029999999999, 'z_zero': -2.680170237734552, 'start_time': '2025-06-05T16:10:02Z', 'end_time': '2025-06-05T16:31:02Z', 'baseline_ratio': 2.9803114253243406}
           服务emailservice的指标rrt比历史均值超过2.98倍
           服务productcatalogservice的指标rrt比历史均值超过1.006倍
        7. 使用`check_metric_apm_service_history_tool`，分析pod的运行数据和历史峰值的关系。
           使用说明：check_metric_apm_pod_history_tool返回举例：
           {'entity_type': 'apmpod', 'entity_name': 'emailservice-0', 'component': None, 'unit': 'ms', 'kpi': 'rrt', 'field': None, 'labels': None, 'mean': np.float64(19.94858095238095), 'p95': 2.6887994999999996, 'max': 51.54367, 'min': 1.78017, 'z_zero': -1.4343548708672786, 'start_time': '2025-06-05T16:10:02Z', 'end_time': '2025-06-05T16:31:02Z', 'baseline_ratio': 1.5879467424780858}
           {'entity_type': 'apmpod', 'entity_name': 'emailservice-1', 'component': None, 'unit': 'ms', 'kpi': 'rrt', 'field': None, 'labels': None, 'mean': np.float64(21.13009), 'p95': 2.737, 'max': 40.07833, 'min': 1.807, 'z_zero': -1.812760729085608, 'start_time': '2025-06-05T16:10:02Z', 'end_time': '2025-06-05T16:31:02Z', 'baseline_ratio': 2.7518799440509896}
           {'entity_type': 'apmpod', 'entity_name': 'emailservice-2', 'component': None, 'unit': 'ms', 'kpi': 'rrt', 'field': None, 'labels': None, 'mean': np.float64(24.08291238095238), 'p95': 2.80183, 'max': 39.00562, 'min': 7.309729999999999, 'z_zero': -2.3278922789093603, 'start_time': '2025-06-05T16:10:02Z', 'end_time': '2025-06-05T16:31:02Z', 'baseline_ratio': 2.6133986004547265}
            pod实例emailservice-0/1/2的指标rrt比历史均值分别超过1.58，2.75，2.61
            pod实例productcatalogservice-1的指标rrt比历史均值超过1.02倍
            综合6，7工具的使用分析，服务emailservice rrt发生故障，故障指标rrt
            
        ### 异常例子1：
        {'entity_type': 'apmpod', 'entity_name': 'shippingservice-2', 'component': None, 'unit': '%', 'kpi': 'client_error_ratio', 'field': None, 'labels': None, 'mean': np.float64(9.473846153846154), 'p95': 0.0, 'max': 63.16, 'min': 0.0, 'z_zero': -0.40951397308168197, 'start_time': '2025-06-05T21:10:10Z', 'end_time': '2025-06-05T21:23:10Z'}
        pod名称： shippingservice-2
        指标名称： client_error_ratio
        最大值： 63.16
        说明shippingservice-2在这段时间内有异常。
        输出：
        {
            component: 'shippingservice-2'
            problem: 'client_error_ratio'
        }

        ### 异常例子2：
        {'entity_type': 'apmservice', 'entity_name': 'recommendationservice', 'component': None, 'unit': 'ms', 'kpi': 'rrt', 'field': None, 'labels': None, 'mean': np.float64(217.57741823529412), 'p95': 4.348033999999999, 'max': 227.07608, 'min': 141.12785, 'z_zero': -10.962570566320995, 'start_time': '2025-06-07T08:10:37Z', 'end_time': '2025-06-07T08:27:37Z'}
        {'entity_type': 'apmpod', 'entity_name': 'recommendationservice-0', 'component': None, 'unit': 'ms', 'kpi': 'rrt', 'field': None, 'labels': None, 'mean': np.float64(213.36540705882354), 'p95': 6.99388, 'max': 222.53589000000002, 'min': 133.56849, 'z_zero': -10.273124474149297, 'start_time': '2025-06-07T08:10:37Z', 'end_time': '2025-06-07T08:27:37Z'}
        {'entity_type': 'apmpod', 'entity_name': 'recommendationservice-2', 'component': None, 'unit': 'ms', 'kpi': 'rrt', 'field': None, 'labels': None, 'mean': np.float64(221.05398941176472), 'p95': 4.3715, 'max': 231.21717, 'min': 147.58096, 'z_zero': -11.4306736516911, 'start_time': '2025-06-07T08:10:37Z', 'end_time': '2025-06-07T08:27:37Z'}
        
        指标名称： rrt
        recommendationservice、recommendationservice-0、recommendationservice-2的rrt is much bigger than p95
        说明问题在服务层，因此根因是recommendationservice
        输出：
        {
            component: 'shippingservice-2'
            problem: 'rrt,rrt_max'
        }

        ### 注意:
        1. 根因不会是k8s-master1，k8s-master2、k8s-master3

        """,
        reflect_on_tool_use=True,
        model_client_stream=False
    )
    
    # 提炼不同助手的答案，并且返回需要的结果
    analyze_agent = AssistantAgent(
        name="analyze_assistant",
        description="总结分析助手",
        model_client=model_client,
        system_message="""你作为根因分析总结专家。遵守以下规范：
            1. 总结各个agent的结果，并且对结果进行汇总，输出为英文。
            2. 输出必须是严格遵循以下格式，不能添加其他字段，并且可以给json.loads直接解析：
            {
              "uuid": "随机8位十六进制字符串（如'a1b2c3d4'）",
              "component": "输入中确认存在的故障组件",
              "reason": "资源类型+异常类型（如'disk IO overload'）",
              "reasoning_trace": [
                {
                  "step": 序号（从1开始）,
                  "action": "动词短语（如'LoadMetrics'）",
                  "observation": "量化现象≤20字（如'rps>500'）"
                }
              ]
            }
            3. uuid必须是info-agent提取的uuid，格式用正则表达式过滤： [a-f0-9]{8}-[a-f0-9]{2,3}
            4. 如果某个agent返回判断正常，或者返回为空，在reasoning_trace内不进行记录
            5. component只有一个，并且必须从以下范围内选择:
            (1) node问题： aiops-k8s-01,aiops-k8s-02,aiops-k8s-03,aiops-k8s-04,aiops-k8s-05,aiops-k8s-06,aiops-k8s-07,aiops-k8s-08
            (2) service问题： adservice,cartservice,checkoutservice,currencyservice,emailservice,paymentservice,productcatalogservice,recommendationservice,cart-redis,shippingservice
            (3) pod问题： adservice-0,adservice-1,adservice-2,cartservice-0,cartservice-1,cartservice-2,checkoutservice-0,checkoutservice-1,checkoutservice-2,currencyservice-0,currencyservice-1,currencyservice-2,emailservice-0,emailservice-1,emailservice-2,frontend-0,frontend-1,frontend-2,paymentservice-0,paymentservice-1,paymentservice-2,productcatalogservice-0,productcatalogservice-1,productcatalogservice-2,recommendationservice-0,recommendationservice-1,recommendationservice-2,redis-cart-0,shippingservice-0,shippingservice-1,shippingservice-2
            (4) 数据库问题： tidb-tidb，tidb-tikv，tidb-pd

            6. 问题排查思路：
            以下场景可以直接定位根因：
            (1) node的文件使用率高，如aiops-k8s-08发生disk space overhead，判定根因组件为aiops-k8s-08，根因原因是disk space overhead
            (2) pod发生kill情况，如adservice-0的instance_count大于1，判定根因组件是adservice-0，根因原因是pod kill
            (3) 多个pod发生kill情况，且都属于一个服务，如adservice-0，adservice-1，adservice-2的instance_count大于1，判定根因组件是adservice，原因是pod kill
            (4) pod发生process number异常的情况，不考虑redis-cart-0的情况，如emailservice-0的pod_processes_avg大于1，判定根因组件是emailservice-0，原因是pod failure
            (5) 多个pod发生process number异常，且都属于一个服务，如emailservice-0，emailservice-1，emailservice-2的pod_processes_avg大于1，判定根因组件是emailservice，原因是pod failure
            (6) tidb-tidb的interrupt_count大于1，故障组件tidb-tidb，故障原因tidb-tidb failure
            (7) tidb-pd的interrupt_count大于1，故障组件tidb-pd，故障原因tidb-pd failure
            (8) tidb-tikv的interrupt_count大于1，故障组件tidb-tikv，故障原因tidb-tikv failure
            其他的排查顺序可以按照以下规则，选出概率最高的问题组件：
            (1) 通常情况，一般层级越低，问题组件的概率越高，如cartservice和redis-cart同时出问题，一般是redis-cart出问题
            (2) 如果apm的指标内，有明显pod或者service的报错，一般是根因。如果apm的service，pod同时远超过基线，对应的service是根因，如果只有1个pod远超过基线，对应的pod是根因
            (3) 如果log，trace可以同时定位component，问题为log，trace定位的component
            (4) 如果node出现CPU使用率高，会存在性能影响，一般是根因，cpu usage overhead
            (5) 如果node出现IO使用率高，会存在性能影响，一般根因是disk io overhead，但是根因组件需要结合apm等数据进行判断
            (5) 出现多个component的情况，其无法按照组件原因进行根因定位，按照各agent发现的故障组件出现次数进行统计，出现次数最多的是根因组件

            7. 调用链路径：
            系统基于 Google 开源的微服务示例系统 HipsterShop，整体由多个使用不同编程语言实现的微服务组成。上图中的矩形框表示各个微服务，用户通过前端服务（Frontend）访问整个系统。每个微服务对应一个容器级别的监控系统，同时部署这些服务的虚拟机也采集了主机级别的运行数据。
            系统采用动态部署架构，共包含 10 个核心微服务和 8 台虚拟机。每个微服务部署了 3 个 Pod，总计 30 个 Pod，这些 Pod 会被动态调度分布到 8 台虚拟机上。此外，系统中的 TiDB 组件也部署在虚拟机上，包括 tidb-tidb、tidb-pd 和 tidb-tikv 三个核心服务，每个服务部署 1 个 Pod。
            为构造多样化的故障场景，系统支持三种层级的故障注入：service 级、pod 级和 node 级。其中，service 和 pod 层级的故障主要模拟 Kubernetes 容器层面的异常行为，Service 级故障表示该服务下的所有 3 个 Pod 同时注入故障；Pod 级故障仅对某一个具体的 Pod 注入故障。
            这张架构图核心是一条“从用户发起请求，经由 Frontend 再分发到各个微服务，最后再落库／发起下游调用”的调用链，我们可以这样把它拆解成几个步骤来看：

            （1） 用户 → Frontend： 所有流量都由 Frontend 接入，用户在页面上的每一次点击、每一次操作，Front end 都负责路由到下面的服务。
            （2） Frontend → 核心查询服务
            - ProductCatalog（商品目录）：用于查询商品的基本信息（名称、价格、库存等）。
            - Recommendation（推荐）：先调用商品目录服务，拉取商品信息后再给用户做个性化推荐。
            - Ad（广告）：直接查询底层数据库（TiDB）以获取投放的广告内容。
            - Currency（汇率）：实时获取不同货币的汇率，用于商品价格或订单金额的多币种显示。
            - Shipping（运费）：计算并返回当前地址/商品组合下的运费。
            - Cart（购物车）：将用户的选购行为写入 Redis Cache，以保证高并发下的快速读写。
            （3） Fallback 存储 → TiDB + Redis
            - TiDB 集群：（内部包含 tidb-tidb,tidb-tikv,tidb-pd）：
              商品目录（ProductCatalog）和广告（Ad）服务都从这里读取核心数据。
            - Redis Cache：
              购物车（Cart）服务将用户的临时选购状态存入 Redis，以便快速响应和后续结算。
            （4）下单流程：Frontend → Checkout
            - 用户点击“去结算”后，Frontend 调用 **Checkout** 服务，后者负责整个下单编排。
            （5）Checkout → 支付 & 发货 & 通知
            - Payment（支付）：发起真正的支付请求（第三方支付网关）。
            - Currency（汇率）：再次确认订单金额的币种转换。
            - Shipping（运费）：确认最终的运费计算。
            - Email（邮件）：下单成功后给用户发送订单确认邮件。
            
            ---
            
            8. 简要流程图
            
            ```
            User
              ↓
            Frontend ──▶ Ad ──────────▶ TiDB
               │
               ├─▶ Recommendation ──▶ ProductCatalog ──▶ TiDB
               │
               ├─▶ ProductCatalog ──▶ TiDB
               │
               ├─▶ Currency
               │
               ├─▶ Shipping
               │
               ├─▶ Cart ──▶ Redis Cache
               │
               └─▶ Checkout ──▶ { Payment, Currency, Shipping, Email }
            ```
            
            9. Diagram 要点
            
            (1) **Fan‑out 模式**：Frontend 一次请求可以并发调用多个微服务。
            (2) **服务依赖**：Recommendation→ProductCatalog; Ad/ProductCatalog→TiDB。
            (3) **状态存储**：购物车走 Redis，核心业务数据存 TiDB。
            (4) **结算编排**：Checkout 汇聚了支付、运费、汇率、邮件通知四个子服务。
            
            10. 以下指标发生告警时，不是根因：
            （1） k8s-master1，k8s-master2，k8s-master3不会是根因
            （2） frontend的服务延时高，不会是根因

            11. 注意:
            1. 根因不会是k8s-master1，k8s-master2、k8s-master3
            
            """,
        reflect_on_tool_use=True,
        model_client_stream=False,  
    )

    analyze_results = {}
    termination = TextMentionTermination("TERMINATE")
    team = RoundRobinGroupChat(
        [info_agent,metric_apm_agent,trace_agent,metric_infra_node_agent,metric_infra_pod_agent,metric_tidb_agent,log_agent,analyze_agent], 
        termination_condition=termination, 
        max_turns=8
        )

    stream = team.run_stream(task=task)
    # console = Console(stream)  # Console 会自动消费 stream 并展示
    async for message in stream:
        # Console(stream) 
        # 所以我们需要自定义流式处理，既打印也保存。
        print(message)
        # result = extract_json_from_content(message.content)
        # print("标准化输出：")
        # if result is None:
        #     print("无法解析为JSON，原始内容：", message.content)
        # elif isinstance(result, dict):
        #     for k, v in result.items():
        #         print(f"{k}: {v}")
        # elif isinstance(result, list):
        #     for idx, item in enumerate(result):
        #         print(f"第{idx+1}项：")
        #         if isinstance(item, dict):
        #             for k, v in item.items():
        #                 print(f"  {k}: {v}")
        #         else:
        #             print(f"  {item}")
        # else:
        #     print(result)

        # 这里之所以要解析两次，是因为stream中的message有两种结构：
        # 1. message 直接有 source 属性且为 "analyze_assistant"，这时 message.content 就是分析结果。
        # 2. message 可能有 messages 属性（如消息聚合或多轮对话），里面每个 msg 也可能是 analyze_assistant 的结果。
        # 所以需要分别判断 message 和 message.messages 里的内容，避免漏掉任何 analyze_assistant 的输出。

        if hasattr(message, "source") and message.source == "analyze_assistant":
            result = extract_json_from_content(message.content)
            if result and "uuid" in result:
                analyze_results[result["uuid"]] = result
                print("analyze_agent 结果已保存:", result)
            else:
                print("解析analyze_agent结果失败: 未能提取有效JSON或缺少uuid")
        if hasattr(message, "messages"):
            for msg in message.messages:
                if hasattr(msg, "source") and msg.source == "analyze_assistant":
                    result = extract_json_from_content(msg.content)
                    if result and "uuid" in result:
                        analyze_results = result
                        print("analyze_agent 结果已保存:", result)
                    else:
                        print("解析analyze_agent结果失败: 未能提取有效JSON或缺少uuid")

    await model_client.close()
    print("所有analyze_agent结果:", analyze_results)
    return analyze_results

def run_sync(task):
    # 由于run_and_collect是async函数，这里需要在子进程中调用asyncio.run
    import asyncio
    return asyncio.run(run_and_collect(task=task))
    
if __name__ == "__main__":
    
    # import time
    # import json
    # total_result = []

    # task = str({
    #     "Anomaly Description": "The system experienced an anomaly from 2025-06-05T16:10:02Z to 2025-06-05T16:31:02Z. Please infer the possible cause.",
    #     "uuid": "345fbe93-80"
    # })

    # start_time = time.time()
    # result = asyncio.run(run_and_collect(task=task))
    # total_result.append(result)
    # print(f"{json.dumps(result)}")
    # end_time = time.time()
    # cost_time = end_time - start_time
    # print(f"本次任务完成，共花费时间{cost_time}s")

    # with open('result.txt','a+',encoding="UTF-8") as f:
    #     f.write(json.dumps(total_result, ensure_ascii=False))
    
    import time
    import json
    import asyncio
    from concurrent.futures import ProcessPoolExecutor
    max_workers = 5

    program_start_time = time.time()
    total_result = []

    with open('input-reload.json', 'r', encoding="UTF-8") as f:
        data = json.load(f)

    tasks = [json.dumps(item, ensure_ascii=False, indent=2) for item in data]

    # 使用进程池并发，每次最多3个进程
    with ProcessPoolExecutor(max_workers=max_workers) as executor, open('result.txt', 'a+', encoding="UTF-8") as result_file:
        futures = []
        for i, task in enumerate(tasks):
            start_time = time.time()
            future = executor.submit(run_sync, task)
            futures.append((future, start_time))

            # 控制并发数为6，或者最后一批任务
            if len(futures) == max_workers or i == len(tasks) - 1:
                for fut, st in futures:
                    result = fut.result()
                    # 每完成一个任务就立即写入文件，不需要等全部任务结束
                    result_file.write(json.dumps(result, ensure_ascii=False))
                    result_file.write('\n')
                    total_result.append(result)
                    end_time = time.time()
                    cost_time = end_time - st
                    print(f"本次任务完成，共花费时间{cost_time}s")
                    time.sleep(60)
                futures = []
    
    program_end_time = time.time()
    program_use_time = program_start_time - program_end_time
    print(f"程序共花费时间：{program_use_time}")